package bfmap

import (
	_ "embed"
	"fmt"
	"log/slog"
	"math/rand"
	"net/url"
	"path/filepath"
	"strings"

	"bfmap/config"

	"github.com/Unknwon/goconfig"
)

//go:embed version.txt
var Version string

func InitBfMapConfig() error {
	ini, err := goconfig.LoadConfigFile("setting.ini")
	if err != nil {
		return fmt.Errorf("can not read setting.ini, %w", err)
	}

	config.DbDir = ini.MustValue("database", "db-dir", "db")
	imageCacheSizeGB := ini.MustFloat64("database", "image-cache-size", 0)
	if imageCacheSizeGB < 0 {
		slog.Warn(
			"image cache size is negative, which is invalid. No cache size will be set",
			"image-cache-size",
			imageCacheSizeGB,
		)
	} else {
		config.ImageCacheSize = int64(imageCacheSizeGB * 1024 * 1024 * 1024)
	}

	config.DbDeletedDataTTL = ini.MustInt("database", "deleted-data-ttl", 0)

	config.WebDir = ini.MustValue("web", "www-dir", "web")
	config.HttpPort = ini.MustInt("web", "http-port", 2232)
	if config.HttpPort <= 0 {
		return fmt.Errorf("http port is invalid, %d", config.HttpPort)
	}
	config.HttpsPort = ini.MustInt("web", "https-port", 0)
	config.SSLKeyFileName = ini.MustValue("web", "ssl-key", "ssl.key")
	config.SSLCertFileName = ini.MustValue("web", "ssl-cert", "ssl.crt")
	config.SSLUpdateToken = ini.MustValue("web", "ssl-update-token", "t2bfdxnet2235")

	config.SSLKeyFilePath = filepath.Join(config.SSLPath, config.SSLKeyFileName)
	config.SSLCertFilePath = filepath.Join(config.SSLPath, config.SSLCertFileName)

	config.NatsServerPort = ini.MustInt("nats", "nats-port", 4222)
	config.NatsLeafNodePort = ini.MustInt("nats", "leaf-node-port", 0)
	config.NatsAuthToken = ini.MustValue("nats", "auth-token", "")
	config.NatsAsLeafNode = ini.MustBool("nats", "as-leaf-node", false)
	config.NatsLeafNodeServerUrl = ini.MustValue("nats", "nats-leaf-server-url", "")
	config.NatsLeafNodeServerAuthToken = ini.MustValue("nats", "leaf-server-auth-token", "")
	config.NatsRequestMapTile = ini.MustBool("nats", "nats-request-map-tile", true)
	config.NatsWaitTimeout = ini.MustInt("nats", "nats-wait-timeout", 3)

	config.IsLogToFile = ini.MustBool("log", "log-to-file", false)
	config.LogDir = ini.MustValue("log", "log-dir", "log")

	socks5Proxy := ini.MustValue("proxy", "socks5", "")
	if len(socks5Proxy) > 0 {
		if !strings.HasPrefix(socks5Proxy, "socks5://") {
			socks5Proxy = "socks5://" + socks5Proxy
		}
		config.Socks5Proxy, err = url.Parse(socks5Proxy)
		if err != nil {
			slog.Warn("parse socks5 proxy url failed, will not use socks5 proxy", "error", err)
		}
	}

	httpProxy := ini.MustValue("proxy", "http", "")
	if len(httpProxy) > 0 {
		if !strings.HasPrefix(httpProxy, "http://") {
			httpProxy = "http://" + httpProxy
		}
		config.HttpProxy, err = url.Parse(httpProxy)
		if err != nil {
			slog.Warn("parse http proxy url failed, will not use http proxy", "error", err)
		}
	}

	if config.HttpProxy != nil && config.Socks5Proxy != nil {
		slog.Info("both http proxy and socks5 proxy are set, will use http proxy")
	}

	// map
	config.DefaultMapStaticApiBaseUrl = ini.MustValue("map", "google-static-base-url", "https://maps.googleapis.com")
	config.DefaultMapTileApiBaseUrl = ini.MustValue("map", "google-tile-base-url", "https://tile.googleapis.com")
	config.DefaultOSMMapStaticApiBaseUrl = ini.MustValue("map", "osm-base-url", "https://tile.openstreetmap.org")
	defaultTiandituMapApiBaseUrl := fmt.Sprintf("https://t%d.tianditu.gov.cn", rand.Intn(8))
	config.DefaultTiandituMapStaticApiBaseUrl = ini.MustValue("map", "tianditu-base-url", defaultTiandituMapApiBaseUrl)

	if !config.IsVerboseDebug {
		return nil
	}

	// log config
	slog.Debug("config", "db-dir", config.DbDir)
	slog.Debug("config", "image-cache-size", config.ImageCacheSize)
	slog.Debug("config", "www-dir", config.WebDir)
	slog.Debug("config", "http-port", config.HttpPort)
	slog.Debug("config", "https-port", config.HttpsPort)
	slog.Debug("config", "ssl-key", config.SSLKeyFileName)
	slog.Debug("config", "ssl-cert", config.SSLCertFileName)
	slog.Debug("config", "ssl-update-token", config.SSLUpdateToken)
	slog.Debug("config", "nats-port", config.NatsServerPort)
	slog.Debug("config", "leaf-node-port", config.NatsLeafNodePort)
	slog.Debug("config", "auth-token", config.NatsAuthToken)
	slog.Debug("config", "as-leaf-node", config.NatsAsLeafNode)
	slog.Debug("config", "nats-leaf-server-url", config.NatsLeafNodeServerUrl)
	slog.Debug("config", "leaf-server-auth-token", config.NatsLeafNodeServerAuthToken)
	slog.Debug("config", "nats-request-map-tile", config.NatsRequestMapTile)
	slog.Debug("config", "nats-wait-timeout", config.NatsWaitTimeout)
	slog.Debug("config", "log-to-file", config.IsLogToFile)
	slog.Debug("config", "log-dir", config.LogDir)
	slog.Debug("config", "socket5-proxy", config.Socks5Proxy)
	slog.Debug("config", "http-proxy", config.HttpProxy)
	slog.Debug("config", "google-static-base-url", config.DefaultMapStaticApiBaseUrl)
	slog.Debug("config", "google-tile-base-url", config.DefaultMapTileApiBaseUrl)
	slog.Debug("config", "osm-base-url", config.DefaultOSMMapStaticApiBaseUrl)
	slog.Debug("config", "tianditu-base-url", config.DefaultTiandituMapStaticApiBaseUrl)
	return nil
}
