package rpc

import (
	"bfmap"
	"bfmap/bfutil"
	"bfmap/config"
	"bfmap/db"
	"bfmap/dbproto"
	"bfmap/maps"
	bfprivilege "bfmap/privilege"
	"bfmap/session"
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	http "net/http"
	"strconv"
	"strings"
	"time"

	"connectrpc.com/connect"
	"github.com/ygrpc/protodb"
	"github.com/ygrpc/protodb/crud"
	"go.etcd.io/bbolt"
	"google.golang.org/protobuf/proto"
)

type RpcImpl struct {
	UnimplementedBfmapHandler
}

func (RpcImpl) IsSetup(_ context.Context, req *connect.Request[Empty]) (resp *connect.Response[Common], err error) {
	// get db
	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[Common]{Msg: &Common{
			Code: int32(CommonRespCode_DataBaseError), Reason: err.Error()}}, nil
	}
	//dbUser
	dbUser := &dbproto.DbUser{
		Rid: config.AdminRid,
	}
	_, err = crud.DbSelectOne(dbConn.ReadDB, dbUser, []string{"Rid"}, []string{"Name"}, "", false)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_NotFound), Reason: "admin user not found"},
			}, nil
		}
		return &connect.Response[Common]{Msg: &Common{
			Code: int32(CommonRespCode_DataBaseError), Reason: err.Error()}}, nil
	}

	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success)}}, nil
}

func (RpcImpl) Setup(_ context.Context, req *connect.Request[SetupReq]) (resp *connect.Response[Common], err error) {
	setupReq := req.Msg

	//validate SetupReq
	if setupReq.Name == "" || setupReq.Password == "" || setupReq.OrgName == "" {
		return &connect.Response[Common]{Msg: &Common{
			Code: int32(CommonRespCode_InvalidParam), Reason: "name, password and org name cannot be empty"}}, nil
	}

	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[Common]{Msg: &Common{
			Code: int32(CommonRespCode_DataBaseError), Reason: err.Error()}}, nil
	}

	dbUser := &dbproto.DbUser{
		Rid:        config.AdminRid,
		Name:       setupReq.Name,
		Nickname:   setupReq.Name,
		Password:   setupReq.Password,
		CreateTime: bfutil.CurrentUTCTimeUnix(),
		Org:        config.AdminRid,
	}
	// check if admin user already exists
	_, err = crud.DbSelectOne(dbConn.ReadDB, dbUser, []string{"Rid"}, []string{"Name"}, "", false)
	if err == nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError), Reason: "admin user already exists"},
		}, nil
	}

	needAddItems := make([]proto.Message, 0, 4)
	needAddItems = append(needAddItems,
		&dbproto.DbOrg{
			Rid:        config.AdminRid,
			Name:       setupReq.OrgName,
			CreateTime: bfutil.CurrentUTCTimeUnix(),
			OwnerRid:   config.AdminRid,
		},
		dbUser,
		&dbproto.DbProject{
			Rid:        config.AdminRid,
			UserRid:    config.AdminRid,
			Name:       setupReq.ProjectName,
			CreateTime: bfutil.CurrentUTCTimeUnix(),
			Note:       "default project created by setup",
			Setting:    "{}",
			Status:     1,
		},
		&dbproto.DbProjectQuotas{
			Rid:         config.AdminRid,
			QuotasType:  4,
			QuotasLimit: 0,
		},
		&dbproto.DbUserPrivilege{
			Rid:                bfutil.UuidV7(),
			UserRid:            config.AdminRid,
			UpdateTime:         bfutil.CurrentUTCTimeUnix(),
			CanModifyOtherUser: true,
		})

	err = db.InsertItemsWithRollbackOnError(dbConn.WriteDB, needAddItems)
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
				Reason: err.Error(),
			},
		}, nil
	}
	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success)}}, nil
}

func loginByPwd(dbConn *db.BfMapSqliteDBConn, dbUser *dbproto.DbUser, loginReq *LoginReq) *LoginResp {
	if loginReq.PasswordHash == "" {
		return &LoginResp{Code: LoginRespCode_PasswordNotMatch, Reason: "password cannot be empty"}
	}

	b := sha256.Sum256([]byte(loginReq.TimeStr + dbUser.Password))
	pwHash := base64.StdEncoding.EncodeToString(b[:])
	if pwHash != loginReq.PasswordHash {
		return &LoginResp{Code: LoginRespCode_PasswordNotMatch, Reason: "password not match"}
	}

	dbUserSession := &dbproto.DbUserSession{
		Rid:     bfutil.UuidV7(),
		UserRid: dbUser.Rid,
		// update session id
		SessionId:  bfutil.UuidV7(),
		UpdateTime: bfutil.CurrentUTCTimeUnix(),
		// 30 days
		ExpireTime: bfutil.CurrentUTCTimeUnix() + 60*60*24*30,
	}
	_ = session.NewUserLoginInfo(dbUser, dbUserSession)
	_, err := crud.DbInsert(dbConn.WriteDB, dbUserSession, 0, "")
	if err != nil {
		slog.Warn("loginByPwd success but fail to save user login session to db", "err", err)
	}

	return &LoginResp{
		Code:          LoginRespCode_LoginSuccess,
		SessionId:     dbUserSession.SessionId,
		UserRid:       dbUser.Rid,
		UserOrgRid:    dbUser.Org,
		ServerVersion: bfmap.Version,
	}
}

func LoginBySessionId(dbConn *db.BfMapSqliteDBConn, dbUser *dbproto.DbUser, loginReq *LoginReq) *LoginResp {
	if loginReq.SessionId == "" {
		return &LoginResp{Code: LoginRespCode_SessionIdNotExist, Reason: "session id cannot be empty"}
	}

	dbUserSession := &dbproto.DbUserSession{
		UserRid:   dbUser.Rid,
		SessionId: loginReq.SessionId,
	}
	result, err := crud.DbSelectOne(dbConn.ReadDB, dbUserSession, []string{"UserRid", "SessionId"}, nil, "", false)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &LoginResp{Code: LoginRespCode_SessionIdNotExist, Reason: "session not found"}
		}
		return &LoginResp{
			Code:   LoginRespCode_FailWithInternalError,
			Reason: fmt.Sprint("fail to get db user session:", err),
		}
	}

	dbUserSession = result.(*dbproto.DbUserSession)

	// check if session id expired
	if time.Since(time.Unix(dbUserSession.ExpireTime, 0)) > 0 {
		// try to delete expired db session
		go crud.DbDelete(dbConn.WriteDB, dbUserSession, "")
		return &LoginResp{Code: LoginRespCode_SessionIdExpired, Reason: "session expired"}
	}

	// update session id
	dbUserSession.SessionId = bfutil.UuidV7()
	dbUserSession.UpdateTime = bfutil.CurrentUTCTimeUnix()
	dbUserSession.ExpireTime = bfutil.CurrentUTCTimeUnix() + 60*60*24*30

	_ = session.NewUserLoginInfo(dbUser, dbUserSession)
	_, err = crud.DbUpdate(dbConn.WriteDB, dbUserSession, 0, "")
	if err != nil {
		slog.Warn("LoginBySessionId success but fail to update user login session to db", "err", err)
	}

	return &LoginResp{
		Code:          LoginRespCode_LoginSuccess,
		SessionId:     dbUserSession.SessionId,
		UserRid:       dbUser.Rid,
		UserOrgRid:    dbUser.Org,
		ServerVersion: bfmap.Version,
	}
}

// nolint
func (RpcImpl) Login(_ context.Context, req *connect.Request[LoginReq]) (resp *connect.Response[LoginResp], err error) {
	// validate LoginReq
	loginReq := req.Msg
	if loginReq.Name == "" {
		return &connect.Response[LoginResp]{
			Msg: &LoginResp{Code: LoginRespCode_InvalidLoginParam, Reason: "name cannot be empty"}}, nil
	}

	loginTime, err := time.Parse(config.BfTImeFormat, loginReq.TimeStr)
	if err != nil {
		return &connect.Response[LoginResp]{
			Msg: &LoginResp{Code: LoginRespCode_InvalidLoginParam, Reason: fmt.Sprint("cannot parse time:", err)}}, nil
	}

	// login time should be within 5 minutes before or after
	if time.Since(loginTime) > 5*time.Minute {
		return &connect.Response[LoginResp]{
			Msg: &LoginResp{Code: LoginRespCode_ReqTimeTooOld, Reason: "login time is too long ago"}}, nil
	}

	if time.Since(loginTime) < -1*time.Minute {
		return &connect.Response[LoginResp]{
			Msg: &LoginResp{Code: LoginRespCode_ReqTimeTooNew, Reason: "login time is too new"}}, nil
	}

	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[LoginResp]{
			Msg: &LoginResp{Code: LoginRespCode_FailWithInternalError, Reason: fmt.Sprint("can not get db:", err)}}, nil
	}

	dbUser := &dbproto.DbUser{
		Name: loginReq.Name,
	}

	result, err := crud.DbSelectOne(
		dbConn.ReadDB,
		dbUser,
		[]string{"Name"},
		[]string{"Rid", "Password", "Org", "Creater", "Disabled"},
		"",
		false,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &connect.Response[LoginResp]{
				Msg: &LoginResp{Code: LoginRespCode_UserNotExist, Reason: "user not found"}}, nil
		}
		return &connect.Response[LoginResp]{Msg: &LoginResp{
			Code:   LoginRespCode_FailWithInternalError,
			Reason: fmt.Sprint("fail to select user:", err),
		}}, nil
	}

	dbUser = result.(*dbproto.DbUser)

	// Check if user is disabled
	if dbUser.Disabled {
		return &connect.Response[LoginResp]{
			Msg: &LoginResp{Code: LoginRespCode_UserDisabled, Reason: "user account is disabled"}}, nil
	}

	var loginResp *LoginResp
	switch loginReq.LoginMethod {
	case LoginMethod_Password:
		loginResp = loginByPwd(dbConn, dbUser, loginReq)
		return &connect.Response[LoginResp]{Msg: loginResp}, err
	case LoginMethod_SessionId:
		loginResp = LoginBySessionId(dbConn, dbUser, loginReq)
	default:
		return &connect.Response[LoginResp]{
			Msg: &LoginResp{
				Code:   LoginRespCode_InvalidLoginParam,
				Reason: "invalid login method " + strconv.Itoa(int(loginReq.LoginMethod)),
			}}, nil

	}

	return &connect.Response[LoginResp]{Msg: loginResp}, nil
}

func (RpcImpl) Logout(_ context.Context, req *connect.Request[Empty]) (resp *connect.Response[Common], err error) {
	sessionId := req.Header().Get(config.BfSessionIdHeaderName)
	if len(sessionId) == 0 {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidSessionId),
			Reason: "session id cannot be empty",
		}}, nil
	}

	info := session.GetUserLoginInfo(sessionId)
	if info == nil {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidParam),
			Reason: "session id not login",
		}}, nil
	}

	session.DeleteUserLoginInfo(sessionId)
	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: fmt.Sprint("can not get db:", err),
			}}, nil
	}

	_, err = crud.DbDelete(dbConn.WriteDB, &dbproto.DbUserSession{Rid: info.Rid}, "")
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: fmt.Sprint("fail to delete user session in db:", err),
			}}, nil
	}

	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success)}}, nil
}

func (RpcImpl) Ping(_ context.Context, req *connect.Request[Empty]) (resp *connect.Response[Common], err error) {
	sessionId := req.Header().Get(config.BfSessionIdHeaderName)
	if len(sessionId) == 0 {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidSessionId),
			Reason: "session id cannot be empty",
		}}, nil
	}

	// LastAccessTime renew in GetUserLoginInfo func
	info := session.GetUserLoginInfo(sessionId)
	if info == nil {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidParam),
			Reason: "session id not login",
		}}, nil
	}

	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success)}}, nil
}

func (RpcImpl) CreateUser(
	_ context.Context,
	req *connect.Request[CreateUserReq],
) (resp *connect.Response[Common], err error) {
	sessionId := req.Header().Get(config.BfSessionIdHeaderName)
	if len(sessionId) == 0 {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidSessionId),
			Reason: "session id cannot be empty",
		}}, nil
	}

	info := session.GetUserLoginInfo(sessionId)
	if info == nil {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidParam),
			Reason: "session id not login",
		}}, nil
	}

	dbUser := req.Msg.User
	dbUserPrivilege := req.Msg.UserPrivilege
	// validate DbUser
	if dbUser.Rid == "" || dbUser.Name == "" || dbUser.Password == "" || dbUser.Org == "" || dbUser.Creater == "" {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_InvalidParam),
				Reason: "rid, name, password, creater and org cannot be empty",
			}}, nil
	}
	if dbUserPrivilege.UserRid != dbUser.Rid {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_InvalidParam),
				Reason: "user rid should be equal to user privilege rid",
			}}, nil
	}
	if dbUser.Creater != info.UserRid {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_InvalidParam),
				Reason: "creater should be session user",
			}}, nil
	}

	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError), Reason: err.Error()}}, nil
	}

	//check if creater can create user
	CreaterPrivilege := bfprivilege.GetUserPrivilege(dbUser.Creater)
	if CreaterPrivilege == nil {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_DataBaseError),
			Reason: "creater privilege not found",
		}}, nil
	}

	if !CreaterPrivilege.CanModifyOtherUser {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_PermissionDenied), Reason: "no permission to create user"}}, nil
	}

	needAddItems := make([]proto.Message, 0, 4)
	needAddItems = append(needAddItems, dbUser,
		&dbproto.DbProject{
			Rid:        dbUser.Rid,
			UserRid:    dbUser.Rid,
			Name:       "Default",
			CreateTime: bfutil.CurrentUTCTimeUnix(),
			Note:       "default project created for user " + dbUser.Name,
			Setting:    "{}",
			Status:     1,
		},
		&dbproto.DbProjectQuotas{
			Rid:         dbUser.Rid,
			QuotasType:  4,
			QuotasLimit: 0,
		},
		dbUserPrivilege)

	err = db.InsertItemsWithRollbackOnError(dbConn.WriteDB, needAddItems)
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
				Reason: err.Error(),
			},
		}, nil
	}

	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success)}}, nil
}

func (RpcImpl) SetProviderToken(
	_ context.Context,
	req *connect.Request[SetProviderTokenReq],
) (resp *connect.Response[Common], err error) {
	s := session.GetUserLoginInfo(req.Header().Get(config.BfSessionIdHeaderName))
	if s == nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidSessionId), Reason: "invalid session"}}, nil
	}

	t := req.Msg.ProviderToken
	if t.UserRid == "" {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidParam),
			Reason: "userRid cannot be empty",
		}}, nil
	}

	// only admin or provider owner can set project
	if t.UserRid != s.UserRid && s.UserRid != config.AdminRid {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_PermissionDenied),
			Reason: "session user cannot set this user token",
		}}, nil
	}

	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError), Reason: err.Error()}}, nil
	}
	switch req.Msg.Code {
	case 1: //1:create
		if t.Status <= 0 {
			return &connect.Response[Common]{Msg: &Common{
				Code:   int32(CommonRespCode_InvalidParam),
				Reason: "invalid status " + strconv.Itoa(int(t.Status)),
			}}, nil
		}
		if t.Rid == "" || t.UserRid == "" {
			return &connect.Response[Common]{Msg: &Common{
				Code:   int32(CommonRespCode_InvalidParam),
				Reason: "token, rid and userRid cannot be empty",
			}}, nil
		}
		if t.Provider != dbproto.MapProviderEnum_ProviderOSM && len(t.Token) == 0 {
			return &connect.Response[Common]{Msg: &Common{
				Code:   int32(CommonRespCode_InvalidParam),
				Reason: "token can not be empty when provider is not osm",
			}}, nil
		}
		q := &dbproto.DbMapProviderUsedQuotas{
			Rid:        t.Rid,
			UsedQuotas: 0,
		}
		err = db.InsertItemsWithRollbackOnError(dbConn.WriteDB, []proto.Message{t, q})
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{
					Code:   int32(CommonRespCode_DataBaseError),
					Reason: err.Error(),
				}}, nil
		}

		go maps.GlobalMapsManager.SetMapProviderTokenCache(maps.NewMapProviderToken(t, q))
	case 2: // 2:update
		var result proto.Message
		if len(req.Msg.UpdateFields) > 0 {
			result, err = crud.DbUpdatePartialReturnNew(dbConn.WriteDB, t, req.Msg.UpdateFields, "")
			if err != nil {
				return &connect.Response[Common]{
					Msg: &Common{
						Code:   int32(CommonRespCode_DataBaseError),
						Reason: "failed to partial update token:" + err.Error(),
					}}, nil
			}
		} else {
			result, err = crud.DbUpdateReturnNew(dbConn.WriteDB, t, 0, "")
			if err != nil {
				return &connect.Response[Common]{
					Msg: &Common{
						Code:   int32(CommonRespCode_DataBaseError),
						Reason: "failed to update token:" + err.Error(),
					}}, nil
			}
		}
		go maps.GlobalMapsManager.UpdateMapProviderTokenCache(result.(*dbproto.DbMapProviderToken))
	case 4: // 4:delete
		// mark token as delete
		t.Status = 8
		result, err := crud.DbUpdatePartialReturnNew(dbConn.WriteDB, t, []string{"Status"}, "")
		if err != nil {
			return &connect.Response[Common]{Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: "delete provider token fail:" + err.Error(),
			}}, nil
		}

		t = result.(*dbproto.DbMapProviderToken)
		go maps.GlobalMapsManager.UpdateMapProviderTokenCache(t)
	case 8: // 8:force delete
		result, err := crud.DbDeleteReturn(dbConn.WriteDB, t, "")
		if err != nil {
			return &connect.Response[Common]{Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: "force delete provider token fail:" + err.Error(),
			}}, nil
		}
		_, err = crud.DbDelete(dbConn.WriteDB, &dbproto.DbMapProviderUsedQuotas{Rid: t.Rid}, "")
		if err != nil {
			slog.Warn("SetProviderToken delete DbMapProviderUsedQuotas fail", "err", err)
		}
		// delete token from cache
		t = result.(*dbproto.DbMapProviderToken)
		go maps.GlobalMapsManager.DeleteMapProviderTokenCache(t.UserRid, int(t.Provider), t.Rid)
	default:
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidParam), Reason: "invalid code"}}, nil
	}

	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success)}}, nil
}

func (RpcImpl) SetProjectToken(
	_ context.Context,
	req *connect.Request[SetProjectTokenReq],
) (resp *connect.Response[Common], err error) {
	s := session.GetUserLoginInfo(req.Header().Get(config.BfSessionIdHeaderName))
	if s == nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidSessionId), Reason: "invalid session"}}, nil
	}

	token := req.Msg.ProjectToken
	if token == nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidParam),
				Reason: "projectToken cannot be empty",
			},
		}, nil
	}

	mapProject, err := maps.GlobalMapsManager.GetMapProjectCache(token.ProjectRid)
	if err != nil {
		if config.IsVerboseDebugRpc {
			slog.Debug("cannot find project", "token rid", token.Rid, "project rid", token.ProjectRid, "err", err)
		}
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError), Reason: "cannot find project"}}, nil
	}

	// only project owner or admin can set project token
	if mapProject.UserRid != s.UserRid && s.UserRid != config.AdminRid {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_PermissionDenied),
			Reason: "session user cannot set project token",
		}}, nil
	}

	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError), Reason: err.Error()}}, nil
	}
	switch req.Msg.Code {
	case 1: //1:create
		if len(token.Token) == 0 ||
			token.SysName == nil ||
			len(token.SysName.Names) == 0 {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_InvalidParam),
					Reason: "token and sysName cannot be empty",
				},
			}, nil
		}

		_, err = crud.DbInsert(dbConn.WriteDB, token, 0, "")
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{
					Code:   int32(CommonRespCode_DataBaseError),
					Reason: "failed to create new token:" + err.Error(),
				}}, nil
		}
		go maps.GlobalMapsManager.SetMapProjectTokenCache(maps.NewMapProjectToken(token))
	case 2: // 2:update
		var result proto.Message
		if len(req.Msg.UpdateFields) > 0 {
			result, err = crud.DbUpdatePartialReturnNew(dbConn.WriteDB, token, req.Msg.UpdateFields, "")
			if err != nil {
				return &connect.Response[Common]{
					Msg: &Common{
						Code:   int32(CommonRespCode_DataBaseError),
						Reason: "failed to partial update token:" + err.Error(),
					}}, nil
			}
		} else {
			result, err = crud.DbUpdateReturnNew(dbConn.WriteDB, token, 0, "")
			if err != nil {
				return &connect.Response[Common]{
					Msg: &Common{
						Code:   int32(CommonRespCode_DataBaseError),
						Reason: "failed to update token:" + err.Error(),
					}}, nil
			}
		}

		go maps.GlobalMapsManager.UpdateMapProjectTokenCache(result.(*dbproto.DbProjectToken))
	case 3: // 3:rotate token
		// get old token
		oldToken := &dbproto.DbProjectToken{
			Rid: token.Rid,
		}
		result, err := crud.DbSelectOne(dbConn.ReadDB, oldToken, []string{"Rid"}, nil, "", false)
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
					Reason: "failed to get old token:" + err.Error()}}, nil
		}
		// create new token
		newToken := result.(*dbproto.DbProjectToken)
		newToken.Rid = bfutil.UuidV7()
		newToken.Token = token.Token
		_, err = crud.DbInsert(dbConn.WriteDB, newToken, 0, "")
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
					Reason: "failed to create new token:" + err.Error()}}, nil
		}
		go func(oldToken, newToken *dbproto.DbProjectToken) {
			maps.GlobalMapsManager.UpdateMapProjectTokenCache(oldToken)
			maps.GlobalMapsManager.SetMapProjectTokenCache(maps.NewMapProjectToken(newToken))
		}(token, newToken)

		// mark old token as delete
		deleteToken := &dbproto.DbProjectToken{
			Rid:    token.Rid,
			Status: 8,
		}
		result, err = crud.DbUpdatePartialReturnNew(dbConn.WriteDB, deleteToken, []string{"Status"}, "")
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
					Reason: "failed to mark old token as delete" + err.Error()}}, nil
		}
		go maps.GlobalMapsManager.UpdateMapProjectTokenCache(result.(*dbproto.DbProjectToken))

		b, err := proto.Marshal(newToken)
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_ServerError),
					Reason: "fail to marshal new token:" + err.Error()}}, nil
		}
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_Success), Body: b}}, nil
	case 4: // 4:delete
		// mark token as delete
		token.Status = 8
		result, err := crud.DbUpdatePartialReturnNew(dbConn.WriteDB, token, []string{"Status"}, "")
		if err != nil {
			return &connect.Response[Common]{Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: "delete project token fail" + err.Error(),
			}}, nil
		}
		go maps.GlobalMapsManager.UpdateMapProjectTokenCache(result.(*dbproto.DbProjectToken))
	case 8: // 8:force delete
		result, err := crud.DbDeleteReturn(dbConn.WriteDB, token, "")
		if err != nil {
			return &connect.Response[Common]{Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: "force delete project token fail" + err.Error(),
			}}, nil
		}
		maps.GlobalMapsManager.DeleteMapProjectTokenCache(result.(*dbproto.DbProjectToken).Token)
	default:
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidParam), Reason: "invalid code"}}, nil
	}
	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success)}}, nil
}

func updateDbProject(
	db *sql.DB,
	project *dbproto.DbProject,
	updateField []string) (newProject *dbproto.DbProject, err error) {
	var result proto.Message
	if len(updateField) > 0 {
		result, err = crud.DbUpdatePartialReturnNew(db, project, updateField, "")
		if err != nil {
			return nil, fmt.Errorf("fail to partial update DbProject:%s", err.Error())
		}
	} else {
		result, err = crud.DbUpdateReturnNew(db, project, 0, "")
		if err != nil {
			return nil, fmt.Errorf("fail to update DbProject:%s", err.Error())
		}
	}
	return result.(*dbproto.DbProject), nil
}

func updateDbProjectQuotas(
	db *sql.DB,
	projectQuotas *dbproto.DbProjectQuotas,
	updateField []string) (newProjectQuotas *dbproto.DbProjectQuotas, err error) {
	var result proto.Message
	if len(updateField) > 0 {
		result, err = crud.DbUpdatePartialReturnNew(db, projectQuotas, updateField, "")
		if err != nil {
			return nil, fmt.Errorf("fail to partial update DbProjectQuotas:%s", err.Error())
		}
	} else {
		result, err = crud.DbUpdateReturnNew(db, projectQuotas, 0, "")
		if err != nil {
			return nil, fmt.Errorf("fail to update DbProjectQuotas:%s", err.Error())
		}
	}
	return result.(*dbproto.DbProjectQuotas), nil
}

func (RpcImpl) SetProject(
	_ context.Context,
	req *connect.Request[SetProjectReq],
) (resp *connect.Response[Common], err error) {
	s := session.GetUserLoginInfo(req.Header().Get(config.BfSessionIdHeaderName))
	if s == nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidSessionId), Reason: "invalid session"}}, nil
	}

	project := req.Msg.DbProject
	projectQuotas := req.Msg.DbProjectQuotas

	// project cannot be empty except for the case of 2(update)
	if project == nil && req.Msg.Code != 2 {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidParam), Reason: "project cannot be empty"}}, nil
	}

	// only admin or project owner can set project
	if project != nil && project.UserRid != s.UserRid && s.UserRid != config.AdminRid {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_PermissionDenied),
				Reason: "session user cannot set this project",
			}}, nil
	}

	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError), Reason: err.Error()}}, nil
	}

	switch req.Msg.Code {
	case 1: // 1:create
		if project == nil || projectQuotas == nil {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_InvalidParam),
					Reason: "project or projectQuotas cannot be empty",
				},
			}, nil
		}
		err = db.InsertItemsWithRollbackOnError(dbConn.WriteDB, []proto.Message{project, projectQuotas})
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
					Reason: err.Error(),
				},
			}, nil
		}
		go maps.GlobalMapsManager.SetMapProjectCache(maps.NewMapProject(req.Msg.DbProject, projectQuotas))
	case 2: // 2:update
		var newProject *dbproto.DbProject
		var newProjectQuotas *dbproto.DbProjectQuotas
		if project != nil {
			newProject, err = updateDbProject(dbConn.WriteDB, project, req.Msg.UpdateProjectFields)
			if err != nil {
				return &connect.Response[Common]{
					Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
						Reason: err.Error(),
					},
				}, nil
			}
		}

		if projectQuotas != nil {
			newProjectQuotas, err = updateDbProjectQuotas(dbConn.WriteDB, projectQuotas, req.Msg.UpdateQuotasFields)
			if err != nil {
				return &connect.Response[Common]{
					Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
						Reason: err.Error(),
					},
				}, nil
			}
		}

		go maps.GlobalMapsManager.UpdateMapProjectCache(newProject, newProjectQuotas)
	case 4: // 4:delete
		project = &dbproto.DbProject{
			Rid:    req.Msg.DbProject.Rid,
			Status: 8,
		}
		_, err = crud.DbUpdatePartial(dbConn.WriteDB, project, []string{"Status"}, "")
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
					Reason: fmt.Sprint("fail to delete DbProject:", err),
				},
			}, nil
		}
		go maps.GlobalMapsManager.UpdateMapProjectCache(project, nil)
	case 8: // 8:force delete
		_, err = crud.DbDelete(dbConn.WriteDB, project, "")
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{Code: int32(CommonRespCode_DataBaseError),
					Reason: fmt.Sprint("fail to force delete DbProject:", err),
				},
			}, nil
		}
		maps.GlobalMapsManager.DeleteMapProjectCache(req.Msg.DbProject.Rid)
	default:
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidParam), Reason: "invalid code"}}, nil
	}

	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success)}}, nil
}

func (RpcImpl) CreateTempMapProjectToken(
	_ context.Context,
	req *connect.Request[Empty],
) (resp *connect.Response[Common], err error) {
	// validate session id
	s := session.GetUserLoginInfo(req.Header().Get(config.BfSessionIdHeaderName))
	if s == nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidSessionId), Reason: "invalid session"}}, nil
	}

	token := "TEMP_" + bfutil.UuidV7()
	maps.SetTempMapProjectToken(token)

	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success), Msg: token}}, nil
}

func (RpcImpl) DeleteMapCacheIndexes(
	_ context.Context,
	req *connect.Request[DeleteMapCacheIndexesReq],
) (resp *connect.Response[Common], err error) {
	// validate session id
	s := session.GetUserLoginInfo(req.Header().Get(config.BfSessionIdHeaderName))
	if s == nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidSessionId), Reason: "invalid session"}}, nil
	}

	if s.UserRid != config.AdminRid {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_PermissionDenied), Reason: "only admin can delete"}}, nil
	}

	deleteReq := req.Msg

	// validate DeleteMapCacheIndexesReq
	if len(deleteReq.Providers) == 0 {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidParam),
			Reason: "providers cannot be empty",
		}}, nil
	}

	if deleteReq.Zoom <= 0 {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidParam),
			Reason: "zoom must be greater than 0",
		}}, nil
	}

	if deleteReq.Zoom > 22 {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidParam),
			Reason: "zoom must be less and equal to 22",
		}}, nil
	}

	if deleteReq.MinLat > deleteReq.MaxLat {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidParam),
			Reason: "minLat must be less than maxLat",
		}}, nil
	}

	if deleteReq.MinLon > deleteReq.MaxLon {
		return &connect.Response[Common]{Msg: &Common{
			Code:   int32(CommonRespCode_InvalidParam),
			Reason: "minLon must be less than maxLon",
		}}, nil
	}

	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError), Reason: err.Error()}}, nil
	}

	var tableName string
	switch deleteReq.MapType {
	case 1: // roadmap
		tableName = "DbMapCacheRoadmapIndex"
	case 2: // satellite
		tableName = "DbMapCacheSatelliteIndex"
	case 3: // hybrid
		tableName = "DbMapCacheHybridIndex"
	default:
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_InvalidParam), Reason: "invalid map type"}}, nil
	}

	// build sql statement
	conditionBuilder := &strings.Builder{}
	for i, p := range deleteReq.Providers {
		if i == 0 {
			conditionBuilder.WriteString("( ")
		}
		conditionBuilder.WriteString("Provider = " + strconv.Itoa(int(p)))
		if i == len(deleteReq.Providers)-1 {
			conditionBuilder.WriteString(" )")
		} else {
			conditionBuilder.WriteString(" OR ")
		}
	}

	z := int(deleteReq.Zoom)
	// calculate x,y,z
	m := maps.GlobalMapsManager.Mercator
	var minGx, maxGx, minGy, maxGy int
	conditionBuilder.WriteString(" AND ( ")
	for ; z <= 22; z++ {
		minGx, maxGy = m.LatLonToGoogleTiles(deleteReq.MinLat, deleteReq.MinLon, z)
		maxGx, minGy = m.LatLonToGoogleTiles(deleteReq.MaxLat, deleteReq.MaxLon, z)

		conditionBuilder.WriteString(" ( ")
		conditionBuilder.WriteString("TileZ = " + strconv.Itoa(z))
		conditionBuilder.WriteString(" AND TileX >= " + strconv.Itoa(minGx))
		conditionBuilder.WriteString(" AND TileX <= " + strconv.Itoa(maxGx))
		conditionBuilder.WriteString(" AND TileY >= " + strconv.Itoa(minGy))
		conditionBuilder.WriteString(" AND TileY <= " + strconv.Itoa(maxGy))
		conditionBuilder.WriteString(" )")
		if z < 22 {
			conditionBuilder.WriteString(" OR ")
		}
	}
	conditionBuilder.WriteString(" )")

	if deleteReq.CacheTime > 0 {
		conditionBuilder.WriteString(" AND CacheTime <= " + strconv.Itoa(int(deleteReq.CacheTime)))
	}

	KeyValueDb, err := db.GetKeyValueDb()
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{Code: int32(CommonRespCode_DataBaseError), Reason: err.Error()}}, nil
	}

	fnGetDb := func(meta http.Header, schemaName string, tableName string, writable bool) (db *sql.DB, err error) {
		return dbConn.ReadDB, nil
	}

	fnTableQueryPermission := func(meta http.Header, schemaName string, tableName string, db *sql.DB, dbmsg proto.Message) (wherStr string, whereSqlVals []any, err error) {
		return conditionBuilder.String(), nil, nil
	}

	mapReq := &maps.MapReq{}
	fnSendResp := func(resp *protodb.QueryResp) error {
		for _, msgBytes := range resp.MsgBytes {
			switch deleteReq.MapType {
			case 1: // roadmap
				index := &dbproto.DbMapCacheRoadmapIndex{}
				err = proto.Unmarshal(msgBytes, index)
				if err != nil {
					return err
				}
				mapReq.Provider = int(index.Provider)
				mapReq.X = int(index.TileX)
				mapReq.Y = int(index.TileY)
				mapReq.Z = int(index.TileZ)
				mapReq.MapType = maps.MapTypeRoadmap
				mapReq.Lang = index.Language
				mapReq.ImageFormatInt = int(index.TileImageFormat)
				mapReq.EnableGcj02 = int(index.Gcj02)
			case 2: // satellite
				index := &dbproto.DbMapCacheSatelliteIndex{}
				err = proto.Unmarshal(msgBytes, index)
				if err != nil {
					return err
				}
				mapReq.Provider = int(index.Provider)
				mapReq.X = int(index.TileX)
				mapReq.Y = int(index.TileY)
				mapReq.Z = int(index.TileZ)
				mapReq.MapType = maps.MapTypeSatellite
				mapReq.ImageFormatInt = int(index.TileImageFormat)
				mapReq.EnableGcj02 = int(index.Gcj02)
			case 3: // hybrid
				index := &dbproto.DbMapCacheHybridIndex{}
				err = proto.Unmarshal(msgBytes, index)
				if err != nil {
					return err
				}
				mapReq.Provider = int(index.Provider)
				mapReq.X = int(index.TileX)
				mapReq.Y = int(index.TileY)
				mapReq.Z = int(index.TileZ)
				mapReq.MapType = maps.MapTypeHybrid
				mapReq.Lang = index.Language
				mapReq.ImageFormatInt = int(index.TileImageFormat)
				mapReq.EnableGcj02 = int(index.Gcj02)
			default:
				return errors.New("unknown map type")
			}

			err = KeyValueDb.Update(func(tx *bbolt.Tx) error {
				b := tx.Bucket(db.BucketName)
				k := mapReq.Key()
				data := b.Get([]byte(k))
				if data == nil {
					return nil
				}
				t := &maps.TileInfo{}
				err2 := json.Unmarshal(data, t)
				if err2 != nil {
					return fmt.Errorf("unmarshal tile info of %s fail: %w", k, err2)
				}
				t.Status = 8
				data, err2 = json.Marshal(t)
				if err2 != nil {
					return fmt.Errorf("marshal tile info of %s fail: %w", k, err2)
				}
				err2 = b.Put([]byte(k), data)
				if err2 != nil {
					return fmt.Errorf("set key-value db of %s fail: %w", k, err2)
				}
				return nil
			})
			if err != nil {
				return err
			}
		}
		return nil
	}

	err = crud.TableQuery(context.Background(), http.Header{}, &protodb.TableQueryReq{
		TableName: tableName,
	}, fnGetDb, fnTableQueryPermission, fnSendResp)
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: "table query and set tile info fail" + err.Error(),
			}}, nil
	}

	if config.IsVerboseDebugRpc {
		slog.Debug("mark map cache index as deleted", "condition sql", conditionBuilder.String())
	}

	// mark cache index status as deleted
	_, err = dbConn.WriteDB.Exec("UPDATE " + tableName + " SET Status=8 WHERE " + conditionBuilder.String())
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: "mark map cache index as deleted fail:" + err.Error(),
			}}, nil
	}

	return &connect.Response[Common]{Msg: &Common{Code: int32(CommonRespCode_Success)}}, nil
}

// UpdateDbUser updates a DbUser record
// When a user is disabled, it also updates all their projects to disabled and removes all their sessions
func (RpcImpl) UpdateDbUser(
	_ context.Context,
	req *connect.Request[UpdateDbUserReq],
) (resp *connect.Response[Common], err error) {
	// Validate session ID
	s := session.GetUserLoginInfo(req.Header().Get(config.BfSessionIdHeaderName))
	if s == nil {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_InvalidSessionId),
				Reason: "invalid session",
			}}, nil
	}

	// Get database connection
	dbConn, err := db.GetDbConn()
	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: fmt.Sprintf("get db connection error: %v", err),
			}}, nil
	}

	// Check if the user has permission to modify other users
	if s.UserRid != req.Msg.User.Rid {
		// Check if the user has permission to modify other users
		canModifyOtherUser := bfprivilege.IsUserCanModifyOtherUser(s.UserRid)
		if !canModifyOtherUser {
			return &connect.Response[Common]{
				Msg: &Common{
					Code:   int32(CommonRespCode_PermissionDenied),
					Reason: "no permission to modify other users",
				}}, nil
		}

		if s.UserRid != config.AdminRid {
			sqlStr := `SELECT 1 FROM DbUser WHERE Rid == ? AND Rid in (` + subUsersSqlStr + `)`
			rows, err := dbConn.ReadDB.Query(sqlStr, req.Msg.User.Rid, s.UserRid)
			if err != nil {
				return &connect.Response[Common]{
					Msg: &Common{
						Code:   int32(CommonRespCode_DataBaseError),
						Reason: fmt.Sprintf("check session user permission error: %v", err),
					}}, nil
			}
			defer rows.Close()
			if !rows.Next() {
				return &connect.Response[Common]{
					Msg: &Common{
						Code:   int32(CommonRespCode_PermissionDenied),
						Reason: "session user has no permission to modify this user",
					}}, nil
			}
		}
	}

	// Check if the user exists
	originalUser := &dbproto.DbUser{Rid: req.Msg.User.Rid}
	_, err = crud.DbSelectOne(
		dbConn.ReadDB,
		originalUser,
		[]string{"Rid"},
		nil,
		"",
		false,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &connect.Response[Common]{
				Msg: &Common{
					Code:   int32(CommonRespCode_NotFound),
					Reason: "user not found",
				}}, nil
		}
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: fmt.Sprintf("check user existence error: %v", err),
			}}, nil
	}

	// Update the user
	updateFields := req.Msg.UpdateFields

	// Check if we're updating the Disabled field and if it's being set to true
	isDisabling := false
	for _, field := range updateFields {
		if field == "Disabled" && req.Msg.User.Disabled {
			isDisabling = true
			break
		}
	}

	// Update the user
	if len(updateFields) == 0 {
		// full update
		_, err = crud.DbUpdate(dbConn.WriteDB, req.Msg.User, 0, "")
	} else {
		// partial update
		_, err = crud.DbUpdatePartial(dbConn.WriteDB, req.Msg.User, updateFields, "")
	}

	if err != nil {
		return &connect.Response[Common]{
			Msg: &Common{
				Code:   int32(CommonRespCode_DataBaseError),
				Reason: fmt.Sprintf("update user error: %v", err),
			}}, nil
	}

	// If we're disabling the user, update all their projects to disabled and remove all their sessions
	if isDisabling {
		// Update all projects to disabled (status 4)
		_, err = dbConn.WriteDB.Exec("UPDATE DbProject SET Status = 4 WHERE UserRid = ?", req.Msg.User.Rid)
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{
					Code:   int32(CommonRespCode_DataBaseError),
					Reason: fmt.Sprintf("update projects error: %v", err),
				}}, nil
		}

		// Delete all user sessions
		_, err = dbConn.WriteDB.Exec("DELETE FROM DbUserSession WHERE UserRid = ?", req.Msg.User.Rid)
		if err != nil {
			return &connect.Response[Common]{
				Msg: &Common{
					Code:   int32(CommonRespCode_DataBaseError),
					Reason: fmt.Sprintf("delete sessions error: %v", err),
				}}, nil
		}

		// Remove user from memory session cache
		session.DeleteUserLoginInfoByUserRid(req.Msg.User.Rid)
	}

	return &connect.Response[Common]{
		Msg: &Common{
			Code: int32(CommonRespCode_Success),
		},
	}, nil
}

func (RpcImpl) GetCurrentUsage(
	_ context.Context,
	req *connect.Request[GetCurrentUsageReq],
) (resp *connect.Response[GetCurrentUsageResp], err error) {
	// validate session id
	s := session.GetUserLoginInfo(req.Header().Get(config.BfSessionIdHeaderName))
	if s == nil {
		return &connect.Response[GetCurrentUsageResp]{
			Msg: &GetCurrentUsageResp{
				Code:   int32(CommonRespCode_InvalidSessionId),
				Reason: "invalid session",
			},
		}, nil
	}

	switch req.Msg.Code {
	case 1: //1:provider token
		if s.UserRid != req.Msg.UserRid && s.UserRid != config.AdminRid {
			return &connect.Response[GetCurrentUsageResp]{
				Msg: &GetCurrentUsageResp{
					Code:   int32(CommonRespCode_PermissionDenied),
					Reason: "only admin user can view other's current usage",
				},
			}, nil
		}
		providerTokens, err := maps.GlobalMapsManager.GetMapProviderTokens(req.Msg.UserRid, int(req.Msg.Provider))
		if err != nil {
			return &connect.Response[GetCurrentUsageResp]{
				Msg: &GetCurrentUsageResp{
					Code:         int32(CommonRespCode_DataBaseError),
					Reason:       "get map provider token fail:" + err.Error(),
					CurrentUsage: 0,
				},
			}, nil
		}

		for _, providerToken := range providerTokens {
			if providerToken.Token == req.Msg.ProviderToken {
				return &connect.Response[GetCurrentUsageResp]{
					Msg: &GetCurrentUsageResp{
						CurrentUsage: providerToken.UsedCount.Load(),
					},
				}, nil
			}
		}

		return &connect.Response[GetCurrentUsageResp]{
			Msg: &GetCurrentUsageResp{
				Code:   int32(CommonRespCode_InvalidParam),
				Reason: "invalid provider token",
			},
		}, nil
	case 2: //project
		project, err := maps.GlobalMapsManager.GetMapProjectCache(req.Msg.ProjectRid)
		if err != nil {
			return &connect.Response[GetCurrentUsageResp]{
				Msg: &GetCurrentUsageResp{
					Code:         int32(CommonRespCode_DataBaseError),
					Reason:       "get project fail:" + err.Error(),
					CurrentUsage: 0,
				},
			}, nil
		}
		return &connect.Response[GetCurrentUsageResp]{
			Msg: &GetCurrentUsageResp{
				CurrentUsage: project.UsedCount.Load(),
			},
		}, nil
	case 3: //project token
		projectToken, err := maps.GlobalMapsManager.GetMapProjectTokenCache(req.Msg.ProjectToken)
		if err != nil {
			return &connect.Response[GetCurrentUsageResp]{
				Msg: &GetCurrentUsageResp{
					Code:         int32(CommonRespCode_DataBaseError),
					Reason:       "get project token fail:" + err.Error(),
					CurrentUsage: 0,
				},
			}, nil
		}
		return &connect.Response[GetCurrentUsageResp]{
			Msg: &GetCurrentUsageResp{
				CurrentUsage: projectToken.UsageCount.Load(),
			},
		}, nil
	default:
		return &connect.Response[GetCurrentUsageResp]{
			Msg: &GetCurrentUsageResp{
				Code:   int32(CommonRespCode_InvalidParam),
				Reason: "invalid code",
			},
		}, nil
	}
}
