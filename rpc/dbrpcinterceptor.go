package rpc

import (
	"context"

	connect "connectrpc.com/connect"
	"github.com/ygrpc/protodb/service"
)

type dbrpcInterceptor struct{}

func NewDbrpcInterceptor() *dbrpcInterceptor {
	return &dbrpcInterceptor{}
}

func (i *dbrpcInterceptor) WrapUnary(next connect.UnaryFunc) connect.UnaryFunc {
	return connect.UnaryFunc(func(
		ctx context.Context,
		req connect.AnyRequest,
	) (connect.AnyResponse, error) {
		// no action
		return next(ctx, req)
	})
}

func (i *dbrpcInterceptor) WrapStreamingClient(next connect.StreamingClientFunc) connect.StreamingClientFunc {
	return connect.StreamingClientFunc(func(
		ctx context.Context,
		spec connect.Spec,
	) connect.StreamingClientConn {
		conn := next(ctx, spec)

		// add error related header into request, if not set
		headers := conn.RequestHeader()
		if len(headers.Get(service.YgrpcErrHeader)) <= 0 {
			// Enable error reporting by setting to "1"
			headers.Set(service.YgrpcErrHeader, "1")
		}

		if len(headers.Get(service.YgrpcErrMax)) <= 0 {
			// Set maximum error message length to 1024 bytes
			headers.Set(service.YgrpcErrMax, "1024")
		}

		return conn
	})
}

func (i *dbrpcInterceptor) WrapStreamingHandler(next connect.StreamingHandlerFunc) connect.StreamingHandlerFunc {
	return connect.StreamingHandlerFunc(func(
		ctx context.Context,
		conn connect.StreamingHandlerConn,
	) error {
		// add error related header into request, if not set
		headers := conn.RequestHeader()
		if len(headers.Get(service.YgrpcErrHeader)) <= 0 {
			// Enable error reporting by setting to "1"
			headers.Set(service.YgrpcErrHeader, "1")
		}

		if len(headers.Get(service.YgrpcErrMax)) <= 0 {
			// Set maximum error message length to 1024 bytes
			headers.Set(service.YgrpcErrMax, "1024")
		}

		return next(ctx, conn)
	})
}
