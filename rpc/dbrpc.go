package rpc

import (
	"bfmap/config"
	dbLib "bfmap/db"
	"bfmap/dbproto"
	bfprivilege "bfmap/privilege"
	"bfmap/session"
	"database/sql"
	"errors"
	"net/http"

	connect "connectrpc.com/connect"
	"github.com/ygrpc/protodb/crud"

	"github.com/ygrpc/protodb"
	"github.com/ygrpc/protodb/service"
	"google.golang.org/protobuf/proto"
)

// get all user Rid that `userRid` can edit, include `userRid` itself,not include user with same dbOrg
const subUsersSqlStr = `WITH RECURSIVE SubUsers (userRid) AS (
    VALUES (?) --userRid
    UNION ALL
    SELECT u.Rid FROM DbUser u, SubUsers su WHERE u.Creater == su.userRid AND u.Rid != su.userRid
    UNION ALL
    SELECT u.Rid FROM
    DbUser u JOIN DbOrg o ON u.Org = o.Rid, SubUsers su
    WHERE o.OwnerRid == su.userRid AND u.Rid != su.userRid
)
SELECT DISTINCT * FROM SubUsers`

// **only for query**, get all user Rid that `userRid` can edit,
// include `userRid` itself and user with same dbOrg, creater user and owner of the user's dbOrg
const userRidAndSameOrgUserRidSqlStr = subUsersSqlStr +
	` UNION SELECT u.Rid FROM DbUser u WHERE u.Org =? -- user Org Rid
	  UNION SELECT ? --Creater Rid
	  UNION SELECT o.OwnerRid FROM DbOrg o WHERE o.Rid = ? -- user Org Rid
	  `

// web client use this to determine whether it should re-login, if modified, should modify in web client too
var ErrorSessionNotFound = errors.New("session not found")
var ErrorTypeAssertion = errors.New("type assertion error")
var ErrorNoPermission = errors.New("no permission")
var ErrorDeny = errors.New("deny")

func fnGetCrudDb(meta http.Header, schemaName string, tableName string, writable bool) (db *sql.DB, err error) {
	if !session.HasSessionIdLogin(meta.Get(config.BfSessionIdHeaderName)) {
		return nil, ErrorSessionNotFound
	}

	dbConn, err := dbLib.GetDbConn()
	if err != nil {
		return nil, err
	}
	if writable {
		return dbConn.WriteDB, nil
	}

	return dbConn.ReadDB, nil
}

func fnCrudPermissionDeny(
	meta http.Header,
	schemaName string,
	crudCode protodb.CrudReqCode,
	db *sql.DB,
	dbmsg proto.Message,
) (err error) {
	return ErrorDeny
}

func fnCrudPermissionForDbOrg(
	meta http.Header,
	schemaName string,
	crudCode protodb.CrudReqCode,
	db *sql.DB,
	dbmsg proto.Message,
) (err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return ErrorSessionNotFound
	}

	dbOrg, ok := dbmsg.(*dbproto.DbOrg)
	if !ok {
		return ErrorTypeAssertion
	}

	if s.OrgRid == dbOrg.Rid && (crudCode == protodb.CrudReqCode_QUERY || crudCode == protodb.CrudReqCode_SELECTONE) {
		// session user can query DbOrg that user is in
		return nil
	}

	if crudCode == protodb.CrudReqCode_INSERT {
		// owner of dbOrg must be session user when creating dbOrg
		if dbOrg.OwnerRid != s.UserRid {
			return ErrorNoPermission
		} else {
			return nil
		}
	}

	// permission for update, partialUpdate, delete or query dbOrg that session user is not in
	sqlStr := `SELECT 1 FROM DbOrg WHERE Rid == ? AND OwnerRid in (` + subUsersSqlStr + `)`
	rows, err := db.Query(sqlStr, dbOrg.Rid, s.UserRid)
	if err != nil {
		return errors.New("query permission for dbOrg fail:" + err.Error())
	}
	defer rows.Close()
	if !rows.Next() {
		return ErrorNoPermission
	}

	return nil
}

func fnCrudPermissionForDbUser(
	meta http.Header,
	schemaName string,
	crudCode protodb.CrudReqCode,
	db *sql.DB,
	dbmsg proto.Message,
) (err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all users
		if crudCode == protodb.CrudReqCode_QUERY ||
			crudCode == protodb.CrudReqCode_SELECTONE {
			return nil
		}
		// admin user can modify all users if admin has permission
		if !bfprivilege.IsUserCanModifyOtherUser(s.UserRid) {
			return ErrorNoPermission
		}
		return nil
	}

	dbUser, ok := dbmsg.(*dbproto.DbUser)
	if !ok {
		return ErrorTypeAssertion
	}

	// cannot delete system admin user
	if dbUser.Rid == config.AdminRid && crudCode == protodb.CrudReqCode_DELETE {
		return errors.New("cannot delete system admin user with rid " + config.AdminRid)
	}

	if s.UserRid == dbUser.Rid {
		// session user itself
		return nil
	}

	if crudCode == protodb.CrudReqCode_INSERT {
		return ErrorNoPermission
	}

	// permission for query, dbUser should be one in subUser or in same dbOrg of session user
	if crudCode == protodb.CrudReqCode_QUERY ||
		crudCode == protodb.CrudReqCode_SELECTONE {
		sqlStr := `SELECT 1 FROM DbUser WHERE Rid == ? AND Rid in (` + userRidAndSameOrgUserRidSqlStr + `)`
		rows, err := db.Query(sqlStr, dbUser.Rid, s.UserRid, s.OrgRid, s.CreaterRid, s.OrgRid)
		if err != nil {
			return errors.New("query permission for dbUser fail:" + err.Error())
		}
		defer rows.Close()
		if !rows.Next() {
			return ErrorNoPermission
		}
		return nil
	}

	// check permission for modifying user
	if !bfprivilege.IsUserCanModifyOtherUser(s.UserRid) {
		return ErrorNoPermission
	}

	// permission for update, partialUpdate, delete. dbUser should be one of subUsers.
	sqlStr := `SELECT 1 FROM DbUser WHERE Rid == ? AND Rid in (` + subUsersSqlStr + `)`
	rows, err := db.Query(sqlStr, dbUser.Rid, s.UserRid)
	if err != nil {
		return errors.New("query permission for dbUser fail:" + err.Error())
	}
	defer rows.Close()
	if !rows.Next() {
		return ErrorNoPermission
	}

	return nil
}

func fnCrudPermissionForDbUserPrivilege(
	meta http.Header,
	schemaName string,
	crudCode protodb.CrudReqCode,
	db *sql.DB,
	dbmsg proto.Message,
) (err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all users
		if crudCode == protodb.CrudReqCode_QUERY ||
			crudCode == protodb.CrudReqCode_SELECTONE {
			return nil
		}
		// admin user can modify all users if admin has permission
		if !bfprivilege.IsUserCanModifyOtherUser(s.UserRid) {
			return ErrorNoPermission
		}
		return nil
	}

	dbUserPrivilege, ok := dbmsg.(*dbproto.DbUserPrivilege)
	if !ok {
		return ErrorTypeAssertion
	}

	if crudCode == protodb.CrudReqCode_INSERT {
		// it should be created by rpc method bfmap.CreateUser
		return ErrorNoPermission
	}

	// query DbUserPrivilege first,dbmsg's fields may not contain UserRid
	var cacheUserPrivilege *dbproto.DbUserPrivilege
	bfprivilege.UserPrivilegeCache.Range(func(key string, value *dbproto.DbUserPrivilege) bool {
		if value.Rid == dbUserPrivilege.Rid {
			cacheUserPrivilege = value
			return false
		}
		return true
	})

	if cacheUserPrivilege == nil {
		cacheUserPrivilege, err = dbLib.GetDbUserPrivilegeByRid(dbUserPrivilege.Rid)
		if err != nil {
			return errors.New("no dbUserPrivilege for " + dbUserPrivilege.Rid)
		}
	}

	if cacheUserPrivilege.UserRid == config.AdminRid {
		return errors.New("can not modify system admin user privilege")
	}

	// permission for query
	if crudCode == protodb.CrudReqCode_QUERY ||
		crudCode == protodb.CrudReqCode_SELECTONE {
		sqlStr := `SELECT 1 FROM DbUser WHERE Rid == ? AND Rid in (` + userRidAndSameOrgUserRidSqlStr + `)`
		rows, err := db.Query(sqlStr, cacheUserPrivilege.UserRid, s.UserRid, s.OrgRid, s.CreaterRid, s.OrgRid)
		if err != nil {
			return errors.New("query permission for dbUserPrivilege fail:" + err.Error())
		}
		defer rows.Close()
		if !rows.Next() {
			return ErrorNoPermission
		}
		return nil
	}

	// permission for modify
	if !bfprivilege.IsUserCanModifyOtherUser(s.UserRid) {
		return ErrorNoPermission
	}

	// permission for update, partialUpdate, delete. dbUser should be one of subUsers.
	sqlStr := `SELECT 1 FROM DbUser WHERE Rid == ? AND Rid in (` + subUsersSqlStr + `)`
	rows, err := db.Query(sqlStr, cacheUserPrivilege.UserRid, s.UserRid)
	if err != nil {
		return errors.New("query permission for dbUserPrivilege fail:" + err.Error())
	}
	defer rows.Close()
	if !rows.Next() {
		return ErrorNoPermission
	}
	return nil
}

func fnCrudPermissionOnlyQuery(
	meta http.Header,
	schemaName string,
	crudCode protodb.CrudReqCode,
	db *sql.DB,
	dbmsg proto.Message,
) (err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return ErrorSessionNotFound
	}

	if crudCode == protodb.CrudReqCode_QUERY || crudCode == protodb.CrudReqCode_SELECTONE {
		return nil
	}

	return ErrorNoPermission
}

func fnTableQueryPermissionWithLoginCheck(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	if !session.HasSessionIdLogin(meta.Get(config.BfSessionIdHeaderName)) {
		return "", nil, ErrorSessionNotFound
	}

	return "", nil, nil
}

func fnTableQueryPermissionDeny(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	return "", nil, ErrorDeny
}

func fnTableQueryPermissionForDbOrg(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return "", nil, ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all
		return "", nil, nil
	}

	return `(Rid = ? OR OwnerRid IN ( ` + subUsersSqlStr + ` ))`, []any{s.OrgRid, s.UserRid}, nil
}

func fnTableQueryPermissionForDbUser(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return "", nil, ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all
		return "", nil, nil
	}

	return `(Rid IN (` + userRidAndSameOrgUserRidSqlStr + ` ) )`, []any{
		s.UserRid,
		s.OrgRid,
		s.CreaterRid,
		s.OrgRid,
	}, nil
}

func fnTableQueryPermissionForDbUserPrivilege(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return "", nil, ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all
		return "", nil, nil
	}

	return `(UserRid IN (` + userRidAndSameOrgUserRidSqlStr + `) )`, []any{
		s.UserRid,
		s.OrgRid,
		s.CreaterRid,
		s.OrgRid,
	}, nil
}

// check if the user has permission to access the project.
func fnTableQueryPermissionForDbProject(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return "", nil, ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all
		return "", nil, nil
	}

	return "UserRid=?", []any{s.UserRid}, nil
}

func fnTableQueryPermissionWithProjectRidCheck(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return "", nil, ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all
		return "", nil, nil
	}

	return `ProjectRid IN (SELECT Rid FROM DbProject WHERE UserRid=?)`, []any{s.UserRid}, nil
}
func fnTableQueryPermissionForDbProjectQuotas(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return "", nil, ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all
		return "", nil, nil
	}

	return `Rid IN (SELECT Rid FROM DbProject WHERE UserRid=?)`, []any{s.UserRid}, nil
}

func fnTableQueryPermissionForDbMapProviderToken(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return "", nil, ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all
		return "", nil, nil
	}

	return `UserRid=? OR UserRid=?`, []any{s.UserRid, config.AdminRid}, nil
}

func fnTableQueryPermissionForDbMapProviderUsedQuotas(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return "", nil, ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all
		return "", nil, nil
	}

	return `Rid IN (SELECT Rid FROM DbMapProviderToken WHERE UserRid=? OR UserRid=?)`, []any{
		s.UserRid,
		config.AdminRid,
	}, nil
}

func fnTableQueryPermissionForDbMapProviderTokenUsage(
	meta http.Header,
	schemaName string,
	tableName string,
	db *sql.DB,
	dbmsg proto.Message,
) (whenStr string, sqlVals []any, err error) {
	s := session.GetUserLoginInfo(meta.Get(config.BfSessionIdHeaderName))
	if s == nil {
		return "", nil, ErrorSessionNotFound
	}

	if s.UserRid == config.AdminRid {
		// admin user can view all
		return "", nil, nil
	}

	return `ProviderRid IN (SELECT Rid FROM DbMapProviderToken WHERE UserRid=? OR UserRid=?)`, []any{
		s.UserRid,
		config.AdminRid,
	}, nil
}

func OnDbUserPrivilegeDbBroadcast(
	meta http.Header,
	db *sql.DB,
	req *protodb.CrudReq,
	reqMsg proto.Message,
	respMsg proto.Message,
) {
	switch req.Code {
	case protodb.CrudReqCode_INSERT, protodb.CrudReqCode_UPDATE:
		privilege, ok := reqMsg.(*dbproto.DbUserPrivilege)
		if !ok {
			return
		}
		bfprivilege.SetUserPrivilegeCache(privilege.UserRid, privilege)
	case protodb.CrudReqCode_PARTIALUPDATE, protodb.CrudReqCode_DELETE:
		privilege, ok := reqMsg.(*dbproto.DbUserPrivilege)
		if !ok {
			return
		}

		if len(privilege.UserRid) > 0 {
			bfprivilege.UserPrivilegeCache.Delete(privilege.UserRid)
			return
		}

		// in case there is no UserRid in reqMsg
		bfprivilege.UserPrivilegeCache.Range(func(key string, value *dbproto.DbUserPrivilege) bool {
			if value.Rid == privilege.Rid {
				bfprivilege.UserPrivilegeCache.Delete(key)
				return false
			}
			return true
		})
	}
}

func CreateDbRpcHandler(opts ...connect.HandlerOption) (string, http.Handler) {
	crud.GlobalCrudBroadcaster.RegisterBroadcast("DbUserPrivilege", OnDbUserPrivilegeDbBroadcast)
	fnCrudPermission := map[string]crud.TfnProtodbCrudPermission{
		"DbOrg":                    fnCrudPermissionForDbOrg,
		"DbUser":                   fnCrudPermissionForDbUser,
		"DbUserPrivilege":          fnCrudPermissionForDbUserPrivilege,
		"DbUserSession":            fnCrudPermissionDeny,
		"DbProject":                fnCrudPermissionOnlyQuery,
		"DbProjectToken":           fnCrudPermissionOnlyQuery,
		"DbProjectQuotas":          fnCrudPermissionOnlyQuery,
		"DbProjectTokenUsage":      fnCrudPermissionOnlyQuery,
		"DbMapProviderToken":       fnCrudPermissionOnlyQuery,
		"DbMapProviderUsedQuotas":  fnCrudPermissionOnlyQuery,
		"DbMapProviderTokenUsage":  fnCrudPermissionOnlyQuery,
		"DbMapCacheRoadmapIndex":   fnCrudPermissionOnlyQuery,
		"DbMapCacheSatelliteIndex": fnCrudPermissionOnlyQuery,
		"DbMapCacheHybridIndex":    fnCrudPermissionOnlyQuery,
	}
	fnTableQueryPermission := map[string]crud.TfnTableQueryPermission{
		"DbOrg":                    fnTableQueryPermissionForDbOrg,
		"DbUser":                   fnTableQueryPermissionForDbUser,
		"DbUserPrivilege":          fnTableQueryPermissionForDbUserPrivilege,
		"DbUserSession":            fnTableQueryPermissionDeny,
		"DbProject":                fnTableQueryPermissionForDbProject,
		"DbProjectToken":           fnTableQueryPermissionWithProjectRidCheck,
		"DbProjectQuotas":          fnTableQueryPermissionForDbProjectQuotas,
		"DbProjectTokenUsage":      fnTableQueryPermissionWithProjectRidCheck,
		"DbMapProviderToken":       fnTableQueryPermissionForDbMapProviderToken,
		"DbMapProviderUsedQuotas":  fnTableQueryPermissionForDbMapProviderUsedQuotas,
		"DbMapProviderTokenUsage":  fnTableQueryPermissionForDbMapProviderTokenUsage,
		"DbMapCacheRoadmapIndex":   fnTableQueryPermissionWithLoginCheck,
		"DbMapCacheSatelliteIndex": fnTableQueryPermissionWithLoginCheck,
		"DbMapCacheHybridIndex":    fnTableQueryPermissionWithLoginCheck,
	}

	protoDbRpcManager := service.NewTconnectrpcProtoDbSrvHandlerImpl(
		fnGetCrudDb,
		fnCrudPermission,
		fnTableQueryPermission,
	)
	return protodb.NewProtoDbSrvHandler(protoDbRpcManager, opts...)
}
