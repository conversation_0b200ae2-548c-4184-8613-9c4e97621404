package rpc

import (
	"bfmap/config"
	"context"

	"log/slog"

	connect "connectrpc.com/connect"
)

type logInterceptor struct{}

func NewLogInterceptor() *logInterceptor {
	return &logInterceptor{}
}

func (i *logInterceptor) WrapUnary(next connect.UnaryFunc) connect.UnaryFunc {
	return connect.UnaryFunc(func(
		ctx context.Context,
		req connect.AnyRequest,
	) (connect.AnyResponse, error) {
		if !config.IsVerboseDebugRpc {
			return next(ctx, req)
		}

		// log the request and response
		slog.Debug(
			"rpc unary request",
			"path", req.Spec().Procedure,
			"request", req,
		)

		resp, err := next(ctx, req)
		slog.Debug(
			"rpc unary response",
			"path", req.Spec().Procedure,
			"response", resp,
			"error", err,
		)
		return resp, err
	})
}

// for streaming client, need to log inside the handler
func (i *logInterceptor) WrapStreamingClient(next connect.StreamingClientFunc) connect.StreamingClientFunc {
	return connect.StreamingClientFunc(func(
		ctx context.Context,
		spec connect.Spec,
	) connect.StreamingClientConn {
		if !config.IsVerboseDebugRpc {
			return next(ctx, spec)
		}

		conn := next(ctx, spec)
		slog.Debug(
			"rpc streaming client ",
			"path", spec.Procedure,
			"peer", conn.Peer(),
		)
		return conn
	})
}

// for streaming handler, need to log inside the handler
// because the request and response are not available at this point
func (i *logInterceptor) WrapStreamingHandler(next connect.StreamingHandlerFunc) connect.StreamingHandlerFunc {
	return connect.StreamingHandlerFunc(func(
		ctx context.Context,
		conn connect.StreamingHandlerConn,
	) error {
		if !config.IsVerboseDebugRpc {
			return next(ctx, conn)
		}
		// log the request and response
		slog.Debug(
			"rpc streaming handler start",
			"path", conn.Spec().Procedure,
			"request", conn,
		)

		err := next(ctx, conn)
		slog.Debug(
			"rpc streaming handler end",
			"error", err,
		)
		return err
	})
}
