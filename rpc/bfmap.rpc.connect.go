// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: bfmap.rpc.proto

package rpc

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// bfmapName is the fully-qualified name of the bfmap service.
	bfmapName = "rpc.bfmap"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BfmapIsSetupProcedure is the fully-qualified name of the bfmap's IsSetup RPC.
	BfmapIsSetupProcedure = "/rpc.bfmap/IsSetup"
	// BfmapSetupProcedure is the fully-qualified name of the bfmap's Setup RPC.
	BfmapSetupProcedure = "/rpc.bfmap/Setup"
	// BfmapLoginProcedure is the fully-qualified name of the bfmap's Login RPC.
	BfmapLoginProcedure = "/rpc.bfmap/Login"
	// BfmapLogoutProcedure is the fully-qualified name of the bfmap's Logout RPC.
	BfmapLogoutProcedure = "/rpc.bfmap/Logout"
	// BfmapPingProcedure is the fully-qualified name of the bfmap's Ping RPC.
	BfmapPingProcedure = "/rpc.bfmap/Ping"
	// BfmapCreateUserProcedure is the fully-qualified name of the bfmap's CreateUser RPC.
	BfmapCreateUserProcedure = "/rpc.bfmap/CreateUser"
	// BfmapSetProviderTokenProcedure is the fully-qualified name of the bfmap's SetProviderToken RPC.
	BfmapSetProviderTokenProcedure = "/rpc.bfmap/SetProviderToken"
	// BfmapSetProjectProcedure is the fully-qualified name of the bfmap's SetProject RPC.
	BfmapSetProjectProcedure = "/rpc.bfmap/SetProject"
	// BfmapSetProjectTokenProcedure is the fully-qualified name of the bfmap's SetProjectToken RPC.
	BfmapSetProjectTokenProcedure = "/rpc.bfmap/SetProjectToken"
	// BfmapCreateTempMapProjectTokenProcedure is the fully-qualified name of the bfmap's
	// CreateTempMapProjectToken RPC.
	BfmapCreateTempMapProjectTokenProcedure = "/rpc.bfmap/CreateTempMapProjectToken"
	// BfmapDeleteMapCacheIndexesProcedure is the fully-qualified name of the bfmap's
	// DeleteMapCacheIndexes RPC.
	BfmapDeleteMapCacheIndexesProcedure = "/rpc.bfmap/DeleteMapCacheIndexes"
	// BfmapGetCurrentUsageProcedure is the fully-qualified name of the bfmap's GetCurrentUsage RPC.
	BfmapGetCurrentUsageProcedure = "/rpc.bfmap/GetCurrentUsage"
	// BfmapUpdateDbUserProcedure is the fully-qualified name of the bfmap's UpdateDbUser RPC.
	BfmapUpdateDbUserProcedure = "/rpc.bfmap/UpdateDbUser"
)

// BfmapClient is a client for the rpc.bfmap service.
type BfmapClient interface {
	// is server has setup，no session id required
	// Common.Code: 0: setup, !0: not setup
	IsSetup(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error)
	// Setup to setup the admin account,no session id required
	// Common.Code: 0: success, !0: fail, may refer to Common.Reason, see CommonRespCode
	Setup(context.Context, *connect.Request[SetupReq]) (*connect.Response[Common], error)
	// Login to login the server,no session id required
	Login(context.Context, *connect.Request[LoginReq]) (*connect.Response[LoginResp], error)
	// Logout to logout the server, use "Session-Id" in header to identify the user
	// Common.Code: 0: success, !0: fail, may refer to Common.Reason, see CommonRespCode
	Logout(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error)
	// Ping to keep the session alive,every hour
	// Common.Code: 0: success, !0: fail, see CommonRespCode
	Ping(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error)
	CreateUser(context.Context, *connect.Request[CreateUserReq]) (*connect.Response[Common], error)
	// 1:create
	// 2:update
	// 3:rotate token,create new one and mark old one status as "delete"
	// only for token, new one return in Common.Body
	// 4:delete, mark old one as "delete"
	// Common.Body is DbMapProviderToken
	// in resp,Common.Code: 0: success, !0: fail, see CommonRespCode
	SetProviderToken(context.Context, *connect.Request[SetProviderTokenReq]) (*connect.Response[Common], error)
	SetProject(context.Context, *connect.Request[SetProjectReq]) (*connect.Response[Common], error)
	// Common.Msg is DbProjectToken, code is same as SetProviderToken
	// **ProjectToken Rid and ProjectRid should be in SetProjectReq**
	SetProjectToken(context.Context, *connect.Request[SetProjectTokenReq]) (*connect.Response[Common], error)
	// in resp,Common.Code: 0: success, !0: fail, see CommonRespCode
	// result in Common.Msg is token value
	CreateTempMapProjectToken(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error)
	DeleteMapCacheIndexes(context.Context, *connect.Request[DeleteMapCacheIndexesReq]) (*connect.Response[Common], error)
	GetCurrentUsage(context.Context, *connect.Request[GetCurrentUsageReq]) (*connect.Response[GetCurrentUsageResp], error)
	// Update a DbUser
	// Common.Code: 0: success, !0: fail, see CommonRespCode
	UpdateDbUser(context.Context, *connect.Request[UpdateDbUserReq]) (*connect.Response[Common], error)
}

// NewBfmapClient constructs a client for the rpc.bfmap service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBfmapClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BfmapClient {
	baseURL = strings.TrimRight(baseURL, "/")
	bfmapMethods := File_bfmap_rpc_proto.Services().ByName("bfmap").Methods()
	return &bfmapClient{
		isSetup: connect.NewClient[Empty, Common](
			httpClient,
			baseURL+BfmapIsSetupProcedure,
			connect.WithSchema(bfmapMethods.ByName("IsSetup")),
			connect.WithClientOptions(opts...),
		),
		setup: connect.NewClient[SetupReq, Common](
			httpClient,
			baseURL+BfmapSetupProcedure,
			connect.WithSchema(bfmapMethods.ByName("Setup")),
			connect.WithClientOptions(opts...),
		),
		login: connect.NewClient[LoginReq, LoginResp](
			httpClient,
			baseURL+BfmapLoginProcedure,
			connect.WithSchema(bfmapMethods.ByName("Login")),
			connect.WithClientOptions(opts...),
		),
		logout: connect.NewClient[Empty, Common](
			httpClient,
			baseURL+BfmapLogoutProcedure,
			connect.WithSchema(bfmapMethods.ByName("Logout")),
			connect.WithClientOptions(opts...),
		),
		ping: connect.NewClient[Empty, Common](
			httpClient,
			baseURL+BfmapPingProcedure,
			connect.WithSchema(bfmapMethods.ByName("Ping")),
			connect.WithClientOptions(opts...),
		),
		createUser: connect.NewClient[CreateUserReq, Common](
			httpClient,
			baseURL+BfmapCreateUserProcedure,
			connect.WithSchema(bfmapMethods.ByName("CreateUser")),
			connect.WithClientOptions(opts...),
		),
		setProviderToken: connect.NewClient[SetProviderTokenReq, Common](
			httpClient,
			baseURL+BfmapSetProviderTokenProcedure,
			connect.WithSchema(bfmapMethods.ByName("SetProviderToken")),
			connect.WithClientOptions(opts...),
		),
		setProject: connect.NewClient[SetProjectReq, Common](
			httpClient,
			baseURL+BfmapSetProjectProcedure,
			connect.WithSchema(bfmapMethods.ByName("SetProject")),
			connect.WithClientOptions(opts...),
		),
		setProjectToken: connect.NewClient[SetProjectTokenReq, Common](
			httpClient,
			baseURL+BfmapSetProjectTokenProcedure,
			connect.WithSchema(bfmapMethods.ByName("SetProjectToken")),
			connect.WithClientOptions(opts...),
		),
		createTempMapProjectToken: connect.NewClient[Empty, Common](
			httpClient,
			baseURL+BfmapCreateTempMapProjectTokenProcedure,
			connect.WithSchema(bfmapMethods.ByName("CreateTempMapProjectToken")),
			connect.WithClientOptions(opts...),
		),
		deleteMapCacheIndexes: connect.NewClient[DeleteMapCacheIndexesReq, Common](
			httpClient,
			baseURL+BfmapDeleteMapCacheIndexesProcedure,
			connect.WithSchema(bfmapMethods.ByName("DeleteMapCacheIndexes")),
			connect.WithClientOptions(opts...),
		),
		getCurrentUsage: connect.NewClient[GetCurrentUsageReq, GetCurrentUsageResp](
			httpClient,
			baseURL+BfmapGetCurrentUsageProcedure,
			connect.WithSchema(bfmapMethods.ByName("GetCurrentUsage")),
			connect.WithClientOptions(opts...),
		),
		updateDbUser: connect.NewClient[UpdateDbUserReq, Common](
			httpClient,
			baseURL+BfmapUpdateDbUserProcedure,
			connect.WithSchema(bfmapMethods.ByName("UpdateDbUser")),
			connect.WithClientOptions(opts...),
		),
	}
}

// bfmapClient implements BfmapClient.
type bfmapClient struct {
	isSetup                   *connect.Client[Empty, Common]
	setup                     *connect.Client[SetupReq, Common]
	login                     *connect.Client[LoginReq, LoginResp]
	logout                    *connect.Client[Empty, Common]
	ping                      *connect.Client[Empty, Common]
	createUser                *connect.Client[CreateUserReq, Common]
	setProviderToken          *connect.Client[SetProviderTokenReq, Common]
	setProject                *connect.Client[SetProjectReq, Common]
	setProjectToken           *connect.Client[SetProjectTokenReq, Common]
	createTempMapProjectToken *connect.Client[Empty, Common]
	deleteMapCacheIndexes     *connect.Client[DeleteMapCacheIndexesReq, Common]
	getCurrentUsage           *connect.Client[GetCurrentUsageReq, GetCurrentUsageResp]
	updateDbUser              *connect.Client[UpdateDbUserReq, Common]
}

// IsSetup calls rpc.bfmap.IsSetup.
func (c *bfmapClient) IsSetup(ctx context.Context, req *connect.Request[Empty]) (*connect.Response[Common], error) {
	return c.isSetup.CallUnary(ctx, req)
}

// Setup calls rpc.bfmap.Setup.
func (c *bfmapClient) Setup(ctx context.Context, req *connect.Request[SetupReq]) (*connect.Response[Common], error) {
	return c.setup.CallUnary(ctx, req)
}

// Login calls rpc.bfmap.Login.
func (c *bfmapClient) Login(ctx context.Context, req *connect.Request[LoginReq]) (*connect.Response[LoginResp], error) {
	return c.login.CallUnary(ctx, req)
}

// Logout calls rpc.bfmap.Logout.
func (c *bfmapClient) Logout(ctx context.Context, req *connect.Request[Empty]) (*connect.Response[Common], error) {
	return c.logout.CallUnary(ctx, req)
}

// Ping calls rpc.bfmap.Ping.
func (c *bfmapClient) Ping(ctx context.Context, req *connect.Request[Empty]) (*connect.Response[Common], error) {
	return c.ping.CallUnary(ctx, req)
}

// CreateUser calls rpc.bfmap.CreateUser.
func (c *bfmapClient) CreateUser(ctx context.Context, req *connect.Request[CreateUserReq]) (*connect.Response[Common], error) {
	return c.createUser.CallUnary(ctx, req)
}

// SetProviderToken calls rpc.bfmap.SetProviderToken.
func (c *bfmapClient) SetProviderToken(ctx context.Context, req *connect.Request[SetProviderTokenReq]) (*connect.Response[Common], error) {
	return c.setProviderToken.CallUnary(ctx, req)
}

// SetProject calls rpc.bfmap.SetProject.
func (c *bfmapClient) SetProject(ctx context.Context, req *connect.Request[SetProjectReq]) (*connect.Response[Common], error) {
	return c.setProject.CallUnary(ctx, req)
}

// SetProjectToken calls rpc.bfmap.SetProjectToken.
func (c *bfmapClient) SetProjectToken(ctx context.Context, req *connect.Request[SetProjectTokenReq]) (*connect.Response[Common], error) {
	return c.setProjectToken.CallUnary(ctx, req)
}

// CreateTempMapProjectToken calls rpc.bfmap.CreateTempMapProjectToken.
func (c *bfmapClient) CreateTempMapProjectToken(ctx context.Context, req *connect.Request[Empty]) (*connect.Response[Common], error) {
	return c.createTempMapProjectToken.CallUnary(ctx, req)
}

// DeleteMapCacheIndexes calls rpc.bfmap.DeleteMapCacheIndexes.
func (c *bfmapClient) DeleteMapCacheIndexes(ctx context.Context, req *connect.Request[DeleteMapCacheIndexesReq]) (*connect.Response[Common], error) {
	return c.deleteMapCacheIndexes.CallUnary(ctx, req)
}

// GetCurrentUsage calls rpc.bfmap.GetCurrentUsage.
func (c *bfmapClient) GetCurrentUsage(ctx context.Context, req *connect.Request[GetCurrentUsageReq]) (*connect.Response[GetCurrentUsageResp], error) {
	return c.getCurrentUsage.CallUnary(ctx, req)
}

// UpdateDbUser calls rpc.bfmap.UpdateDbUser.
func (c *bfmapClient) UpdateDbUser(ctx context.Context, req *connect.Request[UpdateDbUserReq]) (*connect.Response[Common], error) {
	return c.updateDbUser.CallUnary(ctx, req)
}

// BfmapHandler is an implementation of the rpc.bfmap service.
type BfmapHandler interface {
	// is server has setup，no session id required
	// Common.Code: 0: setup, !0: not setup
	IsSetup(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error)
	// Setup to setup the admin account,no session id required
	// Common.Code: 0: success, !0: fail, may refer to Common.Reason, see CommonRespCode
	Setup(context.Context, *connect.Request[SetupReq]) (*connect.Response[Common], error)
	// Login to login the server,no session id required
	Login(context.Context, *connect.Request[LoginReq]) (*connect.Response[LoginResp], error)
	// Logout to logout the server, use "Session-Id" in header to identify the user
	// Common.Code: 0: success, !0: fail, may refer to Common.Reason, see CommonRespCode
	Logout(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error)
	// Ping to keep the session alive,every hour
	// Common.Code: 0: success, !0: fail, see CommonRespCode
	Ping(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error)
	CreateUser(context.Context, *connect.Request[CreateUserReq]) (*connect.Response[Common], error)
	// 1:create
	// 2:update
	// 3:rotate token,create new one and mark old one status as "delete"
	// only for token, new one return in Common.Body
	// 4:delete, mark old one as "delete"
	// Common.Body is DbMapProviderToken
	// in resp,Common.Code: 0: success, !0: fail, see CommonRespCode
	SetProviderToken(context.Context, *connect.Request[SetProviderTokenReq]) (*connect.Response[Common], error)
	SetProject(context.Context, *connect.Request[SetProjectReq]) (*connect.Response[Common], error)
	// Common.Msg is DbProjectToken, code is same as SetProviderToken
	// **ProjectToken Rid and ProjectRid should be in SetProjectReq**
	SetProjectToken(context.Context, *connect.Request[SetProjectTokenReq]) (*connect.Response[Common], error)
	// in resp,Common.Code: 0: success, !0: fail, see CommonRespCode
	// result in Common.Msg is token value
	CreateTempMapProjectToken(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error)
	DeleteMapCacheIndexes(context.Context, *connect.Request[DeleteMapCacheIndexesReq]) (*connect.Response[Common], error)
	GetCurrentUsage(context.Context, *connect.Request[GetCurrentUsageReq]) (*connect.Response[GetCurrentUsageResp], error)
	// Update a DbUser
	// Common.Code: 0: success, !0: fail, see CommonRespCode
	UpdateDbUser(context.Context, *connect.Request[UpdateDbUserReq]) (*connect.Response[Common], error)
}

// NewBfmapHandler builds an HTTP handler from the service implementation. It returns the path on
// which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBfmapHandler(svc BfmapHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	bfmapMethods := File_bfmap_rpc_proto.Services().ByName("bfmap").Methods()
	bfmapIsSetupHandler := connect.NewUnaryHandler(
		BfmapIsSetupProcedure,
		svc.IsSetup,
		connect.WithSchema(bfmapMethods.ByName("IsSetup")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapSetupHandler := connect.NewUnaryHandler(
		BfmapSetupProcedure,
		svc.Setup,
		connect.WithSchema(bfmapMethods.ByName("Setup")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapLoginHandler := connect.NewUnaryHandler(
		BfmapLoginProcedure,
		svc.Login,
		connect.WithSchema(bfmapMethods.ByName("Login")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapLogoutHandler := connect.NewUnaryHandler(
		BfmapLogoutProcedure,
		svc.Logout,
		connect.WithSchema(bfmapMethods.ByName("Logout")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapPingHandler := connect.NewUnaryHandler(
		BfmapPingProcedure,
		svc.Ping,
		connect.WithSchema(bfmapMethods.ByName("Ping")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapCreateUserHandler := connect.NewUnaryHandler(
		BfmapCreateUserProcedure,
		svc.CreateUser,
		connect.WithSchema(bfmapMethods.ByName("CreateUser")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapSetProviderTokenHandler := connect.NewUnaryHandler(
		BfmapSetProviderTokenProcedure,
		svc.SetProviderToken,
		connect.WithSchema(bfmapMethods.ByName("SetProviderToken")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapSetProjectHandler := connect.NewUnaryHandler(
		BfmapSetProjectProcedure,
		svc.SetProject,
		connect.WithSchema(bfmapMethods.ByName("SetProject")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapSetProjectTokenHandler := connect.NewUnaryHandler(
		BfmapSetProjectTokenProcedure,
		svc.SetProjectToken,
		connect.WithSchema(bfmapMethods.ByName("SetProjectToken")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapCreateTempMapProjectTokenHandler := connect.NewUnaryHandler(
		BfmapCreateTempMapProjectTokenProcedure,
		svc.CreateTempMapProjectToken,
		connect.WithSchema(bfmapMethods.ByName("CreateTempMapProjectToken")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapDeleteMapCacheIndexesHandler := connect.NewUnaryHandler(
		BfmapDeleteMapCacheIndexesProcedure,
		svc.DeleteMapCacheIndexes,
		connect.WithSchema(bfmapMethods.ByName("DeleteMapCacheIndexes")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapGetCurrentUsageHandler := connect.NewUnaryHandler(
		BfmapGetCurrentUsageProcedure,
		svc.GetCurrentUsage,
		connect.WithSchema(bfmapMethods.ByName("GetCurrentUsage")),
		connect.WithHandlerOptions(opts...),
	)
	bfmapUpdateDbUserHandler := connect.NewUnaryHandler(
		BfmapUpdateDbUserProcedure,
		svc.UpdateDbUser,
		connect.WithSchema(bfmapMethods.ByName("UpdateDbUser")),
		connect.WithHandlerOptions(opts...),
	)
	return "/rpc.bfmap/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BfmapIsSetupProcedure:
			bfmapIsSetupHandler.ServeHTTP(w, r)
		case BfmapSetupProcedure:
			bfmapSetupHandler.ServeHTTP(w, r)
		case BfmapLoginProcedure:
			bfmapLoginHandler.ServeHTTP(w, r)
		case BfmapLogoutProcedure:
			bfmapLogoutHandler.ServeHTTP(w, r)
		case BfmapPingProcedure:
			bfmapPingHandler.ServeHTTP(w, r)
		case BfmapCreateUserProcedure:
			bfmapCreateUserHandler.ServeHTTP(w, r)
		case BfmapSetProviderTokenProcedure:
			bfmapSetProviderTokenHandler.ServeHTTP(w, r)
		case BfmapSetProjectProcedure:
			bfmapSetProjectHandler.ServeHTTP(w, r)
		case BfmapSetProjectTokenProcedure:
			bfmapSetProjectTokenHandler.ServeHTTP(w, r)
		case BfmapCreateTempMapProjectTokenProcedure:
			bfmapCreateTempMapProjectTokenHandler.ServeHTTP(w, r)
		case BfmapDeleteMapCacheIndexesProcedure:
			bfmapDeleteMapCacheIndexesHandler.ServeHTTP(w, r)
		case BfmapGetCurrentUsageProcedure:
			bfmapGetCurrentUsageHandler.ServeHTTP(w, r)
		case BfmapUpdateDbUserProcedure:
			bfmapUpdateDbUserHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBfmapHandler returns CodeUnimplemented from all methods.
type UnimplementedBfmapHandler struct{}

func (UnimplementedBfmapHandler) IsSetup(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.IsSetup is not implemented"))
}

func (UnimplementedBfmapHandler) Setup(context.Context, *connect.Request[SetupReq]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.Setup is not implemented"))
}

func (UnimplementedBfmapHandler) Login(context.Context, *connect.Request[LoginReq]) (*connect.Response[LoginResp], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.Login is not implemented"))
}

func (UnimplementedBfmapHandler) Logout(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.Logout is not implemented"))
}

func (UnimplementedBfmapHandler) Ping(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.Ping is not implemented"))
}

func (UnimplementedBfmapHandler) CreateUser(context.Context, *connect.Request[CreateUserReq]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.CreateUser is not implemented"))
}

func (UnimplementedBfmapHandler) SetProviderToken(context.Context, *connect.Request[SetProviderTokenReq]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.SetProviderToken is not implemented"))
}

func (UnimplementedBfmapHandler) SetProject(context.Context, *connect.Request[SetProjectReq]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.SetProject is not implemented"))
}

func (UnimplementedBfmapHandler) SetProjectToken(context.Context, *connect.Request[SetProjectTokenReq]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.SetProjectToken is not implemented"))
}

func (UnimplementedBfmapHandler) CreateTempMapProjectToken(context.Context, *connect.Request[Empty]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.CreateTempMapProjectToken is not implemented"))
}

func (UnimplementedBfmapHandler) DeleteMapCacheIndexes(context.Context, *connect.Request[DeleteMapCacheIndexesReq]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.DeleteMapCacheIndexes is not implemented"))
}

func (UnimplementedBfmapHandler) GetCurrentUsage(context.Context, *connect.Request[GetCurrentUsageReq]) (*connect.Response[GetCurrentUsageResp], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.GetCurrentUsage is not implemented"))
}

func (UnimplementedBfmapHandler) UpdateDbUser(context.Context, *connect.Request[UpdateDbUserReq]) (*connect.Response[Common], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("rpc.bfmap.UpdateDbUser is not implemented"))
}
