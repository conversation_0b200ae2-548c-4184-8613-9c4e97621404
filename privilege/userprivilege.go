package bfprivilege

import (
	"bfmap/db"
	"bfmap/dbproto"
	"log/slog"
	"time"

	"github.com/maypok86/otter"
)

// [user rid] => *dbproto.DbUserPrivilege
var UserPrivilegeCache *otter.Cache[string, *dbproto.DbUserPrivilege]

func init() {
	c, err := otter.MustBuilder[string, *dbproto.DbUserPrivilege](1000).
		WithTTL(24 * time.Hour).
		Cost(func(key string, value *dbproto.DbUserPrivilege) uint32 {
			return 1
		}).
		Build()
	if err != nil {
		panic("InitUserPrivilegeCache create cache fail" + err.Error())
	}
	UserPrivilegeCache = &c
}

func GetUserPrivilege(userRid string) *dbproto.DbUserPrivilege {
	if v, ok := UserPrivilegeCache.Get(userRid); ok {
		return v
	}

	privilege, err := db.GetDbUserPrivilegeByUserRid(userRid)
	if err != nil {
		slog.Warn("cannot get user privilege in db", "error", err)
		return nil
	}
	SetUserPrivilegeCache(userRid, privilege)

	return privilege
}

func SetUserPrivilegeCache(userRid string, privilege *dbproto.DbUserPrivilege) {
	UserPrivilegeCache.Set(userRid, privilege)
}

func DeleteUserPrivilegeCache(userRid string) {
	UserPrivilegeCache.Delete(userRid)
}

func IsUserCanModifyOtherUser(userRid string) bool {
	privilege := GetUserPrivilege(userRid)
	if privilege == nil {
		return false
	}
	return privilege.CanModifyOtherUser
}
