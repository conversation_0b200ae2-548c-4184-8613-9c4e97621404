[web]
www-dir = web
http-port = 4288
; https-port = 0
; ssl-key和ssl-cert表示文件名，文件放在 ssl 目录下
; ssl-key = ssl.key
; ssl-cert = ssl.crt
; ssl-update-token = t2bfdxnet2235

[database]
db-dir = db
; 图片缓存占用磁盘空间的限制，单位GB;
; 注意：数据库除了图片缓存外，还有一部分磁盘空间用于存储其他数据，实际占用磁盘空间大小可能会比这个值大，建议设置为期望值的80%。
image-cache-size = 100
; 系统中被删除的数据（projectToken，providerToken，project等）都只是标记为已删除，这个值表示这些数据的保留时间，超过这个时间的数据会被彻底删除，不包含图片缓存，单位天，
; 每天检测一次，<=0 则不检测，永不删除。
deleted-data-ttl = 0

[nats]
; 使用 nats://<host>:<port> 连接nats的监听端口
nats-port = 4289
;使用 nats-leaf://<host>:<port>连接的监听端口，用于叶子节点连接,0 表示关闭
leaf-node-port = 0
; 验证token，可以为空，用于nats鉴权
auth-token =
; 是否作为叶子节点,true 则 leaf-node-port 无效
as-leaf-node = false
; 作为叶子节点时，连接的远程nats服务地址，<host>:<port>
nats-leaf-server-url = t2.bfdx.net:7422
; 作为叶子节点时，连接的远程nats服务的token
leaf-server-auth-token =
; 是否向nats请求map瓦片数据,默认为true
nats-request-map-tile = true
;nats request是等待nats响应的超时时间，单位秒，默认3
nats-wait-timeout = 3

[log]
; 是否开启日志
log-to-file = false
; 日志保存目录
log-dir = log

[proxy]
;socks5://[username:password@]host:port
socks5 = **********:20800
;http://[username:password@]host:port
http =

[map]
; 地图服务默认BaseUrl，可为空，向地图提供商请求瓦片数据时使用，如果provider token中设置了BaseUrl，优先使用provider token中的BaseUrl
google-static-base-url = https://maps.googleapis.com
google-tile-base-url = https://tile.googleapis.com
osm-base-url = https://tile.openstreetmap.org
tianditu-base-url = https://t1.tianditu.gov.cn