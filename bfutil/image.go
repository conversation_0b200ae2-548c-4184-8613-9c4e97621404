package bfutil

import (
	"bytes"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"

	"github.com/anthonynsimon/bild/transform"
)

func CropImageCenter(img image.Image, width, height int) image.Image {
	rect := img.Bounds()
	moveX := rect.Min.X + (rect.Dx()-width)/2
	moveY := rect.Min.Y + (rect.Dy()-height)/2
	intersect := rect.Intersect(image.Rect(0, 0, width, height).Add(image.Point{X: moveX, Y: moveY}))
	return transform.Crop(img, intersect)
}

func ConvertImage2Bytes(img image.Image, format string) ([]byte, error) {
	var err error
	buf := new(bytes.Buffer)

	switch format {
	case "png":
		err = png.Encode(buf, img)
	case "jpg", "jpeg", "jpg-baseline":
		err = jpeg.Encode(buf, img, &jpeg.Options{Quality: 80})
	default:
		err = fmt.Errorf("unsupported image format: %s", format)
	}
	return buf.Bytes(), err
}
