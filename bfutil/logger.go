package bfutil

import (
	"log/slog"
	"os"
	"path/filepath"

	"github.com/phuslu/log"
)

const LogTimeFormat = "01-02 15:04:05.000"

func InitLoggerWithFileHandler(level log.Level, logToFile bool, dir string) {
	writer := &log.MultiEntryWriter{
		&log.ConsoleWriter{ColorOutput: true, Writer: os.Stdout},
	}
	if logToFile {
		if !IsFileExist(dir) {
			_ = os.Mkdir<PERSON>ll(dir, 0o755)
		}
		writer = &log.MultiEntryWriter{
			&log.ConsoleWriter{
				ColorOutput: true,
				Writer:      os.Stdout,
			},
			&log.FileWriter{
				Filename:     filepath.Join(dir, "bfmap.log"),
				MaxBackups:   100,
				EnsureFolder: true,
				MaxSize:      100 << 20,
			},
		}
	}

	logger := log.Logger{
		Level:  level,
		Caller: 2,
		Writer: writer,
	}

	log.DefaultLogger = logger
	slog.SetDefault(logger.Slog())
}
