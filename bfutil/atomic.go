package bfutil

// wrapper for sync/atomic package

import (
	"sync/atomic"
	"time"
)

func NewAtomicTime(t time.Time) *AtomicTime {
	at := AtomicTime{
		Value: atomic.Value{},
	}

	at.Store(t)
	return &at
}

type AtomicTime struct {
	atomic.Value
}

func (t *AtomicTime) Load() time.Time {
	return t.Value.Load().(time.Time)
}

func (t *AtomicTime) Store(v time.Time) {
	t.Value.Store(v)
}

// Swap atomically replaces the current time.Time with the new value and returns the old value.
// If called on a nil pointer, it returns the zero time.Time value.
func (t *AtomicTime) Swap(v time.Time) time.Time {
	if t == nil {
		return time.Time{}
	}
	return t.Value.Swap(v).(time.Time)
}

// CompareAndSwap atomically swaps the current time.Time with the new value
// if the current value equals the old value. Returns true if the swap happened.
// If called on a nil pointer, it returns false.
func (t *AtomicTime) CompareAndSwap(old, new time.Time) bool {
	if t == nil {
		return false
	}
	return t.Value.CompareAndSwap(old, new)
}
