package bfutil

import (
	"context"
	"log/slog"
	"net"
	"net/http"
	"net/url"
	"time"

	"golang.org/x/net/proxy"
)

func CreateProxyClientForSocks5(socks5Proxy *url.URL, timeout time.Duration) *http.Client {
	dialer, err := proxy.SOCKS5("tcp", socks5Proxy.Host, nil, proxy.Direct)
	if err != nil {
		slog.Warn("create socks5 dialer failed", "error", err)
		return nil
	}

	return &http.Client{
		Transport: &http.Transport{
			DialContext: dialer.(interface {
				DialContext(ctx context.Context, network, addr string) (net.Conn, error)
			}).DialContext,
		},
		Timeout: timeout,
	}
}

func CreateProxyClientForHttp(httpProxy *url.URL, timeout time.Duration) *http.Client {
	return &http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyURL(httpProxy),
		},
		Timeout: timeout,
	}
}
