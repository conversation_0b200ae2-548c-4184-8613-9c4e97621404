package config

import (
	"net/url"
	"path/filepath"
)

const AdminRid = "11111111-1111-1111-1111-111111111111"

const BfTImeFormat = "2006-01-02 15:04:05"
const BfSessionIdHeaderName = "Session-Id"

// verbose debug.
var IsVerboseDebug bool = false
var IsVerboseDebugRpc bool = false
var IsVerboseDebugMap bool = false

var HttpPort int = 8080
var HttpsPort int = 0
var SSLKeyFileName string = "ssl.key"
var SSLCertFileName string = " ssl.crt"

// nolint
var SSLUpdateToken string = "t2bfdxnet2235"

const SSLPath = "ssl"

var SSLKeyFilePath string = filepath.Join(SSLPath, SSLKeyFileName)
var SSLCertFilePath string = filepath.Join(SSLPath, SSLCertFileName)

// web dir.
var WebDir string = "web"

// db dir.
var DbDir string = "db"

// image cache size, unit Bytes, <=0 no limit.
var ImageCacheSize int64 = 0

// ttl for deleted data in db, <=0 for no ttl limit.
var DbDeletedDataTTL int = 0

// nats port.
var NatsServerPort int = 4222
var NatsAuthToken string = ""
var NatsLeafNodePort int = 0
var NatsAsLeafNode bool = false
var NatsLeafNodeServerUrl string = ""
var NatsLeafNodeServerAuthToken string = ""
var NatsRequestMapTile bool = true
var NatsWaitTimeout int = 3

// max 10M.
const NatsMaxPayload int = 10 * 1024 * 1024

// log to file.
var IsLogToFile bool = false

// log dir.
var LogDir string = "log"

var Socks5Proxy *url.URL = nil
var HttpProxy *url.URL = nil
