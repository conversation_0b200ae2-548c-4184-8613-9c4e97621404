package main

import (
	"flag"
	"log/slog"
	"net/url"
	"os"

	"bfmap"
	"bfmap/bfnats"
	"bfmap/bfutil"
	"bfmap/config"
	"bfmap/db"
	"bfmap/maps"
	"bfmap/os_signal"
	"bfmap/session"
	"bfmap/web"

	"github.com/nats-io/nats-server/v2/server"
	"github.com/phuslu/log"
)

func main() {
	debugMode := flag.Bool("debug", false, "Enable debug mode")
	debugModeRpc := flag.Bool("debugRpc", false, "Enable debug mode for RPC")
	debugModeMap := flag.Bool("debugMap", false, "Enable debug mode for map")
	flag.Parse()
	config.IsVerboseDebug = *debugMode
	config.IsVerboseDebugRpc = *debugModeRpc
	config.IsVerboseDebugMap = *debugModeMap

	println("bfmap version:", bfmap.Version)

	// Initialize log
	logLevel := log.InfoLevel
	if config.IsVerboseDebug {
		logLevel = log.DebugLevel
	}
	bfutil.InitLoggerWithFileHandler(
		logLevel,
		config.IsLogToFile,
		config.LogDir,
	)

	// Initialize the configuration.
	err := bfmap.InitBfMapConfig()
	if err != nil {
		log.Fatal().Err(err).Msg("InitBfMapConfig failed")
	}

	err = db.InitDb()
	if err != nil {
		log.Fatal().Err(err).Msg("InitDb failed")
	}
	defer db.CloseDbConn()

	// connect to the key-value db
	_, err = db.Connect2KeyValueDb()
	if err != nil {
		log.Fatal().Err(err).Msg("Connect2KeyValueDb failed")
	}
	defer db.CloseKeyValueDb()

	leafNodeOpt := server.LeafNodeOpts{}
	if config.NatsAsLeafNode {
		u := &url.URL{
			Scheme: "nats-leaf",
			Host:   config.NatsLeafNodeServerUrl,
		}
		if len(config.NatsLeafNodeServerAuthToken) > 0 {
			u.User = url.User(config.NatsLeafNodeServerAuthToken)
		}
		leafNodeOpt.Remotes = []*server.RemoteLeafOpts{
			{
				URLs: []*url.URL{
					u,
				},
			},
		}
	} else {
		if config.NatsLeafNodePort > 0 {
			leafNodeOpt.Port = config.NatsLeafNodePort
		}
	}

	// Start embedded NATS server
	err = bfnats.StartNatsServer(
		config.NatsServerPort,
		config.NatsMaxPayload,
		config.NatsAuthToken,
		leafNodeOpt)
	if err != nil {
		log.Fatal().Err(err).Msg("StartNatsServer failed")
	}
	defer bfnats.StopNatsServer()

	err = bfnats.SetupDefaultNatsClient()
	if err != nil {
		slog.Error("SetupDefaultNatsClient fail", "err", err)
	}
	defer bfnats.CloseDefaultNatsClient()

	mapNatSub, err := maps.MapSubscribeNats()
	if err != nil {
		slog.Warn("MapSubscribeNats fail", "err", err)
	} else {
		defer mapNatSub.Unsubscribe()
	}

	// Initialize the global map token manager.
	maps.GlobalMapsManager.Init()
	defer maps.GlobalMapsManager.Close()

	go session.CheckIfLoginSessionExpiredLoop()

	if config.ImageCacheSize > 0 {
		go maps.CheckAndDeleteMapCacheLoop()
	}

	if config.DbDeletedDataTTL > 0 {
		go db.CheckAndDeleteDbDateWithStatus8Loop()
	}

	shutdownCh := make(chan string)

	// Start the web server.
	go func() {
		slog.Info("WebServe start", "www-dir", config.WebDir, "port", config.HttpPort)
		web.WebServe(config.HttpPort, config.WebDir)
		shutdownCh <- "web server exit"
	}()

	// Monitor for exit signals.
	go func() {
		signalChan := make(chan os.Signal, 1)
		os_signal.MonitorExitSignal(signalChan)

		signalVal := <-signalChan
		shutdownCh <- "signal:" + signalVal.String()
	}()

	reason := <-shutdownCh

	slog.Info("bfmap exit", "reason", reason)
}
