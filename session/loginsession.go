package session

import (
	"bfmap/bfutil"
	"bfmap/dbproto"
	"log/slog"
	"sync"
	"time"
)

// [session id] => *UserLoginInfo.
var userLoginSessionMap = sync.Map{}

func CheckIfLoginSessionExpiredLoop() {
	for {
		time.Sleep(30 * time.Minute)

		userLoginSessionMap.Range(func(key, value any) bool {
			info := value.(*UserSession)
			// 2h没有访问或者session过期，删除当前登录的session，不改变数据库值
			if time.Since(info.LastAccessTime).Hours() > 2 || info.IsExpired() {
				userLoginSessionMap.Delete(info.SessionId)
				slog.Info("delete User session", "info", info)
			}
			return true
		})
	}
}

type UserSession struct {
	sync.Mutex

	//session Rid
	Rid        string
	UserName   string
	UserRid    string
	CreaterRid string
	OrgRid     string
	SessionId  string

	ExpireTime     time.Time
	LastAccessTime time.Time
	DbSession      *dbproto.DbUserSession
}

func (i *UserSession) IsExpired() bool {
	return i.ExpireTime.Before(bfutil.CurrentUTCTime())
}

// NewUserLoginInfo create new user login info and save to global map.
func NewUserLoginInfo(user *dbproto.DbUser, session *dbproto.DbUserSession) *UserSession {
	info := &UserSession{
		Rid:            session.Rid,
		UserName:       user.Name,
		UserRid:        user.Rid,
		OrgRid:         user.Org,
		CreaterRid:     user.Creater,
		SessionId:      session.SessionId,
		ExpireTime:     time.Unix(session.ExpireTime, 0),
		LastAccessTime: time.Now(),
		DbSession:      session,
	}

	userLoginSessionMap.Store(session.SessionId, info)

	return info
}

func GetUserLoginInfo(sessionId string) *UserSession {
	info, ok := userLoginSessionMap.Load(sessionId)
	if !ok {
		return nil
	}

	i := info.(*UserSession)

	i.Lock()
	defer i.Unlock()
	i.LastAccessTime = time.Now()
	return i
}

func DeleteUserLoginInfo(sessionId string) {
	userLoginSessionMap.Delete(sessionId)
}

func HasSessionIdLogin(sessionId string) bool {
	if len(sessionId) <= 0 {
		return false
	}

	s := GetUserLoginInfo(sessionId)
	if s == nil {
		return false
	}

	if s.IsExpired() {
		go DeleteUserLoginInfo(sessionId)
		return false
	}

	return true
}

// DeleteUserLoginInfoByUserRid deletes all user sessions for a specific user ID
func DeleteUserLoginInfoByUserRid(userRid string) {
	userLoginSessionMap.Range(func(key, value any) bool {
		info := value.(*UserSession)
		if info.UserRid == userRid {
			userLoginSessionMap.Delete(info.SessionId)
			slog.Info("delete user session due to user being disabled", "userRid", userRid, "sessionId", info.SessionId)
		}
		return true
	})
}
