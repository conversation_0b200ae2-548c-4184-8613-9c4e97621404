// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: db.proto

package dbproto

import (
	_ "github.com/ygrpc/protodb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MapProviderEnum int32

const (
	MapProviderEnum_ProviderGoogle                 MapProviderEnum = 0
	MapProviderEnum_ProviderTianditu               MapProviderEnum = 1
	MapProviderEnum_ProviderOSM                    MapProviderEnum = 2
	MapProviderEnum_ProviderGoogleLocalDirectory   MapProviderEnum = 3
	MapProviderEnum_ProviderTiandituLocalDirectory MapProviderEnum = 4
	MapProviderEnum_ProviderOSMLocalDirectory      MapProviderEnum = 5
)

// Enum value maps for MapProviderEnum.
var (
	MapProviderEnum_name = map[int32]string{
		0: "ProviderGoogle",
		1: "ProviderTianditu",
		2: "ProviderOSM",
		3: "ProviderGoogleLocalDirectory",
		4: "ProviderTiandituLocalDirectory",
		5: "ProviderOSMLocalDirectory",
	}
	MapProviderEnum_value = map[string]int32{
		"ProviderGoogle":                 0,
		"ProviderTianditu":               1,
		"ProviderOSM":                    2,
		"ProviderGoogleLocalDirectory":   3,
		"ProviderTiandituLocalDirectory": 4,
		"ProviderOSMLocalDirectory":      5,
	}
)

func (x MapProviderEnum) Enum() *MapProviderEnum {
	p := new(MapProviderEnum)
	*p = x
	return p
}

func (x MapProviderEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MapProviderEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_db_proto_enumTypes[0].Descriptor()
}

func (MapProviderEnum) Type() protoreflect.EnumType {
	return &file_db_proto_enumTypes[0]
}

func (x MapProviderEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MapProviderEnum.Descriptor instead.
func (MapProviderEnum) EnumDescriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{0}
}

type GoogleMapApiEnum int32

const (
	GoogleMapApiEnum_GoogleMapApiStatic GoogleMapApiEnum = 0
	GoogleMapApiEnum_GoogleMapApiTile   GoogleMapApiEnum = 1
	GoogleMapApiEnum_GoogleMapApiAll    GoogleMapApiEnum = 2
)

// Enum value maps for GoogleMapApiEnum.
var (
	GoogleMapApiEnum_name = map[int32]string{
		0: "GoogleMapApiStatic",
		1: "GoogleMapApiTile",
		2: "GoogleMapApiAll",
	}
	GoogleMapApiEnum_value = map[string]int32{
		"GoogleMapApiStatic": 0,
		"GoogleMapApiTile":   1,
		"GoogleMapApiAll":    2,
	}
)

func (x GoogleMapApiEnum) Enum() *GoogleMapApiEnum {
	p := new(GoogleMapApiEnum)
	*p = x
	return p
}

func (x GoogleMapApiEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoogleMapApiEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_db_proto_enumTypes[1].Descriptor()
}

func (GoogleMapApiEnum) Type() protoreflect.EnumType {
	return &file_db_proto_enumTypes[1]
}

func (x GoogleMapApiEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GoogleMapApiEnum.Descriptor instead.
func (GoogleMapApiEnum) EnumDescriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{1}
}

// DbOrg
type DbOrg struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid string `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,3,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	// note
	Note string `protobuf:"bytes,4,opt,name=Note,proto3" json:"Note,omitempty"`
	// setting
	Setting string `protobuf:"bytes,5,opt,name=Setting,proto3" json:"Setting,omitempty"`
	// owner: user rid
	OwnerRid      string `protobuf:"bytes,6,opt,name=OwnerRid,proto3" json:"OwnerRid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbOrg) Reset() {
	*x = DbOrg{}
	mi := &file_db_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbOrg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbOrg) ProtoMessage() {}

func (x *DbOrg) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbOrg.ProtoReflect.Descriptor instead.
func (*DbOrg) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{0}
}

func (x *DbOrg) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbOrg) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DbOrg) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *DbOrg) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *DbOrg) GetSetting() string {
	if x != nil {
		return x.Setting
	}
	return ""
}

func (x *DbOrg) GetOwnerRid() string {
	if x != nil {
		return x.OwnerRid
	}
	return ""
}

type DbUser struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid string `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	// org
	Org string `protobuf:"bytes,2,opt,name=Org,proto3" json:"Org,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`
	// nickname
	Nickname string `protobuf:"bytes,4,opt,name=Nickname,proto3" json:"Nickname,omitempty"`
	// email
	Email string `protobuf:"bytes,5,opt,name=Email,proto3" json:"Email,omitempty"`
	// phone
	Phone string `protobuf:"bytes,6,opt,name=Phone,proto3" json:"Phone,omitempty"`
	// password, base64(sha256(Name+Password))
	Password string `protobuf:"bytes,7,opt,name=Password,proto3" json:"Password,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,8,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	// creater uuid
	Creater string `protobuf:"bytes,9,opt,name=Creater,proto3" json:"Creater,omitempty"`
	// note
	Note string `protobuf:"bytes,10,opt,name=Note,proto3" json:"Note,omitempty"`
	// setting
	Setting string `protobuf:"bytes,11,opt,name=Setting,proto3" json:"Setting,omitempty"`
	// disabled
	Disabled      bool `protobuf:"varint,12,opt,name=Disabled,proto3" json:"Disabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbUser) Reset() {
	*x = DbUser{}
	mi := &file_db_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbUser) ProtoMessage() {}

func (x *DbUser) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbUser.ProtoReflect.Descriptor instead.
func (*DbUser) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{1}
}

func (x *DbUser) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbUser) GetOrg() string {
	if x != nil {
		return x.Org
	}
	return ""
}

func (x *DbUser) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DbUser) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *DbUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *DbUser) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *DbUser) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *DbUser) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *DbUser) GetCreater() string {
	if x != nil {
		return x.Creater
	}
	return ""
}

func (x *DbUser) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *DbUser) GetSetting() string {
	if x != nil {
		return x.Setting
	}
	return ""
}

func (x *DbUser) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

type DbUserPrivilege struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Rid                string                 `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	UserRid            string                 `protobuf:"bytes,2,opt,name=UserRid,proto3" json:"UserRid,omitempty"`
	UpdateTime         int64                  `protobuf:"varint,3,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty"`
	CanModifyOtherUser bool                   `protobuf:"varint,4,opt,name=CanModifyOtherUser,proto3" json:"CanModifyOtherUser,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *DbUserPrivilege) Reset() {
	*x = DbUserPrivilege{}
	mi := &file_db_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbUserPrivilege) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbUserPrivilege) ProtoMessage() {}

func (x *DbUserPrivilege) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbUserPrivilege.ProtoReflect.Descriptor instead.
func (*DbUserPrivilege) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{2}
}

func (x *DbUserPrivilege) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbUserPrivilege) GetUserRid() string {
	if x != nil {
		return x.UserRid
	}
	return ""
}

func (x *DbUserPrivilege) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *DbUserPrivilege) GetCanModifyOtherUser() bool {
	if x != nil {
		return x.CanModifyOtherUser
	}
	return false
}

// user login session
type DbUserSession struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rid           string                 `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	UserRid       string                 `protobuf:"bytes,2,opt,name=UserRid,proto3" json:"UserRid,omitempty"`
	SessionId     string                 `protobuf:"bytes,3,opt,name=SessionId,proto3" json:"SessionId,omitempty"`
	UpdateTime    int64                  `protobuf:"varint,4,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty"`
	ExpireTime    int64                  `protobuf:"varint,5,opt,name=ExpireTime,proto3" json:"ExpireTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbUserSession) Reset() {
	*x = DbUserSession{}
	mi := &file_db_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbUserSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbUserSession) ProtoMessage() {}

func (x *DbUserSession) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbUserSession.ProtoReflect.Descriptor instead.
func (*DbUserSession) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{3}
}

func (x *DbUserSession) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbUserSession) GetUserRid() string {
	if x != nil {
		return x.UserRid
	}
	return ""
}

func (x *DbUserSession) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *DbUserSession) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *DbUserSession) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

// user project
type DbProject struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid string `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	// project user rid
	UserRid string `protobuf:"bytes,2,opt,name=UserRid,proto3" json:"UserRid,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,4,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	// note
	Note string `protobuf:"bytes,5,opt,name=Note,proto3" json:"Note,omitempty"`
	// setting
	Setting string `protobuf:"bytes,6,opt,name=Setting,proto3" json:"Setting,omitempty"`
	// status, 1:active 4:disable 8:deleted
	Status int32 `protobuf:"varint,7,opt,name=Status,proto3" json:"Status,omitempty"`
	// for clean usage
	DeleteTime    int64 `protobuf:"varint,8,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbProject) Reset() {
	*x = DbProject{}
	mi := &file_db_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbProject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbProject) ProtoMessage() {}

func (x *DbProject) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbProject.ProtoReflect.Descriptor instead.
func (*DbProject) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{4}
}

func (x *DbProject) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbProject) GetUserRid() string {
	if x != nil {
		return x.UserRid
	}
	return ""
}

func (x *DbProject) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DbProject) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *DbProject) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *DbProject) GetSetting() string {
	if x != nil {
		return x.Setting
	}
	return ""
}

func (x *DbProject) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DbProject) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

// TAvailableMapProvider
type TAvailableMapProvider struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 在http请求参数`provider`为空时使用的provider
	DefaultRoadmap   MapProviderEnum `protobuf:"varint,1,opt,name=DefaultRoadmap,proto3,enum=dbproto.MapProviderEnum" json:"DefaultRoadmap,omitempty"`
	DefaultSatellite MapProviderEnum `protobuf:"varint,2,opt,name=DefaultSatellite,proto3,enum=dbproto.MapProviderEnum" json:"DefaultSatellite,omitempty"`
	DefaultHybrid    MapProviderEnum `protobuf:"varint,3,opt,name=DefaultHybrid,proto3,enum=dbproto.MapProviderEnum" json:"DefaultHybrid,omitempty"`
	// google
	Google bool `protobuf:"varint,4,opt,name=Google,proto3" json:"Google,omitempty"`
	// tianditu
	Tianditu bool `protobuf:"varint,5,opt,name=Tianditu,proto3" json:"Tianditu,omitempty"`
	// osm
	OSM           bool `protobuf:"varint,6,opt,name=OSM,proto3" json:"OSM,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TAvailableMapProvider) Reset() {
	*x = TAvailableMapProvider{}
	mi := &file_db_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TAvailableMapProvider) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TAvailableMapProvider) ProtoMessage() {}

func (x *TAvailableMapProvider) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TAvailableMapProvider.ProtoReflect.Descriptor instead.
func (*TAvailableMapProvider) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{5}
}

func (x *TAvailableMapProvider) GetDefaultRoadmap() MapProviderEnum {
	if x != nil {
		return x.DefaultRoadmap
	}
	return MapProviderEnum_ProviderGoogle
}

func (x *TAvailableMapProvider) GetDefaultSatellite() MapProviderEnum {
	if x != nil {
		return x.DefaultSatellite
	}
	return MapProviderEnum_ProviderGoogle
}

func (x *TAvailableMapProvider) GetDefaultHybrid() MapProviderEnum {
	if x != nil {
		return x.DefaultHybrid
	}
	return MapProviderEnum_ProviderGoogle
}

func (x *TAvailableMapProvider) GetGoogle() bool {
	if x != nil {
		return x.Google
	}
	return false
}

func (x *TAvailableMapProvider) GetTianditu() bool {
	if x != nil {
		return x.Tianditu
	}
	return false
}

func (x *TAvailableMapProvider) GetOSM() bool {
	if x != nil {
		return x.OSM
	}
	return false
}

type SysNames struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Names         []string               `protobuf:"bytes,1,rep,name=Names,proto3" json:"Names,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SysNames) Reset() {
	*x = SysNames{}
	mi := &file_db_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SysNames) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SysNames) ProtoMessage() {}

func (x *SysNames) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SysNames.ProtoReflect.Descriptor instead.
func (*SysNames) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{6}
}

func (x *SysNames) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

// project token
type DbProjectToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid string `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	// project rid
	ProjectRid string `protobuf:"bytes,2,opt,name=ProjectRid,proto3" json:"ProjectRid,omitempty"`
	Name       string `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`
	// token
	Token string `protobuf:"bytes,4,opt,name=Token,proto3" json:"Token,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,5,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	// note
	Note string `protobuf:"bytes,6,opt,name=Note,proto3" json:"Note,omitempty"`
	// setting
	Setting string `protobuf:"bytes,7,opt,name=Setting,proto3" json:"Setting,omitempty"`
	// expire time unix utc, 0:forever
	ExpireTime int64 `protobuf:"varint,8,opt,name=ExpireTime,proto3" json:"ExpireTime,omitempty"`
	// status, 1:active 4:disable, 8:deleted
	Status int32 `protobuf:"varint,9,opt,name=Status,proto3" json:"Status,omitempty"`
	// available map provider jsonb of TAvailableMapProvider
	AvailableMapProvider string `protobuf:"bytes,10,opt,name=AvailableMapProvider,proto3" json:"AvailableMapProvider,omitempty"`
	// for clean usage
	DeleteTime int64 `protobuf:"varint,11,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty"`
	// sys name, specify which system can access this token
	SysName       *SysNames `protobuf:"bytes,12,opt,name=SysName,proto3" json:"SysName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbProjectToken) Reset() {
	*x = DbProjectToken{}
	mi := &file_db_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbProjectToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbProjectToken) ProtoMessage() {}

func (x *DbProjectToken) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbProjectToken.ProtoReflect.Descriptor instead.
func (*DbProjectToken) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{7}
}

func (x *DbProjectToken) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbProjectToken) GetProjectRid() string {
	if x != nil {
		return x.ProjectRid
	}
	return ""
}

func (x *DbProjectToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DbProjectToken) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DbProjectToken) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *DbProjectToken) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *DbProjectToken) GetSetting() string {
	if x != nil {
		return x.Setting
	}
	return ""
}

func (x *DbProjectToken) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *DbProjectToken) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DbProjectToken) GetAvailableMapProvider() string {
	if x != nil {
		return x.AvailableMapProvider
	}
	return ""
}

func (x *DbProjectToken) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

func (x *DbProjectToken) GetSysName() *SysNames {
	if x != nil {
		return x.SysName
	}
	return nil
}

// project quotas
type DbProjectQuotas struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7 same as DbProject rid
	Rid string `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	// quotas type 1:per minute 2:per hour 3:per day 4:per month
	QuotasType int32 `protobuf:"varint,2,opt,name=QuotasType,proto3" json:"QuotasType,omitempty"`
	// quotas limit 0=unlimited
	QuotasLimit int32 `protobuf:"varint,3,opt,name=QuotasLimit,proto3" json:"QuotasLimit,omitempty"`
	// count start time
	CountStartTime int64 `protobuf:"varint,4,opt,name=CountStartTime,proto3" json:"CountStartTime,omitempty"`
	// current quotas
	CurrentUsedQuotas int32 `protobuf:"varint,5,opt,name=CurrentUsedQuotas,proto3" json:"CurrentUsedQuotas,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DbProjectQuotas) Reset() {
	*x = DbProjectQuotas{}
	mi := &file_db_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbProjectQuotas) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbProjectQuotas) ProtoMessage() {}

func (x *DbProjectQuotas) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbProjectQuotas.ProtoReflect.Descriptor instead.
func (*DbProjectQuotas) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{8}
}

func (x *DbProjectQuotas) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbProjectQuotas) GetQuotasType() int32 {
	if x != nil {
		return x.QuotasType
	}
	return 0
}

func (x *DbProjectQuotas) GetQuotasLimit() int32 {
	if x != nil {
		return x.QuotasLimit
	}
	return 0
}

func (x *DbProjectQuotas) GetCountStartTime() int64 {
	if x != nil {
		return x.CountStartTime
	}
	return 0
}

func (x *DbProjectQuotas) GetCurrentUsedQuotas() int32 {
	if x != nil {
		return x.CurrentUsedQuotas
	}
	return 0
}

// map project token usage statistics
type DbProjectTokenUsage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid string `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	// project rid
	ProjectRid string `protobuf:"bytes,2,opt,name=ProjectRid,proto3" json:"ProjectRid,omitempty"`
	// token
	Token string `protobuf:"bytes,3,opt,name=Token,proto3" json:"Token,omitempty"`
	// start time
	StartTime int64 `protobuf:"varint,4,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	// end time
	EndTime int64 `protobuf:"varint,5,opt,name=EndTime,proto3" json:"EndTime,omitempty"`
	// usage count, request success times
	UsageCount    int32 `protobuf:"varint,6,opt,name=UsageCount,proto3" json:"UsageCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbProjectTokenUsage) Reset() {
	*x = DbProjectTokenUsage{}
	mi := &file_db_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbProjectTokenUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbProjectTokenUsage) ProtoMessage() {}

func (x *DbProjectTokenUsage) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbProjectTokenUsage.ProtoReflect.Descriptor instead.
func (*DbProjectTokenUsage) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{9}
}

func (x *DbProjectTokenUsage) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbProjectTokenUsage) GetProjectRid() string {
	if x != nil {
		return x.ProjectRid
	}
	return ""
}

func (x *DbProjectTokenUsage) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DbProjectTokenUsage) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *DbProjectTokenUsage) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *DbProjectTokenUsage) GetUsageCount() int32 {
	if x != nil {
		return x.UsageCount
	}
	return 0
}

// map provider token
type DbMapProviderToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid      string          `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	Provider MapProviderEnum `protobuf:"varint,2,opt,name=Provider,proto3,enum=dbproto.MapProviderEnum" json:"Provider,omitempty"`
	// user rid
	UserRid string `protobuf:"bytes,3,opt,name=UserRid,proto3" json:"UserRid,omitempty"`
	Name    string `protobuf:"bytes,4,opt,name=Name,proto3" json:"Name,omitempty"`
	// token
	// for osm, it pass token in url as query string "tk=xxx"
	Token string `protobuf:"bytes,5,opt,name=Token,proto3" json:"Token,omitempty"`
	// zoom level range, 0:no limit
	MinZoom int32 `protobuf:"varint,6,opt,name=MinZoom,proto3" json:"MinZoom,omitempty"`
	MaxZoom int32 `protobuf:"varint,7,opt,name=MaxZoom,proto3" json:"MaxZoom,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,8,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	// note
	Note string `protobuf:"bytes,9,opt,name=Note,proto3" json:"Note,omitempty"`
	// expire time unix utc,0:forever
	ExpireTime int64 `protobuf:"varint,10,opt,name=ExpireTime,proto3" json:"ExpireTime,omitempty"`
	// setting
	Setting string `protobuf:"bytes,11,opt,name=Setting,proto3" json:"Setting,omitempty"`
	// quotas type  3:per day 4:per month
	QuotasType int32 `protobuf:"varint,12,opt,name=QuotasType,proto3" json:"QuotasType,omitempty"`
	// quotas  0=unlimited
	QuotasLimit int32 `protobuf:"varint,13,opt,name=QuotasLimit,proto3" json:"QuotasLimit,omitempty"`
	// status, 1:active 4:disable,8:deleted
	Status int32 `protobuf:"varint,14,opt,name=Status,proto3" json:"Status,omitempty"`
	// use which google map api to get tile
	GoogleMapApi GoogleMapApiEnum `protobuf:"varint,15,opt,name=GoogleMapApi,proto3,enum=dbproto.GoogleMapApiEnum" json:"GoogleMapApi,omitempty"`
	Priority     int32            `protobuf:"varint,16,opt,name=Priority,proto3" json:"Priority,omitempty"`
	// for clean usage
	DeleteTime int64 `protobuf:"varint,17,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty"`
	// custom base url in request
	// for google map static api, it replace the default base url "https://maps.googleapis.com"
	// for google map tile api, it replace the default base url "https://tile.googleapis.com"
	// for tianditu map api, it replace the default base url "https://t0.tianditu.com"
	// for osm map api, it replace the default base url "https://tile.openstreetmap.org"
	// for ProviderGoogleLocalDirectory, this is the local directory path, the file path is like "satellite/{z}/{x}/{y}.jpg"
	// or "hybrid/{z}/{x}/{y}.jpg" or "roadmap/{z}/{x}/{y}.png"
	// ProviderTiandituLocalDirectory and ProviderOSMLocalDirectory are the same as ProviderGoogleLocalDirectory
	BaseUrl       string `protobuf:"bytes,18,opt,name=BaseUrl,proto3" json:"BaseUrl,omitempty"`
	IsUseProxy    bool   `protobuf:"varint,19,opt,name=IsUseProxy,proto3" json:"IsUseProxy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbMapProviderToken) Reset() {
	*x = DbMapProviderToken{}
	mi := &file_db_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbMapProviderToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbMapProviderToken) ProtoMessage() {}

func (x *DbMapProviderToken) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbMapProviderToken.ProtoReflect.Descriptor instead.
func (*DbMapProviderToken) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{10}
}

func (x *DbMapProviderToken) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbMapProviderToken) GetProvider() MapProviderEnum {
	if x != nil {
		return x.Provider
	}
	return MapProviderEnum_ProviderGoogle
}

func (x *DbMapProviderToken) GetUserRid() string {
	if x != nil {
		return x.UserRid
	}
	return ""
}

func (x *DbMapProviderToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DbMapProviderToken) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DbMapProviderToken) GetMinZoom() int32 {
	if x != nil {
		return x.MinZoom
	}
	return 0
}

func (x *DbMapProviderToken) GetMaxZoom() int32 {
	if x != nil {
		return x.MaxZoom
	}
	return 0
}

func (x *DbMapProviderToken) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *DbMapProviderToken) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *DbMapProviderToken) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *DbMapProviderToken) GetSetting() string {
	if x != nil {
		return x.Setting
	}
	return ""
}

func (x *DbMapProviderToken) GetQuotasType() int32 {
	if x != nil {
		return x.QuotasType
	}
	return 0
}

func (x *DbMapProviderToken) GetQuotasLimit() int32 {
	if x != nil {
		return x.QuotasLimit
	}
	return 0
}

func (x *DbMapProviderToken) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DbMapProviderToken) GetGoogleMapApi() GoogleMapApiEnum {
	if x != nil {
		return x.GoogleMapApi
	}
	return GoogleMapApiEnum_GoogleMapApiStatic
}

func (x *DbMapProviderToken) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *DbMapProviderToken) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

func (x *DbMapProviderToken) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *DbMapProviderToken) GetIsUseProxy() bool {
	if x != nil {
		return x.IsUseProxy
	}
	return false
}

// map provider used quotas
type DbMapProviderUsedQuotas struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7 same as DbMapProviderToken rid
	Rid string `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	// count start time
	CountStartTime int64 `protobuf:"varint,2,opt,name=CountStartTime,proto3" json:"CountStartTime,omitempty"`
	// current quotas
	UsedQuotas    int32 `protobuf:"varint,3,opt,name=UsedQuotas,proto3" json:"UsedQuotas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbMapProviderUsedQuotas) Reset() {
	*x = DbMapProviderUsedQuotas{}
	mi := &file_db_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbMapProviderUsedQuotas) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbMapProviderUsedQuotas) ProtoMessage() {}

func (x *DbMapProviderUsedQuotas) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbMapProviderUsedQuotas.ProtoReflect.Descriptor instead.
func (*DbMapProviderUsedQuotas) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{11}
}

func (x *DbMapProviderUsedQuotas) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbMapProviderUsedQuotas) GetCountStartTime() int64 {
	if x != nil {
		return x.CountStartTime
	}
	return 0
}

func (x *DbMapProviderUsedQuotas) GetUsedQuotas() int32 {
	if x != nil {
		return x.UsedQuotas
	}
	return 0
}

// map provider usage statistics
type DbMapProviderTokenUsage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid string `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	// provider rid
	ProviderRid string `protobuf:"bytes,2,opt,name=ProviderRid,proto3" json:"ProviderRid,omitempty"`
	StartTime   int64  `protobuf:"varint,3,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	// rotate time
	RotateTime int64 `protobuf:"varint,4,opt,name=RotateTime,proto3" json:"RotateTime,omitempty"`
	// usage count, request success times
	UsageCount    int32 `protobuf:"varint,5,opt,name=UsageCount,proto3" json:"UsageCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbMapProviderTokenUsage) Reset() {
	*x = DbMapProviderTokenUsage{}
	mi := &file_db_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbMapProviderTokenUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbMapProviderTokenUsage) ProtoMessage() {}

func (x *DbMapProviderTokenUsage) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbMapProviderTokenUsage.ProtoReflect.Descriptor instead.
func (*DbMapProviderTokenUsage) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{12}
}

func (x *DbMapProviderTokenUsage) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbMapProviderTokenUsage) GetProviderRid() string {
	if x != nil {
		return x.ProviderRid
	}
	return ""
}

func (x *DbMapProviderTokenUsage) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *DbMapProviderTokenUsage) GetRotateTime() int64 {
	if x != nil {
		return x.RotateTime
	}
	return 0
}

func (x *DbMapProviderTokenUsage) GetUsageCount() int32 {
	if x != nil {
		return x.UsageCount
	}
	return 0
}

// map cache roadmap index
type DbMapCacheRoadmapIndex struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid      string          `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	Provider MapProviderEnum `protobuf:"varint,2,opt,name=Provider,proto3,enum=dbproto.MapProviderEnum" json:"Provider,omitempty"`
	// language
	Language string `protobuf:"bytes,3,opt,name=Language,proto3" json:"Language,omitempty"`
	// tile x
	TileX int32 `protobuf:"varint,4,opt,name=TileX,proto3" json:"TileX,omitempty"`
	// tile y
	TileY int32 `protobuf:"varint,5,opt,name=TileY,proto3" json:"TileY,omitempty"`
	// tile z
	TileZ int32 `protobuf:"varint,6,opt,name=TileZ,proto3" json:"TileZ,omitempty"`
	// cache time
	CacheTime  int64 `protobuf:"varint,7,opt,name=CacheTime,proto3" json:"CacheTime,omitempty"`
	AccessTime int64 `protobuf:"varint,8,opt,name=AccessTime,proto3" json:"AccessTime,omitempty"`
	// tile image type 1:png 2:jpg
	TileImageFormat int32 `protobuf:"varint,9,opt,name=TileImageFormat,proto3" json:"TileImageFormat,omitempty"`
	// 128 bit xxHash hash, base64=key in kvstore
	TileHash string `protobuf:"bytes,10,opt,name=TileHash,proto3" json:"TileHash,omitempty"`
	Gcj02    int32  `protobuf:"varint,11,opt,name=Gcj02,proto3" json:"Gcj02,omitempty"`
	// status, 1:active 8:deleted
	Status        int32 `protobuf:"varint,12,opt,name=Status,proto3" json:"Status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbMapCacheRoadmapIndex) Reset() {
	*x = DbMapCacheRoadmapIndex{}
	mi := &file_db_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbMapCacheRoadmapIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbMapCacheRoadmapIndex) ProtoMessage() {}

func (x *DbMapCacheRoadmapIndex) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbMapCacheRoadmapIndex.ProtoReflect.Descriptor instead.
func (*DbMapCacheRoadmapIndex) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{13}
}

func (x *DbMapCacheRoadmapIndex) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbMapCacheRoadmapIndex) GetProvider() MapProviderEnum {
	if x != nil {
		return x.Provider
	}
	return MapProviderEnum_ProviderGoogle
}

func (x *DbMapCacheRoadmapIndex) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *DbMapCacheRoadmapIndex) GetTileX() int32 {
	if x != nil {
		return x.TileX
	}
	return 0
}

func (x *DbMapCacheRoadmapIndex) GetTileY() int32 {
	if x != nil {
		return x.TileY
	}
	return 0
}

func (x *DbMapCacheRoadmapIndex) GetTileZ() int32 {
	if x != nil {
		return x.TileZ
	}
	return 0
}

func (x *DbMapCacheRoadmapIndex) GetCacheTime() int64 {
	if x != nil {
		return x.CacheTime
	}
	return 0
}

func (x *DbMapCacheRoadmapIndex) GetAccessTime() int64 {
	if x != nil {
		return x.AccessTime
	}
	return 0
}

func (x *DbMapCacheRoadmapIndex) GetTileImageFormat() int32 {
	if x != nil {
		return x.TileImageFormat
	}
	return 0
}

func (x *DbMapCacheRoadmapIndex) GetTileHash() string {
	if x != nil {
		return x.TileHash
	}
	return ""
}

func (x *DbMapCacheRoadmapIndex) GetGcj02() int32 {
	if x != nil {
		return x.Gcj02
	}
	return 0
}

func (x *DbMapCacheRoadmapIndex) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// map cache satellite index
type DbMapCacheSatelliteIndex struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid      string          `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	Provider MapProviderEnum `protobuf:"varint,2,opt,name=Provider,proto3,enum=dbproto.MapProviderEnum" json:"Provider,omitempty"`
	// tile x
	TileX int32 `protobuf:"varint,3,opt,name=TileX,proto3" json:"TileX,omitempty"`
	// tile y
	TileY int32 `protobuf:"varint,4,opt,name=TileY,proto3" json:"TileY,omitempty"`
	// tile z
	TileZ int32 `protobuf:"varint,5,opt,name=TileZ,proto3" json:"TileZ,omitempty"`
	// cache time
	CacheTime  int64 `protobuf:"varint,6,opt,name=CacheTime,proto3" json:"CacheTime,omitempty"`
	AccessTime int64 `protobuf:"varint,7,opt,name=AccessTime,proto3" json:"AccessTime,omitempty"`
	// tile image type 1:png 2:jpg
	TileImageFormat int32 `protobuf:"varint,8,opt,name=TileImageFormat,proto3" json:"TileImageFormat,omitempty"`
	// 128 bit xxHash hash, base64=key in kvstore
	TileHash string `protobuf:"bytes,9,opt,name=TileHash,proto3" json:"TileHash,omitempty"`
	Gcj02    int32  `protobuf:"varint,10,opt,name=Gcj02,proto3" json:"Gcj02,omitempty"`
	// status, 1:active 8:deleted
	Status        int32 `protobuf:"varint,11,opt,name=Status,proto3" json:"Status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbMapCacheSatelliteIndex) Reset() {
	*x = DbMapCacheSatelliteIndex{}
	mi := &file_db_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbMapCacheSatelliteIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbMapCacheSatelliteIndex) ProtoMessage() {}

func (x *DbMapCacheSatelliteIndex) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbMapCacheSatelliteIndex.ProtoReflect.Descriptor instead.
func (*DbMapCacheSatelliteIndex) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{14}
}

func (x *DbMapCacheSatelliteIndex) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbMapCacheSatelliteIndex) GetProvider() MapProviderEnum {
	if x != nil {
		return x.Provider
	}
	return MapProviderEnum_ProviderGoogle
}

func (x *DbMapCacheSatelliteIndex) GetTileX() int32 {
	if x != nil {
		return x.TileX
	}
	return 0
}

func (x *DbMapCacheSatelliteIndex) GetTileY() int32 {
	if x != nil {
		return x.TileY
	}
	return 0
}

func (x *DbMapCacheSatelliteIndex) GetTileZ() int32 {
	if x != nil {
		return x.TileZ
	}
	return 0
}

func (x *DbMapCacheSatelliteIndex) GetCacheTime() int64 {
	if x != nil {
		return x.CacheTime
	}
	return 0
}

func (x *DbMapCacheSatelliteIndex) GetAccessTime() int64 {
	if x != nil {
		return x.AccessTime
	}
	return 0
}

func (x *DbMapCacheSatelliteIndex) GetTileImageFormat() int32 {
	if x != nil {
		return x.TileImageFormat
	}
	return 0
}

func (x *DbMapCacheSatelliteIndex) GetTileHash() string {
	if x != nil {
		return x.TileHash
	}
	return ""
}

func (x *DbMapCacheSatelliteIndex) GetGcj02() int32 {
	if x != nil {
		return x.Gcj02
	}
	return 0
}

func (x *DbMapCacheSatelliteIndex) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// map cache hybrid index
type DbMapCacheHybridIndex struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// rid uuidv7
	Rid      string          `protobuf:"bytes,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	Provider MapProviderEnum `protobuf:"varint,2,opt,name=Provider,proto3,enum=dbproto.MapProviderEnum" json:"Provider,omitempty"`
	// language
	Language string `protobuf:"bytes,3,opt,name=Language,proto3" json:"Language,omitempty"`
	// tile x
	TileX int32 `protobuf:"varint,4,opt,name=TileX,proto3" json:"TileX,omitempty"`
	// tile y
	TileY int32 `protobuf:"varint,5,opt,name=TileY,proto3" json:"TileY,omitempty"`
	// tile z
	TileZ int32 `protobuf:"varint,6,opt,name=TileZ,proto3" json:"TileZ,omitempty"`
	// cache time
	CacheTime  int64 `protobuf:"varint,7,opt,name=CacheTime,proto3" json:"CacheTime,omitempty"`
	AccessTime int64 `protobuf:"varint,8,opt,name=AccessTime,proto3" json:"AccessTime,omitempty"`
	// tile image type 1:png 2:jpg
	TileImageFormat int32 `protobuf:"varint,9,opt,name=TileImageFormat,proto3" json:"TileImageFormat,omitempty"`
	// 128 bit xxHash hash, base64=key in kvstore
	TileHash string `protobuf:"bytes,10,opt,name=TileHash,proto3" json:"TileHash,omitempty"`
	Gcj02    int32  `protobuf:"varint,11,opt,name=Gcj02,proto3" json:"Gcj02,omitempty"`
	// status, 1:active 8:deleted
	Status        int32 `protobuf:"varint,12,opt,name=Status,proto3" json:"Status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DbMapCacheHybridIndex) Reset() {
	*x = DbMapCacheHybridIndex{}
	mi := &file_db_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DbMapCacheHybridIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DbMapCacheHybridIndex) ProtoMessage() {}

func (x *DbMapCacheHybridIndex) ProtoReflect() protoreflect.Message {
	mi := &file_db_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DbMapCacheHybridIndex.ProtoReflect.Descriptor instead.
func (*DbMapCacheHybridIndex) Descriptor() ([]byte, []int) {
	return file_db_proto_rawDescGZIP(), []int{15}
}

func (x *DbMapCacheHybridIndex) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *DbMapCacheHybridIndex) GetProvider() MapProviderEnum {
	if x != nil {
		return x.Provider
	}
	return MapProviderEnum_ProviderGoogle
}

func (x *DbMapCacheHybridIndex) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *DbMapCacheHybridIndex) GetTileX() int32 {
	if x != nil {
		return x.TileX
	}
	return 0
}

func (x *DbMapCacheHybridIndex) GetTileY() int32 {
	if x != nil {
		return x.TileY
	}
	return 0
}

func (x *DbMapCacheHybridIndex) GetTileZ() int32 {
	if x != nil {
		return x.TileZ
	}
	return 0
}

func (x *DbMapCacheHybridIndex) GetCacheTime() int64 {
	if x != nil {
		return x.CacheTime
	}
	return 0
}

func (x *DbMapCacheHybridIndex) GetAccessTime() int64 {
	if x != nil {
		return x.AccessTime
	}
	return 0
}

func (x *DbMapCacheHybridIndex) GetTileImageFormat() int32 {
	if x != nil {
		return x.TileImageFormat
	}
	return 0
}

func (x *DbMapCacheHybridIndex) GetTileHash() string {
	if x != nil {
		return x.TileHash
	}
	return ""
}

func (x *DbMapCacheHybridIndex) GetGcj02() int32 {
	if x != nil {
		return x.Gcj02
	}
	return 0
}

func (x *DbMapCacheHybridIndex) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

var File_db_proto protoreflect.FileDescriptor

const file_db_proto_rawDesc = "" +
	"\n" +
	"\bdb.proto\x12\adbproto\x1a\rprotodb.proto\"\x86\x02\n" +
	"\x05DbOrg\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12\x12\n" +
	"\x04Name\x18\x02 \x01(\tR\x04Name\x12(\n" +
	"\n" +
	"CreateTime\x18\x03 \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"CreateTime\x12\x12\n" +
	"\x04Note\x18\x04 \x01(\tR\x04Note\x12$\n" +
	"\aSetting\x18\x05 \x01(\tB\n" +
	"\x82v\a2\x03 {}X\aR\aSetting\x12!\n" +
	"\bOwnerRid\x18\x06 \x01(\tB\x05\x82v\x02X\bR\bOwnerRid:G\x82vD*BCREATE INDEX IF NOT EXISTS idx_DbOrg_OwnerRid ON DbOrg (OwnerRid);\"\xd3\x03\n" +
	"\x06DbUser\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x128\n" +
	"\x03Org\x18\x02 \x01(\tB&\x82v#*\n" +
	"DbOrg(Rid):\x11ON DELETE CASCADEX\bh\x01R\x03Org\x12\x1b\n" +
	"\x04Name\x18\x03 \x01(\tB\a\x82v\x04\x18\x01 \x01R\x04Name\x12\x1a\n" +
	"\bNickname\x18\x04 \x01(\tR\bNickname\x12\x14\n" +
	"\x05Email\x18\x05 \x01(\tR\x05Email\x12\x14\n" +
	"\x05Phone\x18\x06 \x01(\tR\x05Phone\x12\x1a\n" +
	"\bPassword\x18\a \x01(\tR\bPassword\x12(\n" +
	"\n" +
	"CreateTime\x18\b \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"CreateTime\x12\x1f\n" +
	"\aCreater\x18\t \x01(\tB\x05\x82v\x02X\bR\aCreater\x12\x12\n" +
	"\x04Note\x18\n" +
	" \x01(\tR\x04Note\x12#\n" +
	"\aSetting\x18\v \x01(\tB\t\x82v\x062\x02{}X\aR\aSetting\x12&\n" +
	"\bDisabled\x18\f \x01(\bB\n" +
	"\x82v\a2\x05falseR\bDisabled:G\x82vD*BCREATE INDEX IF NOT EXISTS idx_DbUser_Creater ON DbUser (Creater);\"\xd5\x01\n" +
	"\x0fDbUserPrivilege\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12A\n" +
	"\aUserRid\x18\x02 \x01(\tB'\x82v$*\vDbUser(Rid):\x11ON DELETE CASCADEX\bh\x01R\aUserRid\x12(\n" +
	"\n" +
	"UpdateTime\x18\x03 \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"UpdateTime\x12:\n" +
	"\x12CanModifyOtherUser\x18\x04 \x01(\bB\n" +
	"\x82v\a2\x05falseR\x12CanModifyOtherUser\"\xe8\x01\n" +
	"\rDbUserSession\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12A\n" +
	"\aUserRid\x18\x02 \x01(\tB'\x82v$*\vDbUser(Rid):\x11ON DELETE CASCADEX\bh\x01R\aUserRid\x12%\n" +
	"\tSessionId\x18\x03 \x01(\tB\a\x82v\x04\x18\x01X\bR\tSessionId\x12(\n" +
	"\n" +
	"UpdateTime\x18\x04 \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"UpdateTime\x12(\n" +
	"\n" +
	"ExpireTime\x18\x05 \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"ExpireTime\"\xa3\x02\n" +
	"\tDbProject\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12A\n" +
	"\aUserRid\x18\x02 \x01(\tB'\x82v$*\vDbUser(Rid):\x11ON DELETE CASCADEX\bh\x01R\aUserRid\x12\x12\n" +
	"\x04Name\x18\x03 \x01(\tR\x04Name\x12(\n" +
	"\n" +
	"CreateTime\x18\x04 \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"CreateTime\x12\x12\n" +
	"\x04Note\x18\x05 \x01(\tR\x04Note\x12$\n" +
	"\aSetting\x18\x06 \x01(\tB\n" +
	"\x82v\a2\x03 {}X\aR\aSetting\x12\x16\n" +
	"\x06Status\x18\a \x01(\x05R\x06Status\x12(\n" +
	"\n" +
	"DeleteTime\x18\b \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"DeleteTime\"\xac\x02\n" +
	"\x15TAvailableMapProvider\x12@\n" +
	"\x0eDefaultRoadmap\x18\x01 \x01(\x0e2\x18.dbproto.MapProviderEnumR\x0eDefaultRoadmap\x12D\n" +
	"\x10DefaultSatellite\x18\x02 \x01(\x0e2\x18.dbproto.MapProviderEnumR\x10DefaultSatellite\x12>\n" +
	"\rDefaultHybrid\x18\x03 \x01(\x0e2\x18.dbproto.MapProviderEnumR\rDefaultHybrid\x12\x16\n" +
	"\x06Google\x18\x04 \x01(\bR\x06Google\x12\x1a\n" +
	"\bTianditu\x18\x05 \x01(\bR\bTianditu\x12\x10\n" +
	"\x03OSM\x18\x06 \x01(\bR\x03OSM:\x05\x82v\x028\x01\"'\n" +
	"\bSysNames\x12\x14\n" +
	"\x05Names\x18\x01 \x03(\tR\x05Names:\x05\x82v\x028\x01\"\xe7\x03\n" +
	"\x0eDbProjectToken\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12J\n" +
	"\n" +
	"ProjectRid\x18\x02 \x01(\tB*\x82v'*\x0eDbProject(Rid):\x11ON DELETE CASCADEX\bh\x01R\n" +
	"ProjectRid\x12\x12\n" +
	"\x04Name\x18\x03 \x01(\tR\x04Name\x12\x1d\n" +
	"\x05Token\x18\x04 \x01(\tB\a\x82v\x04\x18\x01X\bR\x05Token\x12(\n" +
	"\n" +
	"CreateTime\x18\x05 \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"CreateTime\x12\x12\n" +
	"\x04Note\x18\x06 \x01(\tR\x04Note\x12$\n" +
	"\aSetting\x18\a \x01(\tB\n" +
	"\x82v\a2\x03 {}X\aR\aSetting\x12(\n" +
	"\n" +
	"ExpireTime\x18\b \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"ExpireTime\x12\x16\n" +
	"\x06Status\x18\t \x01(\x05R\x06Status\x12>\n" +
	"\x14AvailableMapProvider\x18\n" +
	" \x01(\tB\n" +
	"\x82v\a2\x03 {}X\aR\x14AvailableMapProvider\x12(\n" +
	"\n" +
	"DeleteTime\x18\v \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"DeleteTime\x12+\n" +
	"\aSysName\x18\f \x01(\v2\x11.dbproto.SysNamesR\aSysName\"\x89\x02\n" +
	"\x0fDbProjectQuotas\x12<\n" +
	"\x03Rid\x18\x01 \x01(\tB*\x82v'\x10\x01*\x0eDbProject(Rid):\x11ON DELETE CASCADEX\bR\x03Rid\x12&\n" +
	"\n" +
	"QuotasType\x18\x02 \x01(\x05B\x06\x82v\x032\x014R\n" +
	"QuotasType\x12(\n" +
	"\vQuotasLimit\x18\x03 \x01(\x05B\x06\x82v\x032\x010R\vQuotasLimit\x120\n" +
	"\x0eCountStartTime\x18\x04 \x01(\x03B\b\x82v\x052\x010X\tR\x0eCountStartTime\x124\n" +
	"\x11CurrentUsedQuotas\x18\x05 \x01(\x05B\x06\x82v\x032\x010R\x11CurrentUsedQuotas\"\xfe\x01\n" +
	"\x13DbProjectTokenUsage\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12J\n" +
	"\n" +
	"ProjectRid\x18\x02 \x01(\tB*\x82v'*\x0eDbProject(Rid):\x11ON DELETE CASCADEX\bh\x01R\n" +
	"ProjectRid\x12\x14\n" +
	"\x05Token\x18\x03 \x01(\tR\x05Token\x12&\n" +
	"\tStartTime\x18\x04 \x01(\x03B\b\x82v\x052\x010X\tR\tStartTime\x12\"\n" +
	"\aEndTime\x18\x05 \x01(\x03B\b\x82v\x052\x010X\tR\aEndTime\x12\x1e\n" +
	"\n" +
	"UsageCount\x18\x06 \x01(\x05R\n" +
	"UsageCount\"\xf2\x05\n" +
	"\x12DbMapProviderToken\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x124\n" +
	"\bProvider\x18\x02 \x01(\x0e2\x18.dbproto.MapProviderEnumR\bProvider\x12A\n" +
	"\aUserRid\x18\x03 \x01(\tB'\x82v$*\vDbUser(Rid):\x11ON DELETE CASCADEX\bh\x01R\aUserRid\x12\x12\n" +
	"\x04Name\x18\x04 \x01(\tR\x04Name\x12\x14\n" +
	"\x05Token\x18\x05 \x01(\tR\x05Token\x12\"\n" +
	"\aMinZoom\x18\x06 \x01(\x05B\b\x82v\x05 \x012\x010R\aMinZoom\x12\"\n" +
	"\aMaxZoom\x18\a \x01(\x05B\b\x82v\x05 \x012\x010R\aMaxZoom\x12(\n" +
	"\n" +
	"CreateTime\x18\b \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"CreateTime\x12\x12\n" +
	"\x04Note\x18\t \x01(\tR\x04Note\x12(\n" +
	"\n" +
	"ExpireTime\x18\n" +
	" \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"ExpireTime\x12$\n" +
	"\aSetting\x18\v \x01(\tB\n" +
	"\x82v\a2\x03 {}X\aR\aSetting\x12&\n" +
	"\n" +
	"QuotasType\x18\f \x01(\x05B\x06\x82v\x032\x014R\n" +
	"QuotasType\x12(\n" +
	"\vQuotasLimit\x18\r \x01(\x05B\x06\x82v\x032\x010R\vQuotasLimit\x12\x16\n" +
	"\x06Status\x18\x0e \x01(\x05R\x06Status\x12E\n" +
	"\fGoogleMapApi\x18\x0f \x01(\x0e2\x19.dbproto.GoogleMapApiEnumB\x06\x82v\x032\x010R\fGoogleMapApi\x12\"\n" +
	"\bPriority\x18\x10 \x01(\x05B\x06\x82v\x032\x011R\bPriority\x12(\n" +
	"\n" +
	"DeleteTime\x18\x11 \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"DeleteTime\x12\x1d\n" +
	"\aBaseUrl\x18\x12 \x01(\tB\x03\x82v\x00R\aBaseUrl\x12*\n" +
	"\n" +
	"IsUseProxy\x18\x13 \x01(\bB\n" +
	"\x82v\a2\x05falseR\n" +
	"IsUseProxy\"\xb2\x01\n" +
	"\x17DbMapProviderUsedQuotas\x12E\n" +
	"\x03Rid\x18\x01 \x01(\tB3\x82v0\x10\x01*\x17DbMapProviderToken(Rid):\x11ON DELETE CASCADEX\bR\x03Rid\x120\n" +
	"\x0eCountStartTime\x18\x02 \x01(\x03B\b\x82v\x052\x010X\tR\x0eCountStartTime\x12\x1e\n" +
	"\n" +
	"UsedQuotas\x18\x03 \x01(\x05R\n" +
	"UsedQuotas\"\xfd\x01\n" +
	"\x17DbMapProviderTokenUsage\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12U\n" +
	"\vProviderRid\x18\x02 \x01(\tB3\x82v0*\x17DbMapProviderToken(Rid):\x11ON DELETE CASCADEX\bh\x01R\vProviderRid\x12&\n" +
	"\tStartTime\x18\x03 \x01(\x03B\b\x82v\x052\x010X\tR\tStartTime\x12(\n" +
	"\n" +
	"RotateTime\x18\x04 \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"RotateTime\x12\x1e\n" +
	"\n" +
	"UsageCount\x18\x05 \x01(\x05R\n" +
	"UsageCount\"\xbf\x06\n" +
	"\x16DbMapCacheRoadmapIndex\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12W\n" +
	"\bProvider\x18\x02 \x01(\x0e2\x18.dbproto.MapProviderEnumB!\x82v\x1e\x18\x01\x82\x01\x19MapCacheRoadmapIndexGroupR\bProvider\x12=\n" +
	"\bLanguage\x18\x03 \x01(\tB!\x82v\x1e\x18\x01\x82\x01\x19MapCacheRoadmapIndexGroupR\bLanguage\x127\n" +
	"\x05TileX\x18\x04 \x01(\x05B!\x82v\x1e\x18\x01\x82\x01\x19MapCacheRoadmapIndexGroupR\x05TileX\x127\n" +
	"\x05TileY\x18\x05 \x01(\x05B!\x82v\x1e\x18\x01\x82\x01\x19MapCacheRoadmapIndexGroupR\x05TileY\x127\n" +
	"\x05TileZ\x18\x06 \x01(\x05B!\x82v\x1e\x18\x01\x82\x01\x19MapCacheRoadmapIndexGroupR\x05TileZ\x12&\n" +
	"\tCacheTime\x18\a \x01(\x03B\b\x82v\x052\x010X\tR\tCacheTime\x12(\n" +
	"\n" +
	"AccessTime\x18\b \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"AccessTime\x12K\n" +
	"\x0fTileImageFormat\x18\t \x01(\x05B!\x82v\x1e\x18\x01\x82\x01\x19MapCacheRoadmapIndexGroupR\x0fTileImageFormat\x12\x1a\n" +
	"\bTileHash\x18\n" +
	" \x01(\tR\bTileHash\x12:\n" +
	"\x05Gcj02\x18\v \x01(\x05B$\x82v!\x18\x012\x010\x82\x01\x19MapCacheRoadmapIndexGroupR\x05Gcj02\x12 \n" +
	"\x06Status\x18\f \x01(\x05B\b\x82v\x052\x011X\tR\x06Status:\xad\x01\x82v\xa9\x01*\xa6\x01CREATE INDEX IF NOT EXISTS idx_DbMapCacheRoadmapIndex_deleteExpr_status_accesstime ON DbMapCacheRoadmapIndex ( (CASE WHEN Status = 8 THEN 1 ELSE 2 END), AccessTime );\"\x9e\x06\n" +
	"\x18DbMapCacheSatelliteIndex\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12[\n" +
	"\bProvider\x18\x02 \x01(\x0e2\x18.dbproto.MapProviderEnumB%\x82v\"\x18\x01\x82\x01\x1dDbMapCacheSatelliteIndexGroupR\bProvider\x12;\n" +
	"\x05TileX\x18\x03 \x01(\x05B%\x82v\"\x18\x01\x82\x01\x1dDbMapCacheSatelliteIndexGroupR\x05TileX\x12;\n" +
	"\x05TileY\x18\x04 \x01(\x05B%\x82v\"\x18\x01\x82\x01\x1dDbMapCacheSatelliteIndexGroupR\x05TileY\x12;\n" +
	"\x05TileZ\x18\x05 \x01(\x05B%\x82v\"\x18\x01\x82\x01\x1dDbMapCacheSatelliteIndexGroupR\x05TileZ\x12&\n" +
	"\tCacheTime\x18\x06 \x01(\x03B\b\x82v\x052\x010X\tR\tCacheTime\x12(\n" +
	"\n" +
	"AccessTime\x18\a \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"AccessTime\x12O\n" +
	"\x0fTileImageFormat\x18\b \x01(\x05B%\x82v\"\x18\x01\x82\x01\x1dDbMapCacheSatelliteIndexGroupR\x0fTileImageFormat\x12\x1a\n" +
	"\bTileHash\x18\t \x01(\tR\bTileHash\x12>\n" +
	"\x05Gcj02\x18\n" +
	" \x01(\x05B(\x82v%\x18\x012\x010\x82\x01\x1dDbMapCacheSatelliteIndexGroupR\x05Gcj02\x12 \n" +
	"\x06Status\x18\v \x01(\x05B\b\x82v\x052\x011X\tR\x06Status:\xb1\x01\x82v\xad\x01*\xaa\x01CREATE INDEX IF NOT EXISTS idx_DbMapCacheSatelliteIndex_deleteExpr_status_accesstime ON DbMapCacheSatelliteIndex ( (CASE WHEN Status = 8 THEN 1 ELSE 2 END), AccessTime );\"\xc0\x06\n" +
	"\x15DbMapCacheHybridIndex\x12\x19\n" +
	"\x03Rid\x18\x01 \x01(\tB\a\x82v\x04\x10\x01X\bR\x03Rid\x12X\n" +
	"\bProvider\x18\x02 \x01(\x0e2\x18.dbproto.MapProviderEnumB\"\x82v\x1f\x18\x01\x82\x01\x1aDbMapCacheHybridIndexGroupR\bProvider\x12>\n" +
	"\bLanguage\x18\x03 \x01(\tB\"\x82v\x1f\x18\x01\x82\x01\x1aDbMapCacheHybridIndexGroupR\bLanguage\x128\n" +
	"\x05TileX\x18\x04 \x01(\x05B\"\x82v\x1f\x18\x01\x82\x01\x1aDbMapCacheHybridIndexGroupR\x05TileX\x128\n" +
	"\x05TileY\x18\x05 \x01(\x05B\"\x82v\x1f\x18\x01\x82\x01\x1aDbMapCacheHybridIndexGroupR\x05TileY\x128\n" +
	"\x05TileZ\x18\x06 \x01(\x05B\"\x82v\x1f\x18\x01\x82\x01\x1aDbMapCacheHybridIndexGroupR\x05TileZ\x12&\n" +
	"\tCacheTime\x18\a \x01(\x03B\b\x82v\x052\x010X\tR\tCacheTime\x12(\n" +
	"\n" +
	"AccessTime\x18\b \x01(\x03B\b\x82v\x052\x010X\tR\n" +
	"AccessTime\x12L\n" +
	"\x0fTileImageFormat\x18\t \x01(\x05B\"\x82v\x1f\x18\x01\x82\x01\x1aDbMapCacheHybridIndexGroupR\x0fTileImageFormat\x12\x1a\n" +
	"\bTileHash\x18\n" +
	" \x01(\tR\bTileHash\x128\n" +
	"\x05Gcj02\x18\v \x01(\x05B\"\x82v\x1f\x18\x01\x82\x01\x1aDbMapCacheHybridIndexGroupR\x05Gcj02\x12 \n" +
	"\x06Status\x18\f \x01(\x05B\b\x82v\x052\x011X\tR\x06Status:\xab\x01\x82v\xa7\x01*\xa4\x01CREATE INDEX IF NOT EXISTS idx_DbMapCacheHybridIndex_deleteExpr_status_accesstime ON DbMapCacheHybridIndex ( (CASE WHEN Status = 8 THEN 1 ELSE 2 END), AccessTime );*\xb1\x01\n" +
	"\x0fMapProviderEnum\x12\x12\n" +
	"\x0eProviderGoogle\x10\x00\x12\x14\n" +
	"\x10ProviderTianditu\x10\x01\x12\x0f\n" +
	"\vProviderOSM\x10\x02\x12 \n" +
	"\x1cProviderGoogleLocalDirectory\x10\x03\x12\"\n" +
	"\x1eProviderTiandituLocalDirectory\x10\x04\x12\x1d\n" +
	"\x19ProviderOSMLocalDirectory\x10\x05*U\n" +
	"\x10GoogleMapApiEnum\x12\x16\n" +
	"\x12GoogleMapApiStatic\x10\x00\x12\x14\n" +
	"\x10GoogleMapApiTile\x10\x01\x12\x13\n" +
	"\x0fGoogleMapApiAll\x10\x02B\x0fZ\rbfmap/dbprotob\x06proto3"

var (
	file_db_proto_rawDescOnce sync.Once
	file_db_proto_rawDescData []byte
)

func file_db_proto_rawDescGZIP() []byte {
	file_db_proto_rawDescOnce.Do(func() {
		file_db_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_db_proto_rawDesc), len(file_db_proto_rawDesc)))
	})
	return file_db_proto_rawDescData
}

var file_db_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_db_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_db_proto_goTypes = []any{
	(MapProviderEnum)(0),             // 0: dbproto.MapProviderEnum
	(GoogleMapApiEnum)(0),            // 1: dbproto.GoogleMapApiEnum
	(*DbOrg)(nil),                    // 2: dbproto.DbOrg
	(*DbUser)(nil),                   // 3: dbproto.DbUser
	(*DbUserPrivilege)(nil),          // 4: dbproto.DbUserPrivilege
	(*DbUserSession)(nil),            // 5: dbproto.DbUserSession
	(*DbProject)(nil),                // 6: dbproto.DbProject
	(*TAvailableMapProvider)(nil),    // 7: dbproto.TAvailableMapProvider
	(*SysNames)(nil),                 // 8: dbproto.SysNames
	(*DbProjectToken)(nil),           // 9: dbproto.DbProjectToken
	(*DbProjectQuotas)(nil),          // 10: dbproto.DbProjectQuotas
	(*DbProjectTokenUsage)(nil),      // 11: dbproto.DbProjectTokenUsage
	(*DbMapProviderToken)(nil),       // 12: dbproto.DbMapProviderToken
	(*DbMapProviderUsedQuotas)(nil),  // 13: dbproto.DbMapProviderUsedQuotas
	(*DbMapProviderTokenUsage)(nil),  // 14: dbproto.DbMapProviderTokenUsage
	(*DbMapCacheRoadmapIndex)(nil),   // 15: dbproto.DbMapCacheRoadmapIndex
	(*DbMapCacheSatelliteIndex)(nil), // 16: dbproto.DbMapCacheSatelliteIndex
	(*DbMapCacheHybridIndex)(nil),    // 17: dbproto.DbMapCacheHybridIndex
}
var file_db_proto_depIdxs = []int32{
	0, // 0: dbproto.TAvailableMapProvider.DefaultRoadmap:type_name -> dbproto.MapProviderEnum
	0, // 1: dbproto.TAvailableMapProvider.DefaultSatellite:type_name -> dbproto.MapProviderEnum
	0, // 2: dbproto.TAvailableMapProvider.DefaultHybrid:type_name -> dbproto.MapProviderEnum
	8, // 3: dbproto.DbProjectToken.SysName:type_name -> dbproto.SysNames
	0, // 4: dbproto.DbMapProviderToken.Provider:type_name -> dbproto.MapProviderEnum
	1, // 5: dbproto.DbMapProviderToken.GoogleMapApi:type_name -> dbproto.GoogleMapApiEnum
	0, // 6: dbproto.DbMapCacheRoadmapIndex.Provider:type_name -> dbproto.MapProviderEnum
	0, // 7: dbproto.DbMapCacheSatelliteIndex.Provider:type_name -> dbproto.MapProviderEnum
	0, // 8: dbproto.DbMapCacheHybridIndex.Provider:type_name -> dbproto.MapProviderEnum
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_db_proto_init() }
func file_db_proto_init() {
	if File_db_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_db_proto_rawDesc), len(file_db_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_db_proto_goTypes,
		DependencyIndexes: file_db_proto_depIdxs,
		EnumInfos:         file_db_proto_enumTypes,
		MessageInfos:      file_db_proto_msgTypes,
	}.Build()
	File_db_proto = out.File
	file_db_proto_goTypes = nil
	file_db_proto_depIdxs = nil
}
