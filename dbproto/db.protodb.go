//generated by ygrpc-protodb. DO NOT EDIT.
//source: db.proto

package dbproto

import "google.golang.org/protobuf/proto"
import "github.com/ygrpc/protodb/msgstore"

var msgDbOrg *DbOrg
func getDbOrg(new bool) proto.Message {
	if new {
		return &DbOrg{}
	}
	if msgDbOrg == nil {
		msgDbOrg = &DbOrg{}
	}

	return msgDbOrg
}

const DbOrgLastFieldNo = 6

func (x *DbOrg) LastFieldNo() int32 {
	return DbOrgLastFieldNo
}


var msgDbUser *DbUser
func getDbUser(new bool) proto.Message {
	if new {
		return &DbUser{}
	}
	if msgDbUser == nil {
		msgDbUser = &DbUser{}
	}

	return msgDbUser
}

const DbUserLastFieldNo = 12

func (x *DbUser) LastFieldNo() int32 {
	return DbUserLastFieldNo
}


var msgDbUserPrivilege *DbUserPrivilege
func getDbUserPrivilege(new bool) proto.Message {
	if new {
		return &DbUserPrivilege{}
	}
	if msgDbUserPrivilege == nil {
		msgDbUserPrivilege = &DbUserPrivilege{}
	}

	return msgDbUserPrivilege
}

const DbUserPrivilegeLastFieldNo = 4

func (x *DbUserPrivilege) LastFieldNo() int32 {
	return DbUserPrivilegeLastFieldNo
}


var msgDbUserSession *DbUserSession
func getDbUserSession(new bool) proto.Message {
	if new {
		return &DbUserSession{}
	}
	if msgDbUserSession == nil {
		msgDbUserSession = &DbUserSession{}
	}

	return msgDbUserSession
}

const DbUserSessionLastFieldNo = 5

func (x *DbUserSession) LastFieldNo() int32 {
	return DbUserSessionLastFieldNo
}


var msgDbProject *DbProject
func getDbProject(new bool) proto.Message {
	if new {
		return &DbProject{}
	}
	if msgDbProject == nil {
		msgDbProject = &DbProject{}
	}

	return msgDbProject
}

const DbProjectLastFieldNo = 8

func (x *DbProject) LastFieldNo() int32 {
	return DbProjectLastFieldNo
}


var msgDbProjectToken *DbProjectToken
func getDbProjectToken(new bool) proto.Message {
	if new {
		return &DbProjectToken{}
	}
	if msgDbProjectToken == nil {
		msgDbProjectToken = &DbProjectToken{}
	}

	return msgDbProjectToken
}

const DbProjectTokenLastFieldNo = 12

func (x *DbProjectToken) LastFieldNo() int32 {
	return DbProjectTokenLastFieldNo
}

func (x *DbProjectToken) FieldProtoMsg(fieldName string) (proto.Message,bool) {
   switch fieldName {
   case "SysName":
         return &SysNames{}, true
	}
	return nil, false
}


var msgDbProjectQuotas *DbProjectQuotas
func getDbProjectQuotas(new bool) proto.Message {
	if new {
		return &DbProjectQuotas{}
	}
	if msgDbProjectQuotas == nil {
		msgDbProjectQuotas = &DbProjectQuotas{}
	}

	return msgDbProjectQuotas
}

const DbProjectQuotasLastFieldNo = 5

func (x *DbProjectQuotas) LastFieldNo() int32 {
	return DbProjectQuotasLastFieldNo
}


var msgDbProjectTokenUsage *DbProjectTokenUsage
func getDbProjectTokenUsage(new bool) proto.Message {
	if new {
		return &DbProjectTokenUsage{}
	}
	if msgDbProjectTokenUsage == nil {
		msgDbProjectTokenUsage = &DbProjectTokenUsage{}
	}

	return msgDbProjectTokenUsage
}

const DbProjectTokenUsageLastFieldNo = 6

func (x *DbProjectTokenUsage) LastFieldNo() int32 {
	return DbProjectTokenUsageLastFieldNo
}


var msgDbMapProviderToken *DbMapProviderToken
func getDbMapProviderToken(new bool) proto.Message {
	if new {
		return &DbMapProviderToken{}
	}
	if msgDbMapProviderToken == nil {
		msgDbMapProviderToken = &DbMapProviderToken{}
	}

	return msgDbMapProviderToken
}

const DbMapProviderTokenLastFieldNo = 20

func (x *DbMapProviderToken) LastFieldNo() int32 {
	return DbMapProviderTokenLastFieldNo
}


var msgDbMapProviderUsedQuotas *DbMapProviderUsedQuotas
func getDbMapProviderUsedQuotas(new bool) proto.Message {
	if new {
		return &DbMapProviderUsedQuotas{}
	}
	if msgDbMapProviderUsedQuotas == nil {
		msgDbMapProviderUsedQuotas = &DbMapProviderUsedQuotas{}
	}

	return msgDbMapProviderUsedQuotas
}

const DbMapProviderUsedQuotasLastFieldNo = 3

func (x *DbMapProviderUsedQuotas) LastFieldNo() int32 {
	return DbMapProviderUsedQuotasLastFieldNo
}


var msgDbMapProviderTokenUsage *DbMapProviderTokenUsage
func getDbMapProviderTokenUsage(new bool) proto.Message {
	if new {
		return &DbMapProviderTokenUsage{}
	}
	if msgDbMapProviderTokenUsage == nil {
		msgDbMapProviderTokenUsage = &DbMapProviderTokenUsage{}
	}

	return msgDbMapProviderTokenUsage
}

const DbMapProviderTokenUsageLastFieldNo = 5

func (x *DbMapProviderTokenUsage) LastFieldNo() int32 {
	return DbMapProviderTokenUsageLastFieldNo
}


var msgDbMapCacheRoadmapIndex *DbMapCacheRoadmapIndex
func getDbMapCacheRoadmapIndex(new bool) proto.Message {
	if new {
		return &DbMapCacheRoadmapIndex{}
	}
	if msgDbMapCacheRoadmapIndex == nil {
		msgDbMapCacheRoadmapIndex = &DbMapCacheRoadmapIndex{}
	}

	return msgDbMapCacheRoadmapIndex
}

const DbMapCacheRoadmapIndexLastFieldNo = 12

func (x *DbMapCacheRoadmapIndex) LastFieldNo() int32 {
	return DbMapCacheRoadmapIndexLastFieldNo
}


var msgDbMapCacheSatelliteIndex *DbMapCacheSatelliteIndex
func getDbMapCacheSatelliteIndex(new bool) proto.Message {
	if new {
		return &DbMapCacheSatelliteIndex{}
	}
	if msgDbMapCacheSatelliteIndex == nil {
		msgDbMapCacheSatelliteIndex = &DbMapCacheSatelliteIndex{}
	}

	return msgDbMapCacheSatelliteIndex
}

const DbMapCacheSatelliteIndexLastFieldNo = 11

func (x *DbMapCacheSatelliteIndex) LastFieldNo() int32 {
	return DbMapCacheSatelliteIndexLastFieldNo
}


var msgDbMapCacheHybridIndex *DbMapCacheHybridIndex
func getDbMapCacheHybridIndex(new bool) proto.Message {
	if new {
		return &DbMapCacheHybridIndex{}
	}
	if msgDbMapCacheHybridIndex == nil {
		msgDbMapCacheHybridIndex = &DbMapCacheHybridIndex{}
	}

	return msgDbMapCacheHybridIndex
}

const DbMapCacheHybridIndexLastFieldNo = 12

func (x *DbMapCacheHybridIndex) LastFieldNo() int32 {
	return DbMapCacheHybridIndexLastFieldNo
}


func init() {
   msgstore.RegisterMsg("DbOrg", getDbOrg)
   msgstore.RegisterMsg("DbUser", getDbUser)
   msgstore.RegisterMsg("DbUserPrivilege", getDbUserPrivilege)
   msgstore.RegisterMsg("DbUserSession", getDbUserSession)
   msgstore.RegisterMsg("DbProject", getDbProject)
   msgstore.RegisterMsg("DbProjectToken", getDbProjectToken)
   msgstore.RegisterMsg("DbProjectQuotas", getDbProjectQuotas)
   msgstore.RegisterMsg("DbProjectTokenUsage", getDbProjectTokenUsage)
   msgstore.RegisterMsg("DbMapProviderToken", getDbMapProviderToken)
   msgstore.RegisterMsg("DbMapProviderUsedQuotas", getDbMapProviderUsedQuotas)
   msgstore.RegisterMsg("DbMapProviderTokenUsage", getDbMapProviderTokenUsage)
   msgstore.RegisterMsg("DbMapCacheRoadmapIndex", getDbMapCacheRoadmapIndex)
   msgstore.RegisterMsg("DbMapCacheSatelliteIndex", getDbMapCacheSatelliteIndex)
   msgstore.RegisterMsg("DbMapCacheHybridIndex", getDbMapCacheHybridIndex)
}
