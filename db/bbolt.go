package db

import (
	"bfmap/config"
	"errors"
	"os"
	"path/filepath"
	"sync"

	"go.etcd.io/bbolt"
)

var keyValueDb *bbolt.DB
var keyValueDbMutex sync.Mutex = sync.Mutex{}
var BucketName = []byte("bfmap")
var ErrTileNotExist = errors.New("tile not exist")

func Connect2KeyValueDb() (*bbolt.DB, error) {
	dir := filepath.Join(config.DbDir, "bbolt")
	_ = os.MkdirAll(dir, 0o755)

	var err error
	dbPath := filepath.Join(dir, "bfmap.db")
	keyValueDb, err = bbolt.Open(dbPath, 0600, &bbolt.Options{
		NoSync: false,
	})
	if err != nil {
		return nil, err
	}

	// Create bucket if it doesn't exist
	err = keyValueDb.Update(func(tx *bbolt.Tx) error {
		_, err := tx.CreateBucketIfNotExists(BucketName)
		return err
	})
	if err != nil {
		return nil, err
	}

	return keyValueDb, nil
}

func CloseKeyValueDb() {
	if keyValueDb != nil {
		_ = keyValueDb.Close()
		keyValueDb = nil
	}
}

func GetKeyValueDb() (*bbolt.DB, error) {
	if keyValueDb != nil {
		return keyValueDb, nil
	}

	keyValueDbMutex.Lock()
	defer keyValueDbMutex.Unlock()

	var err error
	keyValueDb, err = Connect2KeyValueDb()
	if err != nil {
		keyValueDb = nil // Ensure it's nil if connection failed
		return nil, err
	}

	return keyValueDb, nil
}

func KeyValueDbSet(key, value []byte) error {
	db, err := GetKeyValueDb()
	if err != nil {
		return err
	}

	return db.Update(func(tx *bbolt.Tx) error {
		b := tx.Bucket(BucketName)
		return b.Put(key, value)
	})
}

func KeyValueDbGet(key []byte) ([]byte, error) {
	db, err := GetKeyValueDb()
	if err != nil {
		return nil, err
	}

	var value []byte
	err = db.View(func(tx *bbolt.Tx) error {
		b := tx.Bucket(BucketName)
		val := b.Get(key)
		if val == nil {
			return ErrTileNotExist
		}
		// Make a copy of the value since it will be invalid outside the transaction
		value = make([]byte, len(val))
		copy(value, val)
		return nil
	})

	return value, err
}

func KeyValueDbDelete(key []byte) error {
	db, err := GetKeyValueDb()
	if err != nil {
		return err
	}

	return db.Update(func(tx *bbolt.Tx) error {
		b := tx.Bucket(BucketName)
		return b.Delete(key)
	})
}

func KeyValueDbDiskSize() int64 {
	db, err := GetKeyValueDb()
	if err != nil {
		return 0
	}

	// Get file info in bytes size
	stat, err := os.Stat(db.Path())
	if err != nil {
		return 0
	}
	return stat.Size()
}

func KeyValueDbPageSize() int64 {
	db, err := GetKeyValueDb()
	if err != nil {
		return 0
	}
	// Get file info to determine size
	txStats := db.Stats().TxStats
	return int64(txStats.GetPageAlloc())
}

// The total number of bytes allocated to free pages, calculated as (FreePageN + PendingPageN) * pageSize.
func KeyValueDbFreeSize() int64 {
	db, err := GetKeyValueDb()
	if err != nil {
		return 0
	}
	// Get file info to determine size
	return int64(db.Stats().FreeAlloc)
}
