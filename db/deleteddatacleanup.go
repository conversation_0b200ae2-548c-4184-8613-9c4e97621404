package db

import (
	"bfmap/bfutil"
	"bfmap/config"
	"log/slog"
	"time"
)

func CheckAndDeleteDbDateWithStatus8Loop() {
	for {
		// config.DbDeletedDataTTL ago
		t := bfutil.CurrentUTCTime().Add(-time.Duration(config.DbDeletedDataTTL) * 24 * time.Hour)
		_, err := DbConn.WriteDB.Exec(
			"DELETE FROM DbProject WHERE Status = 8 AND DeleteTime < ?",
			t.Unix(),
		)
		if err != nil {
			slog.Warn("CheckAndDeleteDbDateWithStatus8Loop delete DbProject fail", "err", err)
		}
		_, err = DbConn.WriteDB.Exec(
			"DELETE FROM DbProjectToken WHERE Status = 8 AND DeleteTime < ?",
			t.Unix(),
		)
		if err != nil {
			slog.Warn("CheckAndDeleteDbDateWithStatus8Loop delete DbProjectToken fail", "err", err)
		}
		_, err = DbConn.WriteDB.Exec(
			"DELETE FROM DbMapProviderToken WHERE Status = 8 AND DeleteTime < ?",
			t.Unix(),
		)
		if err != nil {
			slog.Warn("CheckAndDeleteDbDateWithStatus8Loop delete DbMapProviderToken fail", "err", err)
		}
		time.Sleep(24 * time.Hour)
	}
}
