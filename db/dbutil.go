package db

import (
	"bfmap/dbproto"
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/ygrpc/protodb"
	"github.com/ygrpc/protodb/crud"
	"google.golang.org/protobuf/proto"
)

// if one item insert failed, rollback all items
func InsertItemsWithRollbackOnError(db *sql.DB, items []proto.Message) (err error) {
	if len(items) == 0 {
		return nil
	}

	rollbackOps := make([]func(), 0)
	defer func() {
		if err != nil {
			// LIFO
			for i := len(rollbackOps) - 1; i >= 0; i-- {
				rollbackOps[i]()
			}
		}
	}()

	for _, item := range items {
		_, err = crud.DbInsert(db, item, 0, "")
		if err != nil {
			return fmt.Errorf("insert %s err: %w", item.ProtoReflect().Descriptor().Name(), err)
		}
		rollbackOps = append(rollbackOps, func() {
			_, err2 := crud.DbDelete(db, item, "")
			if err2 != nil {
				slog.Warn(
					"InsertItemsWithRollbackOnError rollback item fail",
					"item", item.ProtoReflect().Descriptor().Name(),
					"err", err2)
			}
		})
	}

	return nil
}

func GetDbUserPrivilegeByUserRid(userRid string) (*dbproto.DbUserPrivilege, error) {
	dbConn, err := GetDbConn()
	if err != nil {
		return nil, err
	}
	q := &dbproto.DbUserPrivilege{
		UserRid: userRid,
	}
	result, err := crud.DbSelectOne(dbConn.ReadDB, q, []string{"UserRid"}, nil, "", false)
	if err != nil {
		return nil, err
	}
	return result.(*dbproto.DbUserPrivilege), nil
}

func GetDbUserPrivilegeByRid(rid string) (*dbproto.DbUserPrivilege, error) {
	dbConn, err := GetDbConn()
	if err != nil {
		return nil, err
	}
	q := &dbproto.DbUserPrivilege{
		Rid: rid,
	}
	result, err := crud.DbSelectOne(dbConn.ReadDB, q, []string{"Rid"}, nil, "", false)
	if err != nil {
		return nil, err
	}
	return result.(*dbproto.DbUserPrivilege), nil
}

func GetAllDbOrgs(db *sql.DB) (dbOrgs []*dbproto.DbOrg, err error) {
	fnGetDb := func(meta http.Header, schemaName string, tableName string, writable bool) (db *sql.DB, err error) {
		return db, nil
	}

	dbOrgs = make([]*dbproto.DbOrg, 0, 16)
	fnSendResp := func(resp *protodb.QueryResp) error {
		for _, msgBytes := range resp.MsgBytes {
			q := &dbproto.DbOrg{}
			err = proto.Unmarshal(msgBytes, q)
			if err != nil {
				return err
			}
			dbOrgs = append(dbOrgs, q)
		}
		return nil
	}

	err = crud.TableQuery(context.Background(), http.Header{}, &protodb.TableQueryReq{
		TableName: "DbOrg",
	}, fnGetDb, crud.FnTableQueryPermissionEmpty, fnSendResp)
	if err != nil {
		return nil, err
	}
	return dbOrgs, nil
}

func GetAllDbUsers(db *sql.DB) (dbUsers []*dbproto.DbUser, err error) {
	fnGetDb := func(meta http.Header, schemaName string, tableName string, writable bool) (db *sql.DB, err error) {
		return db, nil
	}

	dbUsers = make([]*dbproto.DbUser, 0, 16)
	fnSendResp := func(resp *protodb.QueryResp) error {
		for _, msgBytes := range resp.MsgBytes {
			q := &dbproto.DbUser{}
			err = proto.Unmarshal(msgBytes, q)
			if err != nil {
				return err
			}
			dbUsers = append(dbUsers, q)
		}
		return nil
	}

	err = crud.TableQuery(context.Background(), http.Header{}, &protodb.TableQueryReq{
		TableName: "DbUser",
	}, fnGetDb, crud.FnTableQueryPermissionEmpty, fnSendResp)
	if err != nil {
		return nil, err
	}
	return dbUsers, nil
}
