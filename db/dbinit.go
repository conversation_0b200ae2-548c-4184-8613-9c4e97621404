package db

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"bfmap/config"
	"bfmap/dbproto"

	"github.com/ygrpc/protodb/ddl"
	"google.golang.org/protobuf/proto"
	_ "modernc.org/sqlite"
)

var DbConn *BfMapSqliteDBConn

type BfMapSqliteDBConn struct {
	//使用WriteDB修改之前确定ReadDB的查询结果sql.Rows已经关闭
	//否则会导致错误 database is locked (5) (SQLITE_BUSY)
	WriteDB *sql.DB
	ReadDB  *sql.DB
}

func CloseDbConn() {
	if DbConn == nil {
		return
	}
	_ = DbConn.WriteDB.Close()
	_ = DbConn.ReadDB.Close()
}

// get db.
func GetDbConn() (*BfMapSqliteDBConn, error) {
	if DbConn == nil {
		var err error
		DbConn, err = Connect2Sqlite()
		if err != nil {
			return nil, err
		}
	}
	return DbConn, nil
}

func Connect2Sqlite() (*BfMapSqliteDBConn, error) {
	dir := filepath.Join(config.DbDir, "sqlite")
	_ = os.MkdirAll(dir, 0o755)

	dsn := filepath.Join(
		dir,
		"bfmap.db",
	) + "?_pragma=journal_mode=wal&_pragma=busy_timeout=5000&_pragma=synchronous=normal&_pragma=cache_size=1000000000&_pragma=foreign_keys=ON"
	writeDB, err := sql.Open("sqlite", dsn+"&_txlock=immediate")
	if err != nil {
		return nil, fmt.Errorf("open sqlite for write fail, %w", err)
	}
	writeDB.SetMaxOpenConns(1)

	readDB, err := sql.Open("sqlite", dsn)
	if err != nil {
		return nil, fmt.Errorf("open sqlite for read fail, %w", err)
	}
	readDB.SetMaxOpenConns(max(4, runtime.NumCPU()))

	// log the pragma of writeDB and readDB, just for debug
	// for _, v := range []string{
	// 	"cache_size",
	// 	"foreign_keys",
	// 	"journal_mode",
	// 	"synchronous",
	// 	"busy_timeout",
	// } {
	// 	row := writeDB.QueryRow("pragma " + v)
	// 	var val interface{}
	// 	row.Scan(&val)
	// 	fmt.Printf("writedb %q: %T(%v)\n", v, val, val)

	// 	row = readDB.QueryRow("pragma " + v)
	// 	row.Scan(&val)
	// 	fmt.Printf("readdb %q: %T(%v)\n", v, val, val)
	// }

	DbConn = &BfMapSqliteDBConn{
		WriteDB: writeDB,
		ReadDB:  readDB,
	}
	return DbConn, nil
}

func InitDb() (err error) {
	DbConn, err = Connect2Sqlite()
	if err != nil {
		return err
	}

	needCreateTableMsgs := []proto.Message{
		&dbproto.DbUser{},
		&dbproto.DbUserPrivilege{},
		&dbproto.DbUserSession{},
		&dbproto.DbProject{},
		&dbproto.DbProjectQuotas{},
		&dbproto.DbOrg{},
		&dbproto.DbProjectToken{},
		&dbproto.DbProjectTokenUsage{},
		&dbproto.DbMapProviderToken{},
		&dbproto.DbMapProviderUsedQuotas{},
		&dbproto.DbMapProviderTokenUsage{},
		&dbproto.DbMapCacheRoadmapIndex{},
		&dbproto.DbMapCacheHybridIndex{},
		&dbproto.DbMapCacheSatelliteIndex{},
	}

	var sqlStmts []*ddl.TDbTableInitSql
	var sqlStmt *ddl.TDbTableInitSql
	builtInitSqlMap := make(map[string]*ddl.TDbTableInitSql)
	for _, msg := range needCreateTableMsgs {
		sqlStmt, err = ddl.DbMigrateTable(DbConn.WriteDB, msg, "", true, true, builtInitSqlMap)
		if err != nil {
			return fmt.Errorf("create sql statement for %s err: %w",
				msg.ProtoReflect().Descriptor().Name(), err)
		}
		sqlStmts = append(sqlStmts, sqlStmt)
	}

	err = ddl.ExecSql(DbConn.WriteDB, sqlStmts)
	if err != nil {
		return err
	}
	return nil
}
