package maps

import (
	"bfmap/bfutil"
	"bfmap/config"
	"bfmap/db"
	"bfmap/dbproto"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/ygrpc/protodb"
	"github.com/ygrpc/protodb/crud"
	"google.golang.org/protobuf/proto"
)

// GetMapHttpClient get map http client, for request map tile from map provider.
func GetMapHttpClient(timeout time.Duration, needProxy bool) *http.Client {
	httpClient := &http.Client{
		Timeout: timeout,
	}

	if !needProxy {
		return httpClient
	}

	if config.HttpProxy != nil {
		return bfutil.CreateProxyClientForHttp(config.HttpProxy, timeout)
	}

	if config.Socks5Proxy != nil {
		socks5ProxyClient := bfutil.CreateProxyClientForSocks5(config.Socks5Proxy, timeout)
		if socks5ProxyClient != nil {
			return socks5ProxyClient
		}
	}

	return httpClient
}

func UpdateDbMapProviderUsedQuotas(Rid string, countStartTime time.Time, currentQuotas int32) error {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return err
	}

	q := &dbproto.DbMapProviderUsedQuotas{
		Rid:            Rid,
		CountStartTime: countStartTime.Unix(),
		UsedQuotas:     currentQuotas,
	}

	_, err = crud.DbUpdate(dbConn.WriteDB, q, 0, "")

	return err
}

func UpdateDbProjectQuotas(project *MapProject) error {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return err
	}

	q := &dbproto.DbProjectQuotas{
		Rid:               project.ProjectRid,
		QuotasType:        project.QuotasType.Load(),
		QuotasLimit:       project.QuotasLimit.Load(),
		CountStartTime:    project.CountStartTime.Load().Unix(),
		CurrentUsedQuotas: project.UsedCount.Load(),
	}

	_, err = crud.DbUpdate(dbConn.WriteDB, q, 0, "")

	return err
}

func InsertDbMapProviderTokenUsage(providerRid string, startTime int64, usedQuotas int32) error {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return err
	}

	u := &dbproto.DbMapProviderTokenUsage{
		Rid:         bfutil.UuidV7(),
		ProviderRid: providerRid,
		StartTime:   startTime,
		RotateTime:  bfutil.CurrentUTCTimeUnix(),
		UsageCount:  usedQuotas,
	}

	_, err = crud.DbInsert(dbConn.WriteDB, u, 0, "")
	return err
}

func InsertDbProjectTokenUsage(projectRid, token string, startTime int64, count int32) error {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return err
	}

	u := &dbproto.DbProjectTokenUsage{
		Rid:        bfutil.UuidV7(),
		ProjectRid: projectRid,
		Token:      token,
		StartTime:  startTime,
		EndTime:    bfutil.CurrentUTCTimeUnix(),
		UsageCount: count,
	}

	_, err = crud.DbInsert(dbConn.WriteDB, u, 0, "")
	return err
}

func GetDbProjectTokenByToken(token string) (*dbproto.DbProjectToken, error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, err
	}
	q := &dbproto.DbProjectToken{
		Token: token,
	}
	result, err := crud.DbSelectOne(dbConn.ReadDB, q, []string{"Token"}, nil, "", false)
	if err != nil {
		return nil, err
	}
	return result.(*dbproto.DbProjectToken), nil
}

func GetDbProjectByRid(rid string) (*dbproto.DbProject, error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, err
	}
	q := &dbproto.DbProject{
		Rid: rid,
	}
	result, err := crud.DbSelectOne(dbConn.ReadDB, q, []string{"Rid"}, nil, "", false)
	if err != nil {
		return nil, err
	}
	return result.(*dbproto.DbProject), nil
}

func GetDbProjectQuotasByProjectRid(projectRid string) (*dbproto.DbProjectQuotas, error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, err
	}
	q := &dbproto.DbProjectQuotas{
		Rid: projectRid,
	}
	result, err := crud.DbSelectOne(dbConn.ReadDB, q, []string{"Rid"}, nil, "", false)
	if err != nil {
		return nil, err
	}
	return result.(*dbproto.DbProjectQuotas), nil
}

func GetDbMapProviderTokens(userRid string, provider int) ([]*dbproto.DbMapProviderToken, error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, err
	}

	fnGetDb := func(meta http.Header, schemaName string, tableName string, writable bool) (db *sql.DB, err error) {
		return dbConn.ReadDB, nil
	}

	tokens := make([]*dbproto.DbMapProviderToken, 0)
	fnSendResp := func(resp *protodb.QueryResp) error {
		for _, msgBytes := range resp.MsgBytes {
			q := &dbproto.DbMapProviderToken{}
			err = proto.Unmarshal(msgBytes, q)
			if err != nil {
				return err
			}
			tokens = append(tokens, q)
		}

		return nil
	}

	err = crud.TableQuery(context.Background(), http.Header{}, &protodb.TableQueryReq{
		TableName: "DbMapProviderToken",
		Where: map[string]string{
			"UserRid":  userRid,
			"Provider": strconv.Itoa(provider),
		},
	}, fnGetDb, crud.FnTableQueryPermissionEmpty, fnSendResp)
	if err != nil {
		return nil, err
	}

	if len(tokens) == 0 {
		return nil, errors.New("db map provider tokens not found")
	}
	return tokens, nil
}

func GetDbMapProviderCurrentQuotas(Rid string) (*dbproto.DbMapProviderUsedQuotas, error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, err
	}
	q := &dbproto.DbMapProviderUsedQuotas{
		Rid: Rid,
	}

	result, err := crud.DbSelectOne(dbConn.ReadDB, q, []string{"Rid"}, nil, "", false)
	if err != nil {
		return nil, fmt.Errorf("get db map provider current quotas err:%v", err)
	}
	return result.(*dbproto.DbMapProviderUsedQuotas), nil
}

func GetDbMapCacheRoadmapIndex(
	provider int, lang string, x, y, z, imageFormat, gcj02 int) (*dbproto.DbMapCacheRoadmapIndex, error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, err
	}

	q := &dbproto.DbMapCacheRoadmapIndex{
		Provider:        dbproto.MapProviderEnum(provider),
		Language:        lang,
		TileX:           int32(x),
		TileY:           int32(y),
		TileZ:           int32(z),
		TileImageFormat: int32(imageFormat),
		Gcj02:           int32(gcj02),
	}

	result, err := crud.DbSelectOne(
		dbConn.ReadDB,
		q,
		[]string{"Provider", "Language", "TileX", "TileY", "TileZ", "TileImageFormat", "Gcj02"},
		nil,
		"",
		false)
	if err != nil {
		return nil, err
	}
	return result.(*dbproto.DbMapCacheRoadmapIndex), nil
}

func GetDbMapCacheSatelliteIndex(
	provider int,
	x, y, z, imageFormat, gcj02 int,
) (*dbproto.DbMapCacheSatelliteIndex, error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, err
	}
	q := &dbproto.DbMapCacheSatelliteIndex{
		Provider:        dbproto.MapProviderEnum(provider),
		TileX:           int32(x),
		TileY:           int32(y),
		TileZ:           int32(z),
		TileImageFormat: int32(imageFormat),
		Gcj02:           int32(gcj02),
	}

	result, err := crud.DbSelectOne(
		dbConn.ReadDB,
		q,
		[]string{"Provider", "TileX", "TileY", "TileZ", "TileImageFormat", "Gcj02"},
		nil,
		"",
		false)
	if err != nil {
		return nil, err
	}
	return result.(*dbproto.DbMapCacheSatelliteIndex), nil
}

func GetDbMapCacheHybridIndex(
	provider int, lang string, x, y, z, imageFormat, gcj02 int) (*dbproto.DbMapCacheHybridIndex, error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, err
	}
	q := &dbproto.DbMapCacheHybridIndex{
		Provider:        dbproto.MapProviderEnum(provider),
		Language:        lang,
		TileX:           int32(x),
		TileY:           int32(y),
		TileZ:           int32(z),
		TileImageFormat: int32(imageFormat),
		Gcj02:           int32(gcj02),
	}

	result, err := crud.DbSelectOne(
		dbConn.ReadDB,
		q,
		[]string{"Provider", "Language", "TileX", "TileY", "TileZ", "TileImageFormat", "Gcj02"},
		nil,
		"",
		false)
	if err != nil {
		return nil, err
	}
	return result.(*dbproto.DbMapCacheHybridIndex), nil
}

func UpdateDbMapCacheIndexCacheTime(mapReq *MapReq, t time.Time) error {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return err
	}

	var tableName string
	switch mapReq.MapType {
	case MapTypeRoadmap:
		tableName = "DbMapCacheRoadmapIndex"
	case MapTypeSatellite:
		tableName = "DbMapCacheSatelliteIndex"
	case MapTypeHybrid:
		tableName = "DbMapCacheHybridIndex"
	default:
		return errors.New("unknown map type")
	}

	sqlStr := `UPDATE ` + tableName + ` SET AccessTime = ? WHERE Provider = ? AND TileX = ? AND TileY = ? AND TileZ = ? AND TileImageFormat = ? AND Gcj02 = ? `
	if tableName == "DbMapCacheHybridIndex" {
		_, err = dbConn.WriteDB.Exec(
			sqlStr,
			t.Unix(),
			mapReq.Provider,
			mapReq.X,
			mapReq.Y,
			mapReq.Z,
			mapReq.ImageFormatInt,
			mapReq.EnableGcj02,
		)
		return err
	}
	sqlStr += ` AND Language = ? `
	_, err = dbConn.WriteDB.Exec(
		sqlStr,
		t.Unix(),
		mapReq.Provider,
		mapReq.X,
		mapReq.Y,
		mapReq.Z,
		mapReq.ImageFormatInt,
		mapReq.EnableGcj02,
		mapReq.Lang,
	)

	return err
}

func GetMapProviderTokensWithAdminFallback(userRid string, provider int) ([]*MapProviderToken, error) {
	providerTokens, err := GlobalMapsManager.GetMapProviderTokens(userRid, provider)
	if err != nil {
		providerTokens, err = GlobalMapsManager.GetMapProviderTokens(config.AdminRid, provider)
		if err != nil {
			return nil, err
		}
	}
	return providerTokens, nil
}
