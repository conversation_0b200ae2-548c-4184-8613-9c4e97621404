package maps

import (
	"bfmap/bfutil"
	"sync/atomic"
	"time"
)

// PeriodQuotas
type PeriodQuotas struct {
	// 1:per minute 2:per hour 3:per day 4:per month
	QuotasType atomic.Int32
	// 0: unlimited
	QuotasLimit    atomic.Int32
	UsedCount      atomic.Int32
	CountStartTime *bfutil.AtomicTime
}

func (l *PeriodQuotas) IsQuotasOver() bool {
	if l.QuotasLimit.Load() == 0 {
		return false
	}

	if l.UsedCount.Load() >= l.QuotasLimit.Load() {
		return true
	}
	return false
}

func (l *PeriodQuotas) ResetQuotas() {
	l.CountStartTime.Store(bfutil.CurrentUTCTime())
	l.UsedCount.Store(0)
}

func (l *PeriodQuotas) CheckIfShouldReset(
	current time.Time,
	totalMonth int,
) bool {
	var shouldReset bool
	switch l.QuotasType.Load() {
	case 1: // per minute，按分钟，刷新时间为每分钟的00秒
		if current.Truncate(time.Minute).After(l.CountStartTime.Load()) {
			shouldReset = true
		}
	case 2: // per hour，按小时，刷新时间为每小时的00分00秒
		if current.Truncate(time.Hour).After(l.CountStartTime.Load()) {
			shouldReset = true
		}
	case 3: // per day，按天，刷新时间为每天的00:00:00
		if current.Truncate(24 * time.Hour).After(l.CountStartTime.Load()) {
			shouldReset = true
		}
	case 4: // per month，按月，刷新时间为每月1号的00:00:00
		if totalMonth > TotalMonth(l.CountStartTime.Load()) {
			shouldReset = true
		}
	}

	return shouldReset
}

func (l *PeriodQuotas) SetQuotas(PeriodType, limit int32) {
	l.QuotasType.Store(PeriodType)
	l.QuotasLimit.Store(limit)
}

func TotalMonth(t time.Time) int {
	year, month, _ := t.Date()
	return year*12 + int(month)
}

func TotalDay(t time.Time) int {
	year, month, day := t.Date()
	return year*365 + int(month) + day
}
