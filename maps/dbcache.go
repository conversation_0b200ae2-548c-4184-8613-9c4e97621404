package maps

import (
	"bfmap/db"
	"encoding/json"
	"fmt"
	"log/slog"
)

// TileInfo is cache tile info in key-value db, use for quick query
type TileInfo struct {
	// key for tile image
	Hash      string `json:"hash"`
	CacheTime int64  `json:"cacheTime"`
	// 1:active,8:deleted
	Status int `json:"status"`
}

func SaveTileToKeyValueDb(mapReqKey string, tileInfo *TileInfo, imageBytes []byte) error {
	err := SaveTileInfoToKeyValueDb(mapReqKey, tileInfo)
	if err != nil {
		return fmt.Errorf("store tile info to key-value db fail, %w", err)
	}

	err = db.KeyValueDbSet([]byte(tileInfo.Hash), imageBytes)
	if err != nil {
		_ = db.KeyValueDbDelete([]byte(mapReqKey))
		return fmt.Errorf("store tile to key-value db fail, %w", err)
	}

	return nil
}

func QueryTileFromKeyValueDb(mapReq *MapReq) (*TileInfo, []byte, error) {
	tileInfo, err := QueryTileInfoFromKeyValueDb(mapReq.Key())
	if err != nil {
		return nil, nil, err
	}
	imageBytes, err := db.KeyValueDbGet([]byte(tileInfo.Hash))
	if err != nil {
		return nil, nil, err
	}
	return tileInfo, imageBytes, nil
}

func DeleteTileFromKeyValueDb(mapReqKey, hash string) error {
	err := db.KeyValueDbDelete([]byte(mapReqKey))
	if err != nil {
		return fmt.Errorf("delete tile info from key-value db fail, %w", err)
	}

	err = db.KeyValueDbDelete([]byte(hash))
	if err != nil {
		// 多个tile可能有相同的图片，这个图片对应的hash可能已经被删除了
		slog.Warn("delete tile from key-value db fail", "hash", hash, "err", err)
		return nil
	}
	return nil
}

func QueryTileInfoFromKeyValueDb(mapReqKey string) (*TileInfo, error) {
	get, err := db.KeyValueDbGet([]byte(mapReqKey))
	if err != nil {
		return nil, err
	}

	tileInfo := &TileInfo{}
	err = json.Unmarshal(get, tileInfo)
	if err != nil {
		return nil, err
	}
	return tileInfo, nil
}

func SaveTileInfoToKeyValueDb(mapReqKey string, tileInfo *TileInfo) error {
	marshal, err := json.Marshal(tileInfo)
	if err != nil {
		return err
	}
	return db.KeyValueDbSet([]byte(mapReqKey), marshal)
}
