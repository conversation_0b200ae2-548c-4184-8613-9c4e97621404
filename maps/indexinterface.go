package maps

import (
	"bfmap/dbproto"

	"google.golang.org/protobuf/reflect/protoreflect"
)

type MapTileCacheIndex interface {
	GetAccessTime() int64
	GetCacheTime() int64
	GetGcj02() int32
	GetProvider() dbproto.MapProviderEnum
	GetRid() string
	GetStatus() int32
	GetTileHash() string
	GetTileImageFormat() int32
	GetTileX() int32
	GetTileY() int32
	GetTileZ() int32

	ProtoReflect() protoreflect.Message
	String() string
}
