package maps

import (
	"time"

	"github.com/maypok86/otter"
)

// Temp map project token is used for deleting map cache images on the display page, only admin users can create it, valid for 10 minutes
// token string -> struct{}
var tempMapProjectToken otter.Cache[string, struct{}]

func init() {
	var err error
	tempMapProjectToken, err = otter.MustBuilder[string, struct{}](100).
		Cost(func(key string, value struct{}) uint32 {
			return 1
		}).
		WithTTL(10 * time.Minute).
		Build()
	if err != nil {
		panic("create temp map project token cache fail:" + err.Error())
	}
}

func DeleteTempMapProjectToken(token string) {
	tempMapProjectToken.Delete(token)
}

func IsTempMapProjectToken(token string) bool {
	return tempMapProjectToken.Has(token)
}

func SetTempMapProjectToken(token string) {
	tempMapProjectToken.Set(token, struct{}{})
}

func GetTempMapProjectToken(key string) (struct{}, bool) {
	return tempMapProjectToken.Get(key)
}
