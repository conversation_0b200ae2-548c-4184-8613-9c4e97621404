package maps

import (
	"bfmap/bfutil"
	"bfmap/dbproto"
	"errors"
	"fmt"
	"log/slog"
	"slices"
	"strconv"
	"time"

	"bfmap/singleflight"

	"github.com/maypok86/otter"
	"google.golang.org/protobuf/encoding/protojson"
)

var GlobalMapsManager = &MapsManager{}

type MapsManager struct {
	Mercator *Mercator
	// userRid+Provider -> []*MapProviderToken
	mapProviderTokenCache otter.Cache[string, []*MapProviderToken]
	//  MapProviderToken TokenRid -> time, Error occurred time, if there's an error, this token will be considered invalid for 5 minutes
	mapProviderTokenLastFailTimeCache otter.Cache[string, time.Time]
	// project rid -> *MapProject
	projectCache otter.Cache[string, *MapProject]
	// token -> *MapProjectToken
	projectTokenCache otter.Cache[string, *MapProjectToken]
	// for update index access time to db,
	//update trigger when map req is stored in cache for the first time and every 24 hours since cache init
	MapTileIndexAccessTimeCache otter.Cache[*MapReq, time.Time]

	// singleflight group for get data form db and block duplicate request
	sfg *singleflight.Group
}

func (m *MapsManager) Init() (err error) {
	m.Mercator = NewMercator(256)

	m.mapProviderTokenCache, err = otter.MustBuilder[string, []*MapProviderToken](1000).
		Cost(func(key string, value []*MapProviderToken) uint32 {
			return 1
		}).
		WithTTL(24 * time.Hour).
		DeletionListener(func(key string, value []*MapProviderToken, cause otter.DeletionCause) {
			for _, v := range value {
				v.SyncCacheToDb()
			}
		}).
		Build()
	if err != nil {
		return fmt.Errorf("create map provider token cache fail,%w", err)
	}

	m.mapProviderTokenLastFailTimeCache, err = otter.MustBuilder[string, time.Time](1000).
		Cost(func(key string, value time.Time) uint32 {
			return 1
		}).
		WithTTL(5 * time.Minute).
		Build()
	if err != nil {
		return fmt.Errorf("create map provider token error time cache fail,%w", err)
	}

	m.projectCache, err = otter.MustBuilder[string, *MapProject](10_000).
		Cost(func(key string, value *MapProject) uint32 {
			return 1
		}).
		WithTTL(24 * time.Hour).
		DeletionListener(func(key string, value *MapProject, cause otter.DeletionCause) {
			value.SyncCacheToDb()
		}).
		Build()
	if err != nil {
		return fmt.Errorf("create project cache fail,%w", err)
	}

	m.projectTokenCache, err = otter.MustBuilder[string, *MapProjectToken](100_000).
		Cost(func(key string, value *MapProjectToken) uint32 {
			return 1
		}).
		WithTTL(24 * time.Hour).
		DeletionListener(func(key string, value *MapProjectToken, cause otter.DeletionCause) {
			value.SyncCacheToDb()
		}).
		Build()

	if err != nil {
		return fmt.Errorf("create user token cache fail,%w", err)
	}

	m.MapTileIndexAccessTimeCache, err = otter.MustBuilder[*MapReq, time.Time](1_000_000).
		Cost(func(key *MapReq, value time.Time) uint32 {
			return 1
		}).WithTTL(12 * time.Hour).
		DeletionListener(func(key *MapReq, value time.Time, cause otter.DeletionCause) {
			err2 := UpdateDbMapCacheIndexCacheTime(key, value)
			if err2 != nil {
				slog.Warn("UpdateDbMapCacheIndexCacheTime in cache deletion fail", "map req key", key, "err", err2)
			}
		}).
		Build()

	if err != nil {
		return fmt.Errorf("create map req access time cache fail,%w", err)
	}

	m.sfg = &singleflight.Group{}
	go m.CheckAndResetQuotasLoop()
	go m.SyncCacheToDbLoop()
	go m.UpdateMapCacheIndexAccessTimeToDbLoop()

	return
}

func (m *MapsManager) Close() {
	m.SyncCacheToDb()

	m.mapProviderTokenCache.Close()
	m.projectCache.Close()
	m.projectTokenCache.Close()
}

// CheckAndResetQuotasLoop checks if map provider token quotas and map project quotas need to be reset and resets them.
func (m *MapsManager) CheckAndResetQuotasLoop() {
	var currentTime time.Time
	var currentMonth int
	for {
		// sleep to next exact minute
		time.Sleep(currentTime.Add(time.Minute).Truncate(time.Minute).Sub(currentTime))
		currentTime = bfutil.CurrentUTCTime()
		currentMonth = TotalMonth(currentTime)

		// check if token quotas is need to be reset
		m.mapProviderTokenCache.Range(func(key string, value []*MapProviderToken) bool {
			for _, v := range value {
				v.CheckAndResetQuotas(currentTime, currentMonth)
			}
			return true
		})

		m.projectCache.Range(func(key string, value *MapProject) bool {
			value.CheckAndResetQuotas(currentTime, currentMonth)
			return true
		})
	}
}

func (m *MapsManager) SyncCacheToDbLoop() {
	for {
		time.Sleep(5 * time.Minute)
		m.SyncCacheToDb()
	}
}

func (m *MapsManager) SyncCacheToDb() {
	m.mapProviderTokenCache.Range(func(key string, value []*MapProviderToken) bool {
		for _, v := range value {
			v.SyncCacheToDb()
		}
		return true
	})

	m.projectCache.Range(func(key string, value *MapProject) bool {
		value.SyncCacheToDb()
		return true
	})

	m.projectTokenCache.Range(func(key string, value *MapProjectToken) bool {
		value.SyncCacheToDb()
		return true
	})
}

func (m *MapsManager) UpdateMapCacheIndexAccessTimeToDbLoop() {
	for {
		time.Sleep(24 * time.Hour)
		m.MapTileIndexAccessTimeCache.Range(func(key *MapReq, value time.Time) bool {
			err := UpdateDbMapCacheIndexCacheTime(key, value)
			if err != nil {
				slog.Warn("UpdateMapCacheIndexAccessTimeToDbLoop fail", "map req key", key, "err", err)
			}
			return true
		})
	}
}

func (m *MapsManager) UpdateMapCacheIndexAccessTime(mapReq *MapReq) {
	m.MapTileIndexAccessTimeCache.Set(mapReq, bfutil.CurrentUTCTime())
}

func (m *MapsManager) UpdateMapProviderTokenCache(t *dbproto.DbMapProviderToken) {
	c, ok := m.mapProviderTokenCache.Get(t.UserRid + strconv.Itoa(int(t.Provider)))
	if !ok {
		return
	}

	var token *MapProviderToken
	for _, v := range c {
		if v.TokenRid == t.Rid {
			token = v
			break
		}
	}

	if token == nil {
		return
	}

	token.SyncCacheToDb()

	if token.Token != t.Token {
		m.DeleteMapProviderTokenLastFailTime(t.Rid)
	}

	token.TokenRid = t.Rid
	token.Token = t.Token
	token.Provider = t.Provider
	token.ExpireTime = time.Unix(t.ExpireTime, 0)
	token.Expiable = t.ExpireTime != 0
	token.MinZoom = int(t.MinZoom)
	token.MaxZoom = int(t.MaxZoom)
	token.Status = t.Status
	token.GoogleMapApi = t.GoogleMapApi
	token.SetQuotas(t.QuotasType, t.QuotasLimit)
	token.CountStartTime.Store(bfutil.CurrentUTCTime())
	if token.Priority != t.Priority {
		token.Priority = t.Priority
		m.SortMapProviderTokens(c)
	}
	token.BaseUrl = t.BaseUrl
	token.IsUseProxy = t.IsUseProxy
}

func (m *MapsManager) GetMapProviderTokens(userRid string, provider int) ([]*MapProviderToken, error) {
	key := userRid + strconv.Itoa(provider)
	tokens, ok := m.mapProviderTokenCache.Get(key)
	if ok {
		return tokens, nil
	}

	result, err, _ := m.sfg.Do("providerToken"+key, func() (any, error) {
		dbTokens, err := GetDbMapProviderTokens(userRid, provider)
		if err != nil {
			return nil, err
		}

		for _, t := range dbTokens {
			quotas, err := GetDbMapProviderCurrentQuotas(t.Rid)
			if err != nil {
				slog.Warn(
					"GetDbMapProviderCurrentQuotas fail",
					"userRid",
					userRid,
					"provider",
					provider,
					"tokenRid",
					t.Rid,
					"err",
					err,
				)
				continue
			}
			v := NewMapProviderToken(t, quotas)
			tokens = append(tokens, v)
		}

		if len(tokens) == 0 {
			return nil, errors.New("no map provider token found")
		}

		m.SortMapProviderTokens(tokens)

		ok = m.mapProviderTokenCache.Set(key, tokens)
		if !ok {
			slog.Debug("cache map provider token fail", "userRid", userRid, "provider", provider)
		}

		return tokens, nil
	})

	if err != nil {
		return nil, err
	}

	tokens = result.([]*MapProviderToken)

	return tokens, nil
}

// SortMapProviderTokens sorts the map provider tokens by priority in descending order.
func (m *MapsManager) SortMapProviderTokens(tokens []*MapProviderToken) {
	slices.SortFunc(tokens, func(a, b *MapProviderToken) int {
		return int(b.Priority) - int(a.Priority)
	})
}

func (m *MapsManager) SetMapProviderTokenCache(v *MapProviderToken) bool {
	key := v.UserRid + strconv.Itoa(int(v.Provider))
	value, _ := m.mapProviderTokenCache.Get(key)

	for i, t := range value {
		if t.TokenRid == v.TokenRid {
			value[i] = v
			return true
		}
	}

	value = append(value, v)
	m.mapProviderTokenCache.Set(key, value)

	return false
}

func (m *MapsManager) DeleteMapProviderTokensCache(userRid string, provider int) {
	key := userRid + strconv.Itoa(provider)
	m.mapProviderTokenCache.Delete(key)
}

func (m *MapsManager) DeleteMapProviderTokenCache(userRid string, provider int, tokenRid string) {
	key := userRid + strconv.Itoa(provider)

	value, ok := m.mapProviderTokenCache.Get(key)
	if !ok {
		return
	}

	for i, t := range value {
		if t.TokenRid == tokenRid {
			value = append(value[:i], value[i+1:]...)
			m.mapProviderTokenCache.Set(key, value)
			return
		}
	}
}

func (m *MapsManager) GetMapProjectCache(rid string) (*MapProject, error) {
	v, ok := m.projectCache.Get(rid)
	if ok {
		return v, nil
	}

	result, err, _ := m.sfg.Do("project"+rid, func() (any, error) {
		project, err := GetDbProjectByRid(rid)
		if err != nil {
			return nil, fmt.Errorf("get db project by rid fail,%w", err)
		}

		projectQuotas, err := GetDbProjectQuotasByProjectRid(project.Rid)
		if err != nil {
			return nil, fmt.Errorf("get db project quotas by project rid fail,%w", err)
		}

		v = NewMapProject(project, projectQuotas)
		ok = m.projectCache.Set(v.ProjectRid, v)
		if !ok {
			slog.Debug("cache map project fail", "rid", v.ProjectRid)
		}

		return v, nil
	})

	if err != nil {
		return nil, err
	}

	v = result.(*MapProject)

	return v, nil
}

func (m *MapsManager) UpdateMapProjectCache(
	dbProject *dbproto.DbProject,
	DbProjectQuotas *dbproto.DbProjectQuotas) {
	project, ok := m.projectCache.Get(dbProject.Rid)
	if !ok {
		return
	}

	project.SyncCacheToDb()

	if dbProject != nil {
		project.Name = dbProject.Name
		project.ProjectRid = dbProject.Rid
		project.UserRid = dbProject.UserRid
		project.Status = dbProject.Status
	}

	if DbProjectQuotas != nil {
		project.SetQuotas(DbProjectQuotas.QuotasType, DbProjectQuotas.QuotasLimit)
	}
}

func (m *MapsManager) SetMapProjectCache(v *MapProject) bool {
	return m.projectCache.Set(v.ProjectRid, v)
}

func (m *MapsManager) DeleteMapProjectCache(ProjectRid string) {
	m.projectCache.Delete(ProjectRid)
}

func (m *MapsManager) GetMapProjectTokenCache(token string) (*MapProjectToken, error) {
	v, ok := m.projectTokenCache.Get(token)
	if ok {
		return v, nil
	}

	result, err, _ := m.sfg.Do("projectToken"+token, func() (any, error) {
		projectToken, err := GetDbProjectTokenByToken(token)
		if err != nil {
			return nil, err
		}

		v = NewMapProjectToken(projectToken)
		ok = m.projectTokenCache.Set(token, v)
		if !ok {
			slog.Debug("cache project token fail", "token", token)
		}

		return v, nil
	})

	if err != nil {
		return nil, err
	}

	v = result.(*MapProjectToken)

	return v, nil
}

func (m *MapsManager) SetMapProjectTokenCache(v *MapProjectToken) bool {
	return m.projectTokenCache.Set(v.Token, v)
}

func (m *MapsManager) UpdateMapProjectTokenCache(v *dbproto.DbProjectToken) {
	token, ok := m.projectTokenCache.Get(v.Token)
	if !ok {
		return
	}

	token.TokenRid = v.Rid
	token.Token = v.Token
	token.ProjectRid = v.ProjectRid
	token.ExpireTime = time.Unix(v.ExpireTime, 0)
	token.SysNames = v.SysName
	token.Expiable = v.ExpireTime != 0
	token.Status = v.Status
	p := &dbproto.TAvailableMapProvider{}
	err := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}.Unmarshal([]byte(v.AvailableMapProvider), p)
	if err != nil {
		slog.Debug("UpdateMapProjectToken unmarshal available map provider fail", "err", err)
	} else {
		token.AvailableMapProvider = p
	}
}

func (m *MapsManager) DeleteMapProjectTokenCache(token string) {
	m.projectTokenCache.Delete(token)
}

func (m *MapsManager) GetMapProviderTokenLastFailTime(tokenRid string) (time.Time, bool) {
	return m.mapProviderTokenLastFailTimeCache.Get(tokenRid)
}

func (m *MapsManager) SetMapProviderTokenLastFailTime(tokenRid string, errorTime time.Time) {
	m.mapProviderTokenLastFailTimeCache.Set(tokenRid, errorTime)
}

func (m *MapsManager) DeleteMapProviderTokenLastFailTime(tokenRid string) {
	m.mapProviderTokenLastFailTimeCache.Delete(tokenRid)
}
