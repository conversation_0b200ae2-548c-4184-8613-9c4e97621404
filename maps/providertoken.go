package maps

import (
	"bfmap/bfutil"
	"bfmap/dbproto"
	"errors"
	"fmt"
	"log/slog"
	"math/rand/v2"
	"strconv"
	"sync"
	"sync/atomic"
	"time"
)

type MapProviderToken struct {
	TokenRid   string
	Token      string
	Provider   dbproto.MapProviderEnum
	UserRid    string
	ExpireTime time.Time
	Expiable   bool

	MinZoom int
	MaxZoom int

	// status, 1:active 4:disable
	Status int32

	GoogleMapApi dbproto.GoogleMapApiEnum
	Priority     int32
	BaseUrl      string
	IsUseProxy   bool
	Language     string

	PeriodQuotas

	//  mapType+language -> *GoogleMapTileSessionResp
	googleMapTileSessionMap sync.Map
	// mark for cache is different from db,need update db
	isDirty atomic.Bool
}

func NewMapProviderToken(
	token *dbproto.DbMapProviderToken,
	tokenQuotas *dbproto.DbMapProviderUsedQuotas) *MapProviderToken {
	Expiable := token.ExpireTime != 0

	v := &MapProviderToken{
		TokenRid:     token.Rid,
		Token:        token.Token,
		Provider:     token.Provider,
		UserRid:      token.UserRid,
		ExpireTime:   time.Unix(token.ExpireTime, 0),
		Expiable:     Expiable,
		MinZoom:      int(token.MinZoom),
		MaxZoom:      int(token.MaxZoom),
		Status:       token.Status,
		GoogleMapApi: token.GoogleMapApi,
		Priority:     token.Priority,
		BaseUrl:      token.BaseUrl,
		IsUseProxy:   token.IsUseProxy,
		Language:     token.Setting, // 暂时使用Setting字段存储语言信息
		PeriodQuotas: PeriodQuotas{
			QuotasType:     atomic.Int32{},
			QuotasLimit:    atomic.Int32{},
			UsedCount:      atomic.Int32{},
			CountStartTime: bfutil.NewAtomicTime(time.Unix(tokenQuotas.CountStartTime, 0)),
		},
		googleMapTileSessionMap: sync.Map{},
		isDirty:                 atomic.Bool{},
	}

	v.QuotasType.Store(token.QuotasType)
	v.QuotasLimit.Store(token.QuotasLimit)
	v.UsedCount.Store(tokenQuotas.UsedQuotas)

	current := bfutil.CurrentUTCTime()
	v.CheckAndResetQuotas(current, TotalMonth(current))

	return v
}

func (t *MapProviderToken) IsValid() bool {
	ok := t.IsActive() && !t.IsExpired() && !t.IsQuotasOver() && !t.IsFailInLast5Minutes()
	return ok
}

func (t *MapProviderToken) IsFailInLast5Minutes() bool {
	lastFailTime, ok := GlobalMapsManager.GetMapProviderTokenLastFailTime(t.TokenRid)
	if !ok {
		return false
	}
	lastFail := lastFailTime.After(bfutil.CurrentUTCTime().Add(-5 * time.Minute))
	return lastFail
}

func (t *MapProviderToken) IsExpired() bool {
	return t.Expiable && t.ExpireTime.Before(bfutil.CurrentUTCTime())
}

func (t *MapProviderToken) IsActive() bool {
	return t.Status == 1
}

func (t *MapProviderToken) AddOneUsageCount() {
	t.UsedCount.Add(1)
	t.isDirty.Store(true)
}

func (t *MapProviderToken) GetGoogleMapTileSession(
	req *MapReq,
) (googleMapSession *GoogleMapTileSessionResp, err error) {
	needHybridMarker := !req.IsNeedTiandituMarkerForGoogleMap()
	googleMapSessionSessionKey := req.MapType + req.Lang
	if req.MapType == MapTypeHybrid {
		googleMapSessionSessionKey += strconv.FormatBool(needHybridMarker)
	}

	session, ok := t.googleMapTileSessionMap.Load(googleMapSessionSessionKey)
	if ok {
		googleMapSession = session.(*GoogleMapTileSessionResp)
	}
	if googleMapSession == nil ||
		googleMapSession.ExpiryTime.Before(bfutil.CurrentUTCTime()) {
		googleMapSession, err = ReqMapTileSessionFromGoogleMap(
			t.Token,
			req.MapType,
			req.Lang,
			req.Region,
			req.ImageFormat,
			needHybridMarker,
			t.BaseUrl,
			t.IsUseProxy)
		if err != nil {
			return nil, fmt.Errorf("get google map tile session fail,%w", err)
		}
		t.googleMapTileSessionMap.Store(googleMapSessionSessionKey, googleMapSession)
	}

	return googleMapSession, nil
}

func (t *MapProviderToken) ReqGoogleMapStaticTile(mapReq *MapReq, lat, lon float64) ([]byte, error) {
	if t.Provider != dbproto.MapProviderEnum_ProviderGoogle {
		return nil, errors.New("not support provider")
	}
	t.AddOneUsageCount()
	var err error
	imageBytes, err := ReqMapTileFromGoogleMapStatic(
		t.Token,
		mapReq.MapType,
		lat,
		lon,
		mapReq.Z,
		mapReq.Lang,
		mapReq.ImageFormat,
		!mapReq.IsNeedTiandituMarkerForGoogleMap(),
		t.BaseUrl,
		t.IsUseProxy)

	if err != nil {
		return nil, err
	}
	return imageBytes, nil
}

func (t *MapProviderToken) ReqGoogleMapTile(session *GoogleMapTileSessionResp, mapReq *MapReq) ([]byte, error) {
	if t.Provider != dbproto.MapProviderEnum_ProviderGoogle {
		return nil, errors.New("not support provider")
	}
	t.AddOneUsageCount()
	return ReqMapTileFromGoogleMapTile(
		session,
		t.Token,
		mapReq.X,
		mapReq.Y,
		mapReq.Z,
		mapReq.ImageFormat,
		t.BaseUrl,
		t.IsUseProxy,
	)
}

func (t *MapProviderToken) ReqTiandutuMapTile(mapReq *MapReq) ([]byte, error) {
	if t.Provider != dbproto.MapProviderEnum_ProviderTianditu {
		return nil, errors.New("not support provider")
	}
	t.AddOneUsageCount()
	return ReqMapTileFromTianditu(
		t.Token,
		mapReq.MapType,
		mapReq.X,
		mapReq.Y,
		mapReq.Z,
		mapReq.ImageFormat,
		t.BaseUrl,
		t.IsUseProxy,
	)
}

// layerProj: 矢量底图vec_c/vec_w,矢量注记cva_c/cva_w,卫星底图img_c/img_w,卫星注记cia_c/cia_w.
func (t *MapProviderToken) ReqTiandituMapLayer(mapReq *MapReq, layerProj string) ([]byte, error) {
	if t.Provider != dbproto.MapProviderEnum_ProviderTianditu {
		return nil, errors.New("not support provider")
	}
	t.AddOneUsageCount()
	return ReqMapTileLayerFromTianditu(t.Token, mapReq.X, mapReq.Y, mapReq.Z, layerProj, t.BaseUrl, t.IsUseProxy)
}

func (t *MapProviderToken) ReqOSMMapTile(mapReq *MapReq) ([]byte, error) {
	if t.Provider != dbproto.MapProviderEnum_ProviderOSM {
		return nil, errors.New("not support provider")
	}
	t.AddOneUsageCount()
	return ReqMapTileFromOSM(mapReq.X, mapReq.Y, mapReq.Z, t.Token, t.BaseUrl, t.IsUseProxy)
}

func (t *MapProviderToken) RotateUsageCount() {
	if t.UsedCount.Load() == 0 {
		return
	}

	err := InsertDbMapProviderTokenUsage(t.TokenRid, t.CountStartTime.Load().Unix(), t.UsedCount.Load())
	if err != nil {
		slog.Warn("MapProviderToken RotateUsageCount fail", "err", err)
	}

	t.ResetQuotas()
	t.isDirty.Store(true)
}

func (t *MapProviderToken) CheckAndResetQuotas(currentTime time.Time, month int) {
	if t.CheckIfShouldReset(currentTime, month) {
		t.RotateUsageCount()
		t.isDirty.Store(true)
	}
}

func (t *MapProviderToken) SyncCacheToDb() {
	if !t.isDirty.CompareAndSwap(true, false) {
		return
	}

	err := UpdateDbMapProviderUsedQuotas(t.TokenRid, t.CountStartTime.Load(), t.UsedCount.Load())
	if err != nil {
		slog.Warn("MapProviderToken.SyncCacheToDb UpdateDbMapProviderUsedQuotas", "error", err)
		t.isDirty.Store(true)
		return
	}
}

// ChooseMapProviderToken selects a map provider token from a list of tokens, if more than one with the same priority,  choose an unexpired one randomly.
func chooseMapProviderTokenFromSlice(tokens []*MapProviderToken) (index int, err error) {
	if len(tokens) == 0 {
		return -1, errors.New("no tokens provided")
	}
	pos := 0
	n := len(tokens)
	for pos < n {
		priority := tokens[pos].Priority
		// Find the range of tokens with the same priority (tokens are sorted)
		end := pos
		for end < n && tokens[end].Priority == priority {
			end++
		}
		tokensWithSamePriority := tokens[pos:end]
		l := len(tokensWithSamePriority)
		j := rand.IntN(l)
		for i := 0; i < l; i++ {
			idx := j % l
			token := tokensWithSamePriority[idx]
			if token.IsValid() {
				return pos + idx, nil
			}
			j++
		}
		pos = end // Move to the next priority group
	}
	return -1, errors.New("all tokens are invalid")
}
