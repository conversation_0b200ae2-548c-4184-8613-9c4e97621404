package maps

import (
	"bfmap/bfutil"
	"bfmap/config"
	"bfmap/dbproto"
	"bytes"
	"errors"
	"fmt"
	"image"
	"log/slog"
	"math"
	"sync"

	"golang.org/x/image/draw"
)

// XYOffsets for map tile, center point is (0,0), 3x3 grid, use for calculate need map reqs
var _XYOffsets = []struct{ dx, dy int }{
	{-1, -1}, {0, -1}, {1, -1}, // first line
	{-1, 0}, {0, 0}, {1, 0}, // second line
	{-1, 1}, {0, 1}, {1, 1}, // third line
}

func queryMarkerLayerFromTianditu(userRid string, mapReq *MapReq, layerProj string) (imageBytes []byte, err error) {
	// Get provider tokens with admin fallback
	providerTokens, err := GetMapProviderTokensWithAdminFallback(userRid, int(dbproto.MapProviderEnum_ProviderTianditu))
	if err != nil {
		return nil, err
	}

	// Choose an appropriate token
	index, err := chooseMapProviderTokenFromSlice(providerTokens)
	if err != nil {
		return nil, err
	}

	// Try each available token until success or all tokens are exhausted
	for ; index < len(providerTokens); index++ {
		providerToken := providerTokens[index]

		// Skip invalid tokens
		if !providerToken.IsValid() {
			continue
		}

		// Skip tokens with zoom level restrictions
		if (providerToken.MinZoom > 0 && mapReq.Z < providerToken.MinZoom) ||
			(providerToken.MaxZoom > 0 && mapReq.Z > providerToken.MaxZoom) {
			continue
		}

		// Attempt to request map layer
		imageBytes, err = providerToken.ReqTiandituMapLayer(mapReq, layerProj)
		if err == nil {
			return imageBytes, nil
		}

		// Mark token as failed for this request
		GlobalMapsManager.SetMapProviderTokenLastFailTime(providerToken.TokenRid, bfutil.CurrentUTCTime())
	}

	return nil, errors.New("failed to request tianditu marker layer")
}

func ReqGoogleMapTileWithGcj02Convert(
	providerToken *MapProviderToken,
	mapReq *MapReq,
) ([]byte, error) {
	if mapReq.Z < 9 || mapReq.IsOutOfChina || mapReq.EnableGcj02 == 0 {
		return DownloadMapTileDirectFromGoogleMapTile(providerToken, mapReq)
	}

	// get google map tile session
	session, err := providerToken.GetGoogleMapTileSession(mapReq)
	if err != nil {
		return nil, fmt.Errorf("can not get google map tile session, %w", err)
	}

	gcjMapReqs, pixelXOffset, pixelYOffset := prepareNineGridMapReqsForGoogleMapTile(mapReq)

	// 0: mapReq, 1:horizontalMapReq, 2:verticalMapReq, 3:diagonalMapReq
	needMapReqs, needImageRects := calculateNeedMapReqsAndImageRectsForGoogleMapTile(
		mapReq,
		pixelXOffset,
		pixelYOffset,
	)

	needMapReqResults := make([]*MapReqResult, len(needMapReqs))

	// use single flight block resultMapReqs that not equal to mapReq in param
	blockWg := sync.WaitGroup{}
	blockWg.Add(1)
	defer blockWg.Done()
	for i, r := range needMapReqs {
		needMapReqResults[i] = &MapReqResult{
			Err: errors.New("not found"),
		}
		if i == 0 {
			// it has been blocked in MapHttpHandler() already
			continue
		}
		idx := i
		GlobalMapsManager.sfg.DoChan(r.BlockKey(), func() (any, error) {
			blockWg.Wait()
			if needMapReqResults[idx].Err != nil {
				return nil, needMapReqResults[idx].Err
			}
			tileInfo, err := SaveTileToDb(r, needMapReqResults[idx].ImageBytes, true)
			if err != nil {
				slog.Warn("save tile to db fail", "err", err, "tileInfo", tileInfo)
			}
			return &mapReqBlockResult{
				TileInfo:   tileInfo,
				ImageBytes: needMapReqResults[idx].ImageBytes,
			}, needMapReqResults[idx].Err
		})
	}

	// download 3x3 grid of tiles
	gcjMapReqResults := make([]*MapReqResult, len(gcjMapReqs))
	wg := sync.WaitGroup{}
	for i, r := range gcjMapReqs {
		wg.Add(1)
		gcjMapReqResults[i] = &MapReqResult{}
		go func(i int, r *MapReq) {
			defer wg.Done()
			b, cached := GetCacheTile(r.CacheKey("tile"))
			if cached {
				if config.IsVerboseDebugMap {
					slog.Debug("ReqMapTileWithGcj02Convert get google map tile from cache", "key", r.CacheKey("tile"))
				}
				gcjMapReqResults[i].ImageBytes = b
				return
			}
			gcjMapReqResults[i].ImageBytes, gcjMapReqResults[i].Err = providerToken.ReqGoogleMapTile(session, r)
			if gcjMapReqResults[i].Err != nil {
				return
			}
			CacheTile(r.CacheKey("tile"), gcjMapReqResults[i].ImageBytes)
		}(i, r)
	}

	// 0: mapReq, 1:horizontalMapReq, 2:verticalMapReq, 3:diagonalMapReq
	var markerLayerResults []*MapReqResult
	if mapReq.MapType == MapTypeHybrid {
		markerLayerResults = make([]*MapReqResult, len(needMapReqs))
		for i, r := range needMapReqs {
			wg.Add(1)
			//fetch marker tile
			go func(k int, r *MapReq) {
				defer wg.Done()
				markerLayerResults[k] = &MapReqResult{}
				cacheKey := r.CacheKey("marker")
				b, cached := GetCacheTile(cacheKey)
				if cached {
					markerLayerResults[k].ImageBytes = b
					return
				}

				markerLayerResults[k].ImageBytes, markerLayerResults[k].Err = queryMarkerLayerFromTianditu(
					providerToken.UserRid,
					r,
					"cia_w",
				)
				if markerLayerResults[k].Err == nil {
					CacheTile(cacheKey, markerLayerResults[k].ImageBytes)
				}
			}(i, r)
		}
	}
	wg.Wait()

	// merge tile in Nine-grid
	var img image.Image
	m := image.NewRGBA(image.Rect(0, 0, 256*3, 256*3))
	for i, r := range gcjMapReqResults {
		if r.Err != nil {
			return nil, fmt.Errorf("download the %d  map tile from google map failed, %w", i, r.Err)
		}
		img, _, r.Err = image.Decode(bytes.NewReader(r.ImageBytes))
		if r.Err != nil {
			return nil, fmt.Errorf("decode the %d map tile from google map failed, %w", i, r.Err)
		}
		draw.Draw(m,
			image.Rect((i%3)*256, (i/3)*256, (i%3+1)*256, (i/3+1)*256),
			img,
			image.Point{0, 0},
			draw.Src)
	}

	// crop the map image
	// 0: mapReq, 1:horizontalMapReq, 2:verticalMapReq, 3:diagonalMapReq
	resultImages := make([]image.Image, len(needMapReqs))
	for i := range needMapReqs {
		resultImages[i] = m.SubImage(needImageRects[i])
	}

	// marge marker to resultImages if markerLayerResults is not nil
	var markerImage image.Image
	for i, r := range markerLayerResults {
		if r.Err != nil {
			return nil, fmt.Errorf("download the %d map tile marker from tianditu failed, %w", i, r.Err)
		}

		markerImage, _, r.Err = image.Decode(bytes.NewReader(r.ImageBytes))
		if r.Err != nil {
			return nil, fmt.Errorf("decode marker layer for the %d google map hybrid tile fail:%v", i, r.Err)
		}

		img := image.NewRGBA(image.Rect(0, 0, 256, 256))
		// result image shares pixels with the original image.
		draw.Draw(img, img.Bounds(), resultImages[i], needImageRects[i].Min, draw.Src)
		draw.Draw(img, img.Bounds(), markerImage, image.Point{0, 0}, draw.Over)
		resultImages[i] = img
	}

	// convert the image to mapReq.ImageFormat
	for i, result := range needMapReqResults {
		result.ImageBytes, result.Err = bfutil.ConvertImage2Bytes(resultImages[i], mapReq.ImageFormat)
		if result.Err != nil {
			return nil, fmt.Errorf(
				"convert the %d google map hybrid tile to %s failed, %w",
				i,
				mapReq.ImageFormat,
				result.Err,
			)
		}
	}

	return needMapReqResults[0].ImageBytes, nil
}

// prepareNineGridMapReqsForGoogleMapTile prepare gcjMapReqs, pixelXOffset, pixelYOffset
// gcjMapReqs contains 9 map tile requests
func prepareNineGridMapReqsForGoogleMapTile(mapReq *MapReq) (gcjMapReqs []*MapReq, pixelXOffset, pixelYOffset int) {
	gcjPx, gcjPy := GlobalMapsManager.Mercator.LatLonToPixels(mapReq.GcjLat, mapReq.GcjLon, mapReq.Z)
	gcjTx, gcjTy := GlobalMapsManager.Mercator.PixelsToTiles(gcjPx, gcjPy, mapReq.Z)
	// google tile number
	gcjGTx, gcjGTy := GlobalMapsManager.Mercator.GoogleTiles(gcjTx, gcjTy, mapReq.Z)
	// google pixel coordinates
	gcjGpx, gcjGpy := GlobalMapsManager.Mercator.GooglePixels(gcjPx, gcjPy, mapReq.Z)
	pixelXOffset, pixelYOffset = GlobalMapsManager.Mercator.PixelDistanceBetweenPixelAndTileCenter(
		gcjGpx,
		gcjGpy,
		mapReq.Z,
	)

	gcjMapReqs = make([]*MapReq, 9)

	for i := 0; i < 9; i++ {
		gcjMapReqs[i] = mapReq.Clone()
		gcjMapReqs[i].X = gcjGTx + _XYOffsets[i].dx
		gcjMapReqs[i].Y = gcjGTy + _XYOffsets[i].dy
	}

	return
}

// 0: mapReq, 1:horizontalMapReq, 2:verticalMapReq, 3:diagonalMapReq
func calculateNeedMapReqsAndImageRectsForGoogleMapTile(
	mapReq *MapReq,
	pixelXOffset, pixelYOffset int,
) (needMapReqs []*MapReq, needImageRects []image.Rectangle) {
	needMapReqs = make([]*MapReq, 4)
	needImageRects = make([]image.Rectangle, 4)
	centerX := 384 + pixelXOffset
	centerY := 384 + pixelYOffset
	// build map reqs in resultMapReqs, needImageRects
	needMapReqs[0] = mapReq
	needImageRects[0] = image.Rect(centerX-128, centerY-128, centerX+128, centerY+128)
	var horizontalIndex, horizontalX int
	horizontalMapReq := mapReq.Clone()
	if pixelXOffset >= 0.0 {
		// east,use west tile
		horizontalIndex = 3
		horizontalX = centerX - 256
		horizontalMapReq.Y = mapReq.Y
		horizontalMapReq.X = mapReq.X - 1
	} else {
		// west,use east tile
		horizontalIndex = 5
		horizontalX = centerX + 256
		horizontalMapReq.Y = mapReq.Y
		horizontalMapReq.X = mapReq.X + 1
	}
	needMapReqs[1] = horizontalMapReq
	needImageRects[1] = image.Rect(horizontalX-128, centerY-128, horizontalX+128, centerY+128)

	var verticalIndex, verticalY int
	verticalMapReq := mapReq.Clone()
	if pixelYOffset >= 0.0 {
		// south,use north tile
		verticalIndex = 1
		verticalY = centerY - 256
		verticalMapReq.Y = mapReq.Y - 1
		verticalMapReq.X = mapReq.X
	} else {
		// north,use south tile
		verticalIndex = 7
		verticalY = centerY + 256
		verticalMapReq.Y = mapReq.Y + 1
		verticalMapReq.X = mapReq.X
	}
	needMapReqs[2] = verticalMapReq
	needImageRects[2] = image.Rect(centerX-128, verticalY-128, centerX+128, verticalY+128)

	diagonalIdx := verticalIndex + horizontalIndex - 4
	diagonalMapReq := mapReq.Clone()
	switch diagonalIdx {
	case 0:
		diagonalMapReq.Y = mapReq.Y - 1
		diagonalMapReq.X = mapReq.X - 1
	case 2:
		diagonalMapReq.Y = mapReq.Y - 1
		diagonalMapReq.X = mapReq.X + 1
	case 6:
		diagonalMapReq.Y = mapReq.Y + 1
		diagonalMapReq.X = mapReq.X - 1
	case 8:
		diagonalMapReq.Y = mapReq.Y + 1
		diagonalMapReq.X = mapReq.X + 1
	}
	needMapReqs[3] = diagonalMapReq
	needImageRects[3] = image.Rect(horizontalX-128, verticalY-128, horizontalX+128, verticalY+128)

	return
}

func DownloadMapTileDirectFromGoogleMapTile(
	providerToken *MapProviderToken,
	req *MapReq,
) ([]byte, error) {
	var srcImageBytes []byte
	session, err := providerToken.GetGoogleMapTileSession(req)
	if err != nil {
		return nil, fmt.Errorf("can not get google map tile session, %w", err)
	}
	if !req.IsNeedTiandituMarkerForGoogleMap() {
		srcImageBytes, err = providerToken.ReqGoogleMapTile(session, req)
		return srcImageBytes, err
	}

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		cacheKey := req.CacheKey("tile")
		b, cached := GetCacheTile(cacheKey)
		if cached {
			srcImageBytes = b
			return
		}
		srcImageBytes, err = providerToken.ReqGoogleMapTile(session, req)
		if err != nil {
			return
		}
		CacheTile(cacheKey, srcImageBytes)
	}()

	var overImageBytes []byte
	var err2 error
	wg.Add(1)
	go func() {
		defer wg.Done()
		cacheKey := req.CacheKey("marker")
		b, cached := GetCacheTile(cacheKey)
		if cached {
			overImageBytes = b
			return
		}
		overImageBytes, err2 = queryMarkerLayerFromTianditu(providerToken.UserRid, req, "cia_w")
		if err2 != nil {
			return
		}
		CacheTile(cacheKey, overImageBytes)
	}()

	wg.Wait()
	if err != nil {
		return nil, fmt.Errorf("get google map tile fail:%v", err)
	}
	if err2 != nil {
		return nil, fmt.Errorf("get tianditu map tile fail:%v", err2)
	}

	imageBytes, err := MergeImage(srcImageBytes, overImageBytes, 256, req.ImageFormat)
	if err != nil {
		return nil, fmt.Errorf("merge google tile src image and tianditu over image fail:%v", err)
	}
	return imageBytes, nil
}

func MergeImage(src, over []byte, size int, format string) ([]byte, error) {
	srcImage, _, err := image.Decode(bytes.NewReader(src))
	if err != nil {
		return nil, fmt.Errorf("decode src image fail:%v", err)
	}
	markerImage, _, err := image.Decode(bytes.NewReader(over))
	if err != nil {
		return nil, fmt.Errorf("decode over image fail:%v", err)
	}

	m := image.NewRGBA(image.Rect(0, 0, size, size))

	draw.Draw(m, m.Bounds(), srcImage, image.Point{0, 0}, draw.Src)
	draw.Draw(m, m.Bounds(), markerImage, image.Point{0, 0}, draw.Over)

	return bfutil.ConvertImage2Bytes(m, format)
}

func ReqGoogleMapStaticWithGcj02Convert(providerToken *MapProviderToken, mapReq *MapReq) ([]byte, error) {
	isGcj02 := true
	if mapReq.Z < 9 || mapReq.IsOutOfChina || mapReq.EnableGcj02 == 0 {
		isGcj02 = false
	}

	maxTileIndex := int(math.Pow(2, float64(mapReq.Z))) - 1
	if mapReq.X == 0 || mapReq.Y == 0 || mapReq.X == maxTileIndex || mapReq.Y == maxTileIndex {
		return DownloadMapTileFromGoogleMapStaticReturnOne(providerToken, mapReq, isGcj02)
	} else {
		return DownloadMapTileFromGoogleMapStaticReturnFour(providerToken, mapReq, isGcj02)
	}
}

// DownloadMapTileFromGoogleMapStaticReturnOne crop center 256x256 image from 640x640 image
func DownloadMapTileFromGoogleMapStaticReturnOne(
	providerToken *MapProviderToken,
	mapReq *MapReq,
	isGcj02 bool,
) ([]byte, error) {
	lat, lon := mapReq.WgsLat, mapReq.WgsLon
	//sattelite map tile has not been gcj02 converted
	//and when use hybrid map tile with gcj02, combine tianditu marker and sattelite map tile
	if isGcj02 && mapReq.MapType == MapTypeRoadmap {
		lat, lon = mapReq.GcjLat, mapReq.GcjLon
	}

	wg := sync.WaitGroup{}
	wg.Add(1)
	var originImage image.Image
	var err1 error
	go func() {
		defer wg.Done()
		var originImageBytes []byte
		originImageBytes, err1 = providerToken.ReqGoogleMapStaticTile(mapReq, lat, lon)
		if err1 != nil {
			return
		}
		originImage, _, err1 = image.Decode(bytes.NewReader(originImageBytes))
	}()

	var overImage image.Image
	var err2 error
	if mapReq.IsNeedTiandituMarkerForGoogleMap() {
		// query tianditu marker
		wg.Add(1)
		go func() {
			defer wg.Done()
			var overImageBytes []byte

			cacheKey := mapReq.CacheKey("marker")
			b, cached := GetCacheTile(cacheKey)
			if cached {
				overImageBytes = b
			} else {
				overImageBytes, err2 = queryMarkerLayerFromTianditu(providerToken.UserRid, mapReq, "cia_w")
				if err2 != nil {
					return
				}

				CacheTile(cacheKey, overImageBytes)
			}

			overImage, _, err2 = image.Decode(bytes.NewReader(overImageBytes))
		}()
	}
	wg.Wait()

	if err1 != nil {
		return nil, fmt.Errorf("download map tile from google map failed, %w", err1)
	}

	if err2 != nil {
		return nil, fmt.Errorf("download map tile marker layer from tianditu failed, %w", err2)
	}

	// crop center 256x256 image from origin image
	cropped := bfutil.CropImageCenter(originImage, 256, 256)

	resultImage := image.NewRGBA(image.Rect(0, 0, 256, 256))
	// cropped image share same pixel bound with original image
	draw.Draw(resultImage, resultImage.Bounds(), cropped, cropped.Bounds().Min, draw.Src)
	if overImage != nil {
		draw.Draw(resultImage, resultImage.Bounds(), overImage, image.Point{0, 0}, draw.Over)
	}

	imageBytes, err := bfutil.ConvertImage2Bytes(resultImage, mapReq.ImageFormat)
	if err != nil {
		return nil, fmt.Errorf("encode map tile from google map failed, %w", err)
	}

	return imageBytes, nil
}

func DownloadMapTileFromGoogleMapStaticReturnFour(
	providerToken *MapProviderToken,
	mapReq *MapReq,
	isGcj02 bool,
) ([]byte, error) {
	// take the **left bottom** corner point of tile as center point to download image
	lat, lon, direction := calculateCenterLatLonForGoogleMapStatic(mapReq)
	//sattelite map tile has not been gcj02 converted
	//and when use hybrid map tile with gcj02, combine tianditu marker and sattelite map tile
	if isGcj02 && mapReq.MapType == MapTypeRoadmap {
		lat, lon = WGStoGCJ(lat, lon)
	}

	if config.IsVerboseDebugMap {
		slog.Debug(
			"DownloadMapTileFromGoogleMapStaticReturnFour calculate center",
			"lat",
			lat,
			"lon",
			lon,
			"direction",
			direction,
			"map req key",
			mapReq.Key(),
		)
	}

	// add other tile's map req
	needMapReqs, needImageRects := calculateNeedMapReqsAndImageRectsForGoogleMapStatic(mapReq, direction)
	if config.IsVerboseDebugMap {
		slog.Debug("DownloadMapTileFromGoogleMapStaticReturnFour calculate need map reqs", "needMapReqs", needMapReqs)
	}
	needMapReqResults := make([]*MapReqResult, len(needMapReqs))

	// block resultMapReqs, except mapReq in param,it has been blocked already
	// use single flight block resultMapReqs that not equal to mapReq in param
	blockWg := sync.WaitGroup{}
	blockWg.Add(1)
	defer blockWg.Done()
	for i, r := range needMapReqs {
		needMapReqResults[i] = &MapReqResult{Err: errors.New("not found")}
		if i == 0 {
			// it has been blocked in MapHttpHandler() already
			continue
		}
		idx := i
		GlobalMapsManager.sfg.DoChan(r.BlockKey(), func() (any, error) {
			blockWg.Wait()
			if needMapReqResults[idx].Err != nil {
				return nil, needMapReqResults[idx].Err
			}
			tileInfo, err := SaveTileToDb(r, needMapReqResults[idx].ImageBytes, true)
			if err != nil {
				slog.Warn("save tile to db fail", "err", err, "tileInfo", tileInfo)
			}
			return &mapReqBlockResult{
				TileInfo:   tileInfo,
				ImageBytes: needMapReqResults[idx].ImageBytes,
			}, needMapReqResults[idx].Err
		})
	}

	wg := sync.WaitGroup{}
	wg.Add(1)
	var originImage image.Image
	var err error
	go func() {
		defer wg.Done()
		var originImageBytes []byte
		originImageBytes, err = providerToken.ReqGoogleMapStaticTile(mapReq, lat, lon)
		if err != nil {
			return
		}
		originImage, _, err = image.Decode(bytes.NewReader(originImageBytes))
	}()

	var markerLayerResults []*MapReqResult
	if mapReq.IsNeedTiandituMarkerForGoogleMap() {
		// query tianditu marker
		markerLayerResults = make([]*MapReqResult, len(needImageRects))
		for i, r := range needMapReqs {
			wg.Add(1)
			markerLayerResults[i] = &MapReqResult{}
			go func(i int, r *MapReq) {
				defer wg.Done()
				markerLayerResults[i].ImageBytes, markerLayerResults[i].Err = queryMarkerLayerFromTianditu(
					providerToken.UserRid,
					r,
					"cia_w",
				)
				if markerLayerResults[i].Err != nil {
					return
				}
			}(i, r)
		}
	}
	wg.Wait()

	// check if get origin image failed
	if err != nil {
		return nil, fmt.Errorf("download map tile from google map failed, %w", err)
	}

	// crop four 256x256 image from origin image
	resultImages := make([]*image.RGBA, len(needMapReqs))
	for i := range needMapReqs {
		m := image.NewRGBA(image.Rect(0, 0, 256, 256))
		draw.Draw(m, m.Bounds(), originImage, needImageRects[i].Min, draw.Src)
		resultImages[i] = m
	}

	// combine sattelite map tile and tianditu marker if needed
	for i, result := range markerLayerResults {
		if result.Err != nil {
			return nil, fmt.Errorf("download map tile marker layer %d from tianditu failed, %w", i, result.Err)
		}

		m, _, err := image.Decode(bytes.NewReader(result.ImageBytes))
		if err != nil {
			return nil, fmt.Errorf("decode map tile marker layer %d fail:%v", i, err)
		}
		draw.Draw(resultImages[i], resultImages[i].Bounds(), m, image.Point{0, 0}, draw.Over)
	}

	// encode map tile to bytes
	for i, result := range needMapReqResults {
		result.ImageBytes, result.Err = bfutil.ConvertImage2Bytes(resultImages[i], mapReq.ImageFormat)
		if result.Err != nil {
			return nil, fmt.Errorf("encode map tile %d failed, %w", i, result.Err)
		}
	}

	return needMapReqResults[0].ImageBytes, nil
}

// calculateLonLatForGoogleMapStatic calculate center point coordinates and direction
// x,y around map req
// slice index map to direction: 0:top-left, 1:top, 2:top-right, 3:left, 4:center, 5:right, 6:bottom-left, 7:bottom, 8:bottom-right
func calculateCenterLatLonForGoogleMapStatic(mapReq *MapReq) (lat, lon float64, direction int) {
	// Define the 8 surrounding positions (excluding center)
	surroundingPositions := []struct {
		pos     int   // position index
		dx, dy  int   // offset from center
		corners []int // affected corner indices
	}{
		{0, -1, -1, []int{0}},   // top-left
		{1, 0, -1, []int{0, 2}}, // top
		{2, 1, -1, []int{2}},    // top-right
		{3, -1, 0, []int{0, 6}}, // left
		{5, 1, 0, []int{2, 8}},  // right
		{6, -1, 1, []int{6}},    // bottom-left
		{7, 0, 1, []int{6, 8}},  // bottom
		{8, 1, 1, []int{8}},     // bottom-right
	}

	// Get tile bounds, the ** bottom-left** corner point is (minLat,minLon)
	minLat, minLon, maxLat, maxLon := GlobalMapsManager.Mercator.GoogleTile2LatLonBounds(mapReq.X, mapReq.Y, mapReq.Z)

	// Count unavailable tiles for each corner
	cornerCounts := make(map[int]int)
	tmpReq := mapReq.Clone()

	// Check each surrounding position
	for _, pos := range surroundingPositions {
		tmpReq.X = mapReq.X + pos.dx
		tmpReq.Y = mapReq.Y + pos.dy

		// Skip if tile is available (in processing or in DB)
		if GlobalMapsManager.sfg.IsProcessing(tmpReq.BlockKey()) {
			continue
		}
		if info, err := QueryTileInfoFromKeyValueDb(tmpReq.Key()); err == nil && info != nil && info.Status != 8 {
			continue
		}

		// Increment count for affected corners
		for _, corner := range pos.corners {
			cornerCounts[corner]++
			// If a corner has 3 unavailable tiles, return its coordinates and direction
			if cornerCounts[corner] == 3 {
				return getCoordinatesFromCorner(corner, minLat, minLon, maxLat, maxLon)
			}
		}
	}

	// If no unavailable corners found, use default (bottom-left)
	if len(cornerCounts) == 0 {
		minLat, minLon, _, _ := GlobalMapsManager.Mercator.GoogleTile2LatLonBounds(mapReq.X, mapReq.Y, mapReq.Z)
		return minLat, minLon, 6
	}

	// Find corner with the highest count
	maxCorner, maxCount := 6, 0
	for corner, count := range cornerCounts {
		if count > maxCount || (count == maxCount && corner < maxCorner) {
			maxCorner = corner
			maxCount = count
		}
	}

	return getCoordinatesFromCorner(maxCorner, minLat, minLon, maxLat, maxLon)
}

func getCoordinatesFromCorner(corner int, minLat, minLon, maxLat, maxLon float64) (lat, lon float64, cornerId int) {
	switch corner {
	case 0: // top-left
		return maxLat, minLon, 0
	case 2: // top-right
		return maxLat, maxLon, 2
	case 8: // bottom-right
		return minLat, maxLon, 8
	default: // bottom-left (6) or fallback
		return minLat, minLon, 6
	}
}

// 0: mapReq, 1:horizontalMapReq, 2:verticalMapReq, 3:diagonalMapReq
// slice index map to direction: 0:top-left, 2:top-right, 6:bottom-left, 8:bottom-right
func calculateNeedMapReqsAndImageRectsForGoogleMapStatic(
	mapReq *MapReq,
	direction int,
) (resultMapReqs []*MapReq, needImageRects []image.Rectangle) {
	// 初始化结果数组
	resultMapReqs = make([]*MapReq, 4)
	needImageRects = make([]image.Rectangle, 4)
	for i := 0; i < 4; i++ {
		resultMapReqs[i] = mapReq.Clone()
	}
	// center point of origin tile image in google map static(size is 640x640)
	centerX := 320
	centerY := 320

	switch direction {
	case 0: // top-left
		// mapReq tile is in right bottom side of center point
		needImageRects[0] = image.Rect(centerX, centerY, centerX+256, centerY+256)
		resultMapReqs[1].X += _XYOffsets[3].dx
		resultMapReqs[1].Y += _XYOffsets[3].dy
		// horizontalMapReq tile is in left bottom side of center point
		needImageRects[1] = image.Rect(centerX-256, centerY, centerX, centerY+256)
		resultMapReqs[2].X += _XYOffsets[1].dx
		resultMapReqs[2].Y += _XYOffsets[1].dy
		// verticalMapReq tile is in right top side of center point
		needImageRects[2] = image.Rect(centerX, centerY-256, centerX+256, centerY)
		resultMapReqs[3].X += _XYOffsets[0].dx
		resultMapReqs[3].Y += _XYOffsets[0].dy
		// diagonalMapReq tile is in left top side of center point
		needImageRects[3] = image.Rect(centerX-256, centerY-256, centerX, centerY)
	case 2: // top-right
		// mapReq tile is in left bottom side of center point
		needImageRects[0] = image.Rect(centerX-256, centerY, centerX, centerY+256)
		resultMapReqs[1].X += _XYOffsets[5].dx
		resultMapReqs[1].Y += _XYOffsets[5].dy
		// horizontalMapReq tile is in right bottom side of center point
		needImageRects[1] = image.Rect(centerX, centerY, centerX+256, centerY+256)
		resultMapReqs[2].X += _XYOffsets[1].dx
		resultMapReqs[2].Y += _XYOffsets[1].dy
		// verticalMapReq tile is in left top side of center point
		needImageRects[2] = image.Rect(centerX-256, centerY-256, centerX, centerY)
		resultMapReqs[3].X += _XYOffsets[2].dx
		resultMapReqs[3].Y += _XYOffsets[2].dy
		// diagonalMapReq tile is in right top side of center point
		needImageRects[3] = image.Rect(centerX, centerY-256, centerX+256, centerY)
	case 8: // bottom-right
		// map Req tile is in left top side of center point
		needImageRects[0] = image.Rect(centerX-256, centerY-256, centerX, centerY)
		resultMapReqs[1].X += _XYOffsets[5].dx
		resultMapReqs[1].Y += _XYOffsets[5].dy
		// horizontalMapReq tile is in right top side of center point
		needImageRects[1] = image.Rect(centerX, centerY-256, centerX+256, centerY)
		resultMapReqs[2].X += _XYOffsets[7].dx
		resultMapReqs[2].Y += _XYOffsets[7].dy
		// verticalMapReq tile is in left bottom side of center point
		needImageRects[2] = image.Rect(centerX-256, centerY, centerX, centerY+256)
		resultMapReqs[3].X += _XYOffsets[8].dx
		resultMapReqs[3].Y += _XYOffsets[8].dy
		// diagonalMapReq tile is in right bottom side of center point
		needImageRects[3] = image.Rect(centerX, centerY, centerX+256, centerY+256)
	default: // fallback to bottom-left
		// mapReq tile is in right top side of center point
		needImageRects[0] = image.Rect(centerX, centerY-256, centerX+256, centerY)
		resultMapReqs[1].X += _XYOffsets[3].dx
		resultMapReqs[1].Y += _XYOffsets[3].dy
		// horizontalMapReq tile is in left top side of center point
		needImageRects[1] = image.Rect(centerX-256, centerY-256, centerX, centerY)
		resultMapReqs[2].X += _XYOffsets[7].dx
		resultMapReqs[2].Y += _XYOffsets[7].dy
		// verticalMapReq tile is in right bottom side of center point
		needImageRects[2] = image.Rect(centerX, centerY, centerX+256, centerY+256)
		resultMapReqs[3].X += _XYOffsets[6].dx
		resultMapReqs[3].Y += _XYOffsets[6].dy
		// diagonalMapReq tile is in left bottom side of center point
		needImageRects[3] = image.Rect(centerX-256, centerY, centerX, centerY+256)
	}

	return
}
