package maps

import (
	"math"
)

//	TMS Global Mercator Profile
//	---------------------------

//	Functions necessary for generation of tiles in Spherical Mercator projection,
//	EPSG:900913 (EPSG:gOOglE, Google Maps Global Mercator), EPSG:3785, OSGEO:41001.

//	Such tiles are compatible with Google Maps, Microsoft Virtual Earth, Yahoo Maps,
//	UK Ordnance Survey OpenSpace API, ...
//	and you can overlay them on top of base maps of those web mapping applications.

//	Pixel and tile coordinates are in TMS notation (origin [0,0] in bottom-left).

//	What coordinate conversions do we need for TMS Global Mercator tiles::

//	     LatLon      <->       Meters      <->     Pixels    <->       Tile

//	 WGS84 coordinates   Spherical Mercator  Pixels in pyramid  Tiles in pyramid
//	     lat/lon            XY in metres     XY pixels Z zoom      XYZ from TMS
//	    EPSG:4326           EPSG:900913
//	     .----.              ---------               --                TMS
//	    /      \     <->     |       |     <->     /----/    <->      Google
//	    \      /             |       |           /--------/          QuadTree
//	     -----               ---------         /------------/
//	   KML, public         WebMapService         Web Clients      TileMapService

//	What is the coordinate extent of Earth in EPSG:900913?

//	  [-20037508.342789244, -20037508.342789244, 20037508.342789244, 20037508.342789244]
//	  Constant 20037508.342789244 comes from the circumference of the Earth in meters,
//	  which is 40 thousand kilometers, the coordinate origin is in the middle of extent.
//      In fact you can calculate the constant as: 2 * math.pi * 6378137 / 2.0
//	  $ echo 180 85 | gdaltransform -s_srs EPSG:4326 -t_srs EPSG:900913
//	  Polar areas with abs(latitude) bigger then 85.05112878 are clipped off.

//	What are zoom level constants (pixels/meter) for pyramid with EPSG:900913?

//	  whole region is on top of pyramid (zoom=0) covered by 256x256 pixels tile,
//	  every lower zoom level resolution is always divided by two
//	  initialResolution = 20037508.342789244 * 2 / 256 = 156543.03392804062

//	What is the difference between TMS and Google Maps/QuadTree tile name convention?

//	  The tile raster itself is the same (equal extent, projection, pixel size),
//	  there is just different identification of the same raster tile.
//	  Tiles in TMS are counted from [0,0] in the bottom-left corner, id is XYZ.
//	  Google placed the origin [0,0] to the top-left corner, reference is XYZ.
//	  Microsoft is referencing tiles by a QuadTree name, defined on the website:
//	  http://msdn2.microsoft.com/en-us/library/bb259689.aspx

//	The lat/lon coordinates are using WGS84 datum, yeh?

//	  Yes, all lat/lon we are mentioning should use WGS84 Geodetic Datum.
//	  Well, the web clients like Google Maps are projecting those coordinates by
//	  Spherical Mercator, so in fact lat/lon coordinates on sphere are treated as if
//	  the were on the WGS84 ellipsoid.

//	  From MSDN documentation:
//	  To simplify the calculations, we use the spherical form of projection, not
//	  the ellipsoidal form. Since the projection is used only for map display,
//	  and not for displaying numeric coordinates, we don't need the extra precision
//	  of an ellipsoidal projection. The spherical projection causes approximately
//	  0.33 percent scale distortion in the Y direction, which is not visually noticable.

//	How do I create a raster in EPSG:900913 and convert coordinates with PROJ.4?

//	  You can use standard GIS tools like gdalwarp, cs2cs or gdaltransform.
//	  All of the tools supports -t_srs 'epsg:900913'.

//	  For other GIS programs check the exact definition of the projection:
//	  More info at http://spatialreference.org/ref/user/google-projection/
//	  The same projection is degined as EPSG:3785. WKT definition is in the official
//	  EPSG database.

//	  Proj4 Text:
//	    +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0
//	    +k=1.0 +units=m +nadgrids=@null +no_defs

//	  Human readable WKT format of EPGS:900913:
//	     PROJCS["Google Maps Global Mercator",
//	         GEOGCS["WGS 84",
//	             DATUM["WGS_1984",
//	                 SPHEROID["WGS 84",6378137,298.2572235630016,
//	                     AUTHORITY["EPSG","7030"]],
//	                 AUTHORITY["EPSG","6326"]],
//	             PRIMEM["Greenwich",0],
//	             UNIT["degree",0.0174532925199433],
//	             AUTHORITY["EPSG","4326"]],
//	         PROJECTION["Mercator_1SP"],
//	         PARAMETER["central_meridian",0],
//	         PARAMETER["scale_factor",1],
//	         PARAMETER["false_easting",0],
//	         PARAMETER["false_northing",0],
//	         UNIT["metre",1,
//	             AUTHORITY["EPSG","9001"]]]

type Mercator struct {
	tileSize          int
	initialResolution float64
	originShift       float64
}

func NewMercator(tileSize int) *Mercator {
	return &Mercator{
		tileSize:          tileSize,
		initialResolution: math.Pi * 2.0 * 6378137.0 / float64(tileSize),
		originShift:       math.Pi * 2.0 * 6378137.0 / 2.0,
	}
}

func (mer *Mercator) GoogleTile2LatLonBounds(gx, gy, gz int) (minLat, minLon, maxLat, maxLon float64) {
	tx, ty := mer.GoogleTiles(gx, gy, gz)
	return mer.TileLatLonBounds(tx, ty, gz)
}
func (mer *Mercator) TileLatLonBounds(tx, ty, tz int) (minLat, minLon, maxLat, maxLon float64) {
	minx, miny, maxx, maxy := mer.TileBounds(tx, ty, tz)
	minLat, minLon = mer.MetersToLatLon(minx, miny)
	maxLat, maxLon = mer.MetersToLatLon(maxx, maxy)
	return minLat, minLon, maxLat, maxLon
}

// TileBounds calc the bounds of given tile
func (m *Mercator) TileBounds(tx, ty, zoom int) (minLat, minLon, maxLat, maxLon float64) {
	minLat, minLon = m.PixelsToMeters(float64(tx*m.tileSize), float64(ty*m.tileSize), zoom)
	maxLat, maxLon = m.PixelsToMeters(float64((tx+1)*m.tileSize), float64((ty+1)*m.tileSize), zoom)
	return
}

// Converts given lat/lon in WGS84 Datum to XY in Spherical Mercator EPSG:900913
func (m *Mercator) LatLonToMeters(lat, lon float64) (mx, my float64) {
	mx = lon * m.originShift / 180.0
	my = math.Log(math.Tan((90.0+lat)*math.Pi/360.0)) / (math.Pi / 180.0)
	my = my * m.originShift / 180.0
	return
}

// Converts XY point from Spherical Mercator EPSG:900913 to lat/lon in WGS84 Datum
func (m *Mercator) MetersToLatLon(mx, my float64) (lat, lon float64) {
	lon = (mx / m.originShift) * 180.0
	lat = (my / m.originShift) * 180.0
	lat = 180.0 / math.Pi * (2.0*math.Atan(math.Exp(lat*math.Pi/180.0)) - math.Pi/2.0)
	return
}

// Resolution 分辨率
func (m *Mercator) Resolution(zoom int) float64 {
	return m.initialResolution / math.Pow(2, float64(zoom))
}

// Converts EPSG:900913 to pyramid pixel coordinates in given zoom level
func (m *Mercator) MetersToPixels(mx, my float64, zoom int) (px, py float64) {
	res := m.Resolution(zoom)
	px = (mx + m.originShift) / res
	py = (my + m.originShift) / res
	return
}

// Converts pixel coordinates in given zoom level of pyramid to EPSG:900913
func (m *Mercator) PixelsToMeters(px, py float64, zoom int) (mx float64, my float64) {
	res := m.Resolution(zoom)
	mx = px*res - m.originShift
	my = py*res - m.originShift
	return
}

// Converts given lat/lon in WGS84 Datum to pyramid pixel coordinates in given zoom level
func (m *Mercator) LatLonToPixels(lat, lon float64, zoom int) (px, py float64) {
	mx, my := m.LatLonToMeters(lat, lon)
	px, py = m.MetersToPixels(mx, my, zoom)
	return
}

// Converts pixel coordinates in given zoom level of pyramid to lat/lon in WGS84 Datum
func (m *Mercator) PixelsToLatLon(px, py float64, zoom int) (lat, lon float64) {
	mx, my := m.PixelsToMeters(px, py, zoom)
	lat, lon = m.MetersToLatLon(mx, my)
	return
}

// Converts  pixel coordinates to TMS tile covering region
func (m *Mercator) PixelsToTiles(px, py float64, zoom int) (tx, ty int) {
	tx = int(math.Floor(px / float64(m.tileSize)))
	ty = int(math.Floor(py / float64(m.tileSize)))
	maxTileIndex := int(math.Pow(2, float64(zoom))) - 1
	if ty > maxTileIndex {
		ty = maxTileIndex
	}
	if tx > maxTileIndex {
		tx = maxTileIndex
	}
	return
}

func (m *Mercator) MetersToTile(mx, my float64, zoom int) (tx, ty int) {
	px, py := m.MetersToPixels(mx, my, zoom)
	tx, ty = m.PixelsToTiles(px, py, zoom)
	return
}

// Converts given lat/lon in WGS84 Datum to tile coordinates in given zoom level
func (m *Mercator) LatLonToTiles(lat, lon float64, zoom int) (tx, ty int) {
	mx, my := m.LatLonToMeters(lat, lon)
	tx, ty = m.MetersToTile(mx, my, zoom)
	return
}

// GoogleTiles convert tms tile to google tile
// revert of y-axis
func (mer *Mercator) GoogleTiles(gx, gy, gz int) (tx, ty int) {
	return gx, int(math.Pow(2, float64(gz))-1) - gy
}

func (mer *Mercator) LatLonToGoogleTiles(lat, lon float64, zoom int) (gx, gy int) {
	tx, ty := mer.LatLonToTiles(lat, lon, zoom)
	gx, gy = mer.GoogleTiles(tx, ty, zoom)
	return
}

// GooglePixels convert  pixel coordinates between tms and google map,
// revert of y-axis
func (m *Mercator) GooglePixels(px, py float64, zoom int) (gx, gy float64) {
	return px, float64(m.tileSize)*math.Pow(2, float64(zoom)) - py
}

// calc pixel distance to tile top left
func (m *Mercator) PixelDistanceBetweenPixelAndTileTopLeft(px, py float64, zoom int) (dx, dy int) {
	tx, ty := m.PixelsToTiles(px, py, zoom)
	dx = int(px - float64(tx*m.tileSize))
	dy = int(py - float64(ty*m.tileSize))
	return
}

// calc pixel distance to tile center
func (m *Mercator) PixelDistanceBetweenPixelAndTileCenter(px, py float64, zoom int) (dx, dy int) {
	tx, ty := m.PixelsToTiles(px, py, zoom)
	centerIndex := m.tileSize / 2
	dx = int(px-float64(tx*m.tileSize)) - centerIndex
	dy = int(py-float64(ty*m.tileSize)) - centerIndex
	return
}

//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
//func(mer*Mercator)()(){

//}
