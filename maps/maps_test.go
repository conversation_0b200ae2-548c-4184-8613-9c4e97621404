package maps

import (
	"fmt"
	"io"
	"math"
	"net/http"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

type LoadTestResult struct {
	TotalRequests      int64
	SuccessRequests    int64
	FailedRequests     int64
	TotalLatency       time.Duration
	MinLatency         time.Duration
	MaxLatency         time.Duration
	AverageLatency     time.Duration
	RequestsPerSecond  float64
	ConcurrentRequests int
	Duration           time.Duration
}

func runLoadTest(baseURL string, token string, concurrency int, duration time.Duration) (*LoadTestResult, error) {
	result := &LoadTestResult{
		MinLatency:         time.Hour, // Initialize with a large value
		ConcurrentRequests: concurrency,
		Duration:           duration,
	}

	var wg sync.WaitGroup
	start := time.Now()
	stop := make(chan bool)

	zoom := 16
	maxTileIdx := int(math.Pow(2, float64(zoom)) - 1)
	x, y := 0, 0
	reqCounter := uint64(0)
	// Start concurrent workers
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for {
				select {
				case <-stop:
					return
				default:
					// Round-robin through sample requests
					id := atomic.AddUint64(&reqCounter, 1) - 1
					x = int(id % uint64(maxTileIdx+1))
					y = int((id / uint64(maxTileIdx+1)) % uint64(maxTileIdx+1))

					url := fmt.Sprintf("%s/map?token=%s&x=%d&y=%d&z=%d&mtype=roadmap&lang=en&provider=tianditu",
						baseURL, token, x, y, zoom)

					reqStart := time.Now()
					resp, err := http.Get(url)

					atomic.AddInt64(&result.TotalRequests, 1)

					if err != nil {
						atomic.AddInt64(&result.FailedRequests, 1)
						continue
					}

					_, err = io.ReadAll(resp.Body)
					resp.Body.Close()
					latency := time.Since(reqStart)

					if err != nil {
						atomic.AddInt64(&result.FailedRequests, 1)
						continue
					}

					atomic.AddInt64(&result.SuccessRequests, 1)
					atomic.AddInt64((*int64)(&result.TotalLatency), int64(latency))

					// Update min/max latency (using atomic operations for thread safety)
					for {
						current := time.Duration(atomic.LoadInt64((*int64)(&result.MinLatency)))
						if latency >= current {
							break
						}
						if atomic.CompareAndSwapInt64((*int64)(&result.MinLatency), int64(current), int64(latency)) {
							break
						}
					}

					for {
						current := time.Duration(atomic.LoadInt64((*int64)(&result.MaxLatency)))
						if latency <= current {
							break
						}
						if atomic.CompareAndSwapInt64((*int64)(&result.MaxLatency), int64(current), int64(latency)) {
							break
						}
					}
				}
			}
		}()
	}

	// Wait for test duration
	time.Sleep(duration)
	close(stop)
	wg.Wait()

	// Calculate final statistics
	totalDuration := time.Since(start)
	result.RequestsPerSecond = float64(result.TotalRequests) / totalDuration.Seconds()
	if result.SuccessRequests > 0 {
		result.AverageLatency = time.Duration(int64(result.TotalLatency) / result.SuccessRequests)
	}

	return result, nil
}

func Test_MapHttpHandler(t *testing.T) {
	// Configuration
	baseURL := "http://localhost:2242"                     // Change this to your server URL
	token := "0195e9e9-2339-7ff6-aa9d-27902b68f4e3"        // Change this to a valid token
	concurrency := []int{1000, 5000, 10000, 50000, 100000} // Test different concurrency levels
	duration := 30 * time.Second                           // Test duration for each concurrency level

	for _, c := range concurrency {
		t.Run(fmt.Sprintf("Concurrent_%d", c), func(t *testing.T) {
			result, err := runLoadTest(baseURL, token, c, duration)
			if err != nil {
				t.Fatalf("Load test failed: %v", err)
			}

			// Print test results
			t.Logf("=== Load Test Results (Concurrent Requests: %d) ===", c)
			t.Logf("Total Requests: %d", result.TotalRequests)
			t.Logf("Successful Requests: %d", result.SuccessRequests)
			t.Logf("Failed Requests: %d", result.FailedRequests)
			t.Logf("Requests/Second: %.2f", result.RequestsPerSecond)
			t.Logf("Average Latency: %v", result.AverageLatency)
			t.Logf("Min Latency: %v", result.MinLatency)
			t.Logf("Max Latency: %v", result.MaxLatency)
			t.Logf("Success Rate: %.2f%%", float64(result.SuccessRequests)/float64(result.TotalRequests)*100)
		})
	}
}
