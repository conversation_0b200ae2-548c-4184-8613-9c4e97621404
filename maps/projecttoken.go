package maps

import (
	"bfmap/bfutil"
	"bfmap/dbproto"
	"log/slog"
	"sync/atomic"
	"time"

	"google.golang.org/protobuf/encoding/protojson"
)

type MapProjectToken struct {
	TokenRid   string
	Token      string
	ProjectRid string
	ExpireTime time.Time
	Expiable   bool
	// 1:active 4:disable
	Status               int32
	AvailableMapProvider *dbproto.TAvailableMapProvider
	SysNames             *dbproto.SysNames

	// usage statistics
	countStartTime *bfutil.AtomicTime
	UsageCount     atomic.Int32
}

func NewMapProjectToken(projectToken *dbproto.DbProjectToken) *MapProjectToken {
	provider := &dbproto.TAvailableMapProvider{}
	err := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}.Unmarshal([]byte(projectToken.AvailableMapProvider), provider)
	if err != nil {
		slog.Error("NewMapProjectToken unmarshal available map provider fail", "err", err)
		provider = &dbproto.TAvailableMapProvider{}
	}

	Expiable := projectToken.ExpireTime != 0
	v := &MapProjectToken{
		Token:                projectToken.Token,
		ProjectRid:           projectToken.ProjectRid,
		AvailableMapProvider: provider,
		SysNames:             projectToken.SysName,
		Status:               projectToken.Status,
		ExpireTime:           time.Unix(projectToken.ExpireTime, 0),
		Expiable:             Expiable,
		countStartTime:       bfutil.NewAtomicTime(bfutil.CurrentUTCTime()),
		UsageCount:           atomic.Int32{},
	}

	return v
}

func (t *MapProjectToken) DefaultProvider(mapType string) int {
	switch mapType {
	case MapTypeRoadmap:
		return int(t.AvailableMapProvider.DefaultRoadmap)
	case MapTypeSatellite:
		return int(t.AvailableMapProvider.DefaultSatellite)
	case MapTypeHybrid:
		return int(t.AvailableMapProvider.DefaultHybrid)
	}
	return -1
}

func (t *MapProjectToken) IsMapProviderAllowed(provider int) bool {
	if t.AvailableMapProvider == nil {
		return false
	}
	switch provider {
	case int(dbproto.MapProviderEnum_ProviderGoogle):
		return t.AvailableMapProvider.Google
	case int(dbproto.MapProviderEnum_ProviderTianditu):
		return t.AvailableMapProvider.Tianditu
	case int(dbproto.MapProviderEnum_ProviderOSM):
		return t.AvailableMapProvider.OSM
	}
	return false
}

func (t *MapProjectToken) IsExpired() bool {
	return t.Expiable && t.ExpireTime.Before(bfutil.CurrentUTCTime())
}

func (t *MapProjectToken) IsActive() bool {
	return t.Status == 1
}

func (t *MapProjectToken) AddOneUsageCount() {
	t.UsageCount.Add(1)

	// update map project usage count
	p, _ := GlobalMapsManager.GetMapProjectCache(t.ProjectRid)
	if p == nil {
		return
	}
	p.AddOneUsageCount()

	if t.UsageCount.Load() >= 100 {
		go t.RotateUsageCount()
	}
}

func (t *MapProjectToken) RotateUsageCount() {
	if t.UsageCount.Load() == 0 {
		return
	}

	// reset usage count
	tim := t.countStartTime.Swap(bfutil.CurrentUTCTime())
	count := t.UsageCount.Swap(0)

	// save usage count to db
	err := InsertDbProjectTokenUsage(
		t.ProjectRid,
		t.Token,
		tim.Unix(),
		count)
	if err != nil {
		slog.Warn("MapProjectToken.RotateUsageCount save fail", "error", err)
	}
}

func (t *MapProjectToken) SyncCacheToDb() {
	t.RotateUsageCount()
}

func (t *MapProjectToken) HasSysName(name string) bool {
	if t.SysNames == nil {
		return false
	}
	for _, v := range t.SysNames.Names {
		if v == name {
			return true
		}
	}
	return false
}
