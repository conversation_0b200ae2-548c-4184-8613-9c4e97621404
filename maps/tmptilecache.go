package maps

import (
	"time"

	"github.com/maypok86/otter"
)

type TmapTileCache struct {
	cache otter.Cache[string, []byte]
}

var GlobalTmapTileCache *TmapTileCache

func init() {
	var err error
	GlobalTmapTileCache, err = NewTmapTileCache()
	if err != nil {
		panic(err)
	}
}

// NewTmapTileCache returns a new TmapTileCache.
func NewTmapTileCache() (*TmapTileCache, error) {
	r := &TmapTileCache{}

	var err error
	r.cache, err = otter.
		MustBuilder[string, []byte](10000).
		Cost(func(key string, value []byte) uint32 {
			return 1
		}).
		WithTTL(2 * time.Minute).Build()

	return r, err
}

// set tile to cache
func (r *TmapTileCache) SetTile(key string, value []byte) {
	r.cache.Set(key, value)
}

func CacheTile(key string, value []byte) {
	GlobalTmapTileCache.cache.Set(key, value)
}

// get tile from cache
func (r *TmapTileCache) GetTile(key string) ([]byte, bool) {
	return r.cache.Get(key)
}

func GetCacheTile(key string) ([]byte, bool) {
	return GlobalTmapTileCache.cache.Get(key)
}
