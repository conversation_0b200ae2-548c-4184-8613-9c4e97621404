package maps

import (
	"bfmap/bfutil"
	"bfmap/config"
	"bytes"
	"fmt"
	"image"
	"io"
	"log/slog"
	"net/http"
	"strings"
	"sync"
	"time"

	"golang.org/x/image/draw"
)

// http://t{num}.tianditu.gov.cn/DataServer?T={layer_proj}&x={x}&y={y}&l={z}&tk={tk}
const TiandituTileApiUrlTemplate1 = "%s/DataServer?T=%s&x=%d&y=%d&l=%d&tk=%s"

// http://t{num}.tianditu.gov.cn/{layer_proj}/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&
// LAYER={layer}&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk={tk}
// nolint
const TiandituTileApiUrlTemplate2 = "%s/%s/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=%s&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL=%d&TILEROW=%d&TILEMATRIX=%d&tk=%s"

func ReqMapTileFromTianditu(
	providerToken string,
	mapType string,
	x, y, z int,
	imageFormat string,
	BaseUrl string,
	needProxy bool) ([]byte, error) {
	// 第1个是底图，第2个是注记
	needLayers := make([]string, 0)
	switch mapType {
	case MapTypeRoadmap:
		needLayers = append(needLayers, "vec_w", "cva_w")
	case MapTypeSatellite:
		needLayers = append(needLayers, "img_w")
	case MapTypeHybrid:
		needLayers = append(needLayers, "img_w", "cia_w")
	default:
		return nil, fmt.Errorf("unknown map type: %s", mapType)
	}

	if len(needLayers) == 1 {
		return ReqMapTileLayerFromTianditu(providerToken, x, y, z, needLayers[0], BaseUrl, needProxy)
	}

	type layerResult struct {
		img image.Image
		err error
	}

	results := make([]layerResult, len(needLayers))
	var wg sync.WaitGroup

	for i, layer := range needLayers {
		wg.Add(1)
		go func(index int, layerType string) {
			defer wg.Done()

			b, err := ReqMapTileLayerFromTianditu(
				providerToken,
				x, y, z,
				layerType,
				BaseUrl,
				needProxy)
			if err != nil {
				results[index].err = err
				return
			}

			img, _, err := image.Decode(bytes.NewReader(b))
			if err != nil {
				results[index].err = err
				return
			}

			results[index].img = img
		}(i, layer)
	}

	wg.Wait()

	for i, result := range results {
		if result.err != nil {
			return nil, fmt.Errorf("fetching layer %s failed: %w", needLayers[i], result.err)
		}
	}

	m := image.NewRGBA(image.Rect(0, 0, 256, 256))

	draw.Draw(m, m.Bounds(), results[0].img, image.Point{0, 0}, draw.Src)
	for i := 1; i < len(results); i++ {
		draw.Draw(m, m.Bounds(), results[i].img, image.Point{0, 0}, draw.Over)
	}

	return bfutil.ConvertImage2Bytes(m, imageFormat)
}

// layerProj: 矢量底图vec_c/vec_w,矢量注记cva_c/cva_w,卫星底图img_c/img_w,卫星注记cia_c/cia_w.
// _c 经纬度投影,_w 墨卡托投影.
func ReqMapTileLayerFromTianditu(
	providerToken string,
	x, y, z int,
	layerProj string,
	BaseUrl string,
	needProxy bool) ([]byte, error) {
	if len(BaseUrl) == 0 {
		BaseUrl = config.DefaultTiandituMapStaticApiBaseUrl
	} else {
		BaseUrl = strings.TrimSuffix(BaseUrl, "/")
	}

	urlStr := fmt.Sprintf(
		TiandituTileApiUrlTemplate2,
		//nolint
		BaseUrl,
		layerProj,
		strings.SplitN(layerProj, "_", 2)[0],
		x,
		y,
		z,
		providerToken,
	)
	if config.IsVerboseDebugMap {
		slog.Debug("req tianditu map layer", "url", urlStr)
	}

	httpClient := GetMapHttpClient(10*time.Second, needProxy)
	resp, err := httpClient.Get(urlStr)
	if err != nil {
		return nil, fmt.Errorf("get map layer from tianditu map failed, %w", err)
	}
	defer resp.Body.Close()

	data, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get map layer from tianditu map failed with status code %d, %s",
			resp.StatusCode, string(data))
	}

	return data, nil
}
