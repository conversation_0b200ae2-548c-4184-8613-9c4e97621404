package maps

const (
	MapProviderGoogle   = "google"
	MapProviderTianDiTu = "tianditu"
	MapProviderOSM      = "osm"

	// 图片格式为png.
	MapTypeRoadmap = "roadmap"
	// 图片格式为jpg，jpg-baseline优先.
	MapTypeSatellite = "satellite"
	// 图片格式为jpg，jpg-baseline优先.
	MapTypeHybrid = "hybrid"
	// MapTypeTerrain = "terrain".

	TileImageFormatPng         = "png"
	TileImageFormatJpg         = "jpg"
	TileImageFormatJpeg        = "jpeg"
	TileImageFormatJpgBaseline = "jpg-baseline"

	mapReqNatSubject = "map.req"

	MapProviderHeaderName  = "Map-Provider"
	MapCacheTimeHeaderName = "Cache-Time"

	MapTileStatusHeaderName = "Map-Tile-Status"
)
