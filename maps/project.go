package maps

import (
	"bfmap/bfutil"
	"bfmap/dbproto"
	"log/slog"
	"sync/atomic"
	"time"
)

type MapProject struct {
	Name       string
	ProjectRid string
	UserRid    string
	// 1:active 4:disable
	Status int32

	PeriodQuotas
	// mark for cache is different from db,need update db
	isDirty atomic.Bool
}

func NewMapProject(project *dbproto.DbProject, projectQuotas *dbproto.DbProjectQuotas) *MapProject {
	v := &MapProject{
		Name:       project.Name,
		ProjectRid: project.Rid,
		UserRid:    project.UserRid,
		Status:     project.Status,
		PeriodQuotas: PeriodQuotas{
			QuotasType:     atomic.Int32{},
			QuotasLimit:    atomic.Int32{},
			UsedCount:      atomic.Int32{},
			CountStartTime: bfutil.NewAtomicTime(time.Unix(projectQuotas.CountStartTime, 0)),
		},
		isDirty: atomic.Bool{},
	}

	v.QuotasType.Store(projectQuotas.QuotasType)
	v.QuotasLimit.Store(projectQuotas.QuotasLimit)
	v.UsedCount.Store(projectQuotas.CurrentUsedQuotas)

	current := bfutil.CurrentUTCTime()
	go v.CheckAndResetQuotas(current, TotalMonth(current))

	return v
}

func (m *MapProject) AddOneUsageCount() {
	m.UsedCount.Add(1)
	m.isDirty.Store(true)
}

func (m *MapProject) IsActive() bool {
	return m.Status == 1
}

func (m *MapProject) CheckAndResetQuotas(time time.Time, month int) {
	if m.CheckIfShouldReset(time, month) {
		m.ResetQuotas()
		m.isDirty.Store(true)
	}
}

func (m *MapProject) SyncCacheToDb() {
	if !m.isDirty.CompareAndSwap(true, false) {
		// cache is not dirty
		return
	}

	err := UpdateDbProjectQuotas(m)
	if err != nil {
		slog.Warn("MapProject.SyncCacheToDb UpdateDbProjectQuotas fail", "error", err)
		m.isDirty.Store(true)
		return
	}
}
