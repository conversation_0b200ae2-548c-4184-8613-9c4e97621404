import globals from "globals"
import pluginJs from "@eslint/js"
import vue from 'eslint-plugin-vue'
import typescript from '@typescript-eslint/eslint-plugin'
import typescriptParser from '@typescript-eslint/parser'
import vueParser from 'vue-eslint-parser'
import { fileURLToPath } from 'node:url'
import { dirname } from 'node:path'

const __dirname = dirname(fileURLToPath(import.meta.url))

/** @type {import('eslint').Linter.Config[]} */
export default [
  {
    languageOptions: {
      globals: globals.browser,
    },
  },
  {
    ignores: [
      ".gitignore",
      "build/",
      "coverage/",
      "dist/",
      "src/proto/",
      "src/vite-env.d.ts",
    ],
  },
  {
    languageOptions: {
      globals: {
        window: 'writable', // 通常也需要声明 window
        document: 'writable', // 以及 document

        defineProps: 'readonly',
        defineEmits: 'readonly',
        defineExpose: 'readonly',
        withDefaults: 'readonly'
      },
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
  },
  pluginJs.configs.recommended,  //  ESLint 官方推荐的基本规则
  {
    files: ['**/*.vue'], // 处理 .vue 文件
    processor: vue.processors['.vue'],
    languageOptions: {
      parser: vueParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        // 重要：需要告诉 vue-eslint-parser 支持 TS
        parser: typescriptParser,
      }
    },
    plugins: {
      vue,
    },
    rules: {
      ...vue.configs.essential.rules, //  Vue 3 essential 规则
      'vue/multi-word-component-names': 'off',
    },
  },
  {
    files: ['**/*.ts', '**/*.tsx'], // 处理 .ts 和 .tsx 文件
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        project: './tsconfig.app.json',
        tsconfigRootDir: __dirname,
        ecmaVersion: 'latest',
        sourceType: 'module',
        extraFileExtensions: ['.vue']
      },
    },
    settings: {
      'import/resolver': {
        typescript: {
          project: './tsconfig.app.json'
        }
      }
    },
    plugins: {
      '@typescript-eslint': typescript,
    },
    rules: {
      ...typescript.configs['recommended'].rules, //  TypeScript 推荐规则
      '@typescript-eslint/no-unused-vars': 'warn', // TypeScript 未使用的变量视为警告
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },
  {
    files: ['vite.config.ts'],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        project: './tsconfig.node.json',
        tsconfigRootDir: __dirname,
        ecmaVersion: 'latest',
        sourceType: 'module',
      },
      globals: {
        process: true,
      },
    },
    settings: {
      'import/resolver': {
        typescript: {
          project: './tsconfig.node.json'
        }
      }
    },
    plugins: {
      '@typescript-eslint': typescript,
    },
    rules: {
      ...typescript.configs['recommended'].rules, //  TypeScript 推荐规则
    },
  },
  {
    rules: {
      'no-unused-vars': 'warn', //  通用的未使用的变量警告 (覆盖其他配置)
    },
  },
]
