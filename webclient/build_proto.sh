#!/usr/bin/env bash

cd $(dirname "$0")

myBinDir="./node_modules/.bin"

if [[ ! -d "$myBinDir" ]]; then
  echo "Please run 'pnpm install' first"
  exit 1
fi

protoDir="../proto"

if [[ ! -d "$protoDir" ]]; then
  echo "Please check the proto directory is exist"
  exit 1
fi

outputDir="./src/proto"

# build db proto
echo "Building db proto..."
protoc -I=$protoDir --plugin=protoc-gen-es=$myBinDir/protoc-gen-es --es_out=target=ts:$outputDir $protoDir/protodb.proto $protoDir/db.proto

# build rpc proto
echo "Building rpc proto..."
protoc -I=$protoDir --plugin=protoc-gen-es=$myBinDir/protoc-gen-es --es_out=target=ts:$outputDir $protoDir/bfmap.rpc.proto

echo "Building proto done! See $outputDir for the generated files."
exit 0
