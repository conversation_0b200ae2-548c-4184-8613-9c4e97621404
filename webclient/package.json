{"name": "webclient", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build-proto": "./build_proto.sh", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.ts,.tsx"}, "dependencies": {"@bufbuild/protobuf": "^2.2.3", "@connectrpc/connect": "^2.0.2", "@connectrpc/connect-web": "^2.0.2", "@quasar/extras": "^1.16.17", "@types/maplibre-gl": "^1.14.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "localforage": "^1.10.0", "lodash": "^4.17.21", "loglevel": "^1.9.2", "maplibre-gl": "^5.5.0", "nats.ws": "^1.30.3", "pinia": "^3.0.1", "quasar": "^2.18.1", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-echarts": "^6.7.3", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "ypubsub": "^1.0.15"}, "devDependencies": {"@bufbuild/protoc-gen-es": "^2.2.3", "@eslint/js": "^9.23.0", "@quasar/vite-plugin": "^1.9.0", "@tailwindcss/vite": "^4.1.0", "@types/crypto-js": "^4.2.2", "@types/eslint": "^9.6.1", "@types/lodash": "^4.17.16", "@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-standard": "^9.0.1", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.23.0", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "sass-embedded": "^1.80.2", "tailwindcss": "^4.1.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-plugin-eslint": "^1.8.1", "vue-eslint-parser": "^10.1.1", "vue-tsc": "^2.2.4"}}