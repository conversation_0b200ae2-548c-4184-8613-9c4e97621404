{"defaultOrgName": "默认单位名称", "defaultProjectName": "默认项目名称", "copyrightInfo": "© 福建北峰通信科技股份有限公司", "common": {"back": "返回", "cancel": "取消", "confirm": "确认", "operationSuccess": "操作成功", "operationFailed": "操作失败", "operationProgress": "处理中... {progress}%", "operationPartialSuccess": "操作部分成功：{success}个成功，{fail}个失败", "sessionExpired": "登录会话过期", "sessionExpiredDesc": "长时间未操作，登录已过期，请重新登录", "backToLogin": "返回登录页", "yes": "是", "no": "否", "permissionDenied": "暂无权限"}, "errorPage": {"notFoundDesc": "抱歉，您访问的页面不存在...", "backHome": "返回首页"}, "form": {"back": "@:common.back", "cancel": "@:common.cancel", "confirm": "@:common.confirm", "confirmSubmit": "确认提交", "submit": "提交", "add": "添加", "edit": "编辑", "delete": "删除", "deleteConfirm": "确认删除", "batchDelete": "批量删除", "addSuccess": "添加成功", "search": "搜索", "deleteSuccess": "删除成功", "updateSuccess": "更新成功", "keepAdd": "继续添加"}, "password": "密码", "setup": {"confirmSubmitMsg": "该操作无法撤消，请确认数据无误后点击确认提交！", "jumpNow": "立即跳转", "setFailed": "设置失败", "setSuccess": "设置成功", "setupFailedMessage": "设置失败，请检查用户名和密码是否正确", "willJumpToHome": "{second}秒后，即将自动跳转到首页"}, "siteTitle": "北峰地图API管理平台", "userName": "用户名", "validate": {"allowedSpecifyChars": "只能输入字母、数字和指定特殊字符（{chars}）", "passwordAtLeast": "密码长度至少为{length}位", "required": "此项必填", "phoneValidFail": "手机号格式不正确", "emailValidFail": "邮箱格式不正确", "nameUnique": "名称已存在，不可重复", "numberRange": "输入范围：{min} - {max}", "sysNameUnique": "系统名称已存在"}, "welcome": "欢迎使用@:siteTitle", "welcomeInfo": "初次使用请设置默认管理员账号信息", "pages": {"dashboard": "API用量", "dbOrg": "单位", "dbUser": "用户", "dbProject": "项目和API密钥", "dbProjectToken": "API密钥", "dbMapProviderToken": "地图图源API密钥", "mapGl": "地图展示"}, "myLogin": {"userName": "@:userName", "password": "@:password", "rememberAccount": "记住账号/密码", "signIn": "登录", "loginSuccessful": "登录成功", "loginFailed": "登录失败！", "ReqTimeTooOld": "@:myLogin.loginFailed 客户端时间与服务器时间相差超过五分钟", "UserNotExist": "@:myLogin.loginFailed 用户不存在", "PasswordNotMatch": "@:myLogin.loginFailed 用户名或密码错误", "SessionIdNotExist": "@:myLogin.loginFailed 登录密钥不存在", "SessionIdExpired": "@:myLogin.loginFailed 登录密钥已失效", "SessionAlreadyLogin": "@:myLogin.loginFailed 登录密钥已在其他地方登录使用，请使用密码重新登录", "FailWithInternalError": "@:myLogin.loginFailed 服务器异常，请稍后再试", "UserDisabled": "@:myLogin.loginFailed 您的账号已被禁用，请联系管理员"}, "language": {"zh": "中文", "en": "English"}, "myLayout": {"logOut": "退出登录", "newAction": "新建...", "newProject": "新建项目", "newUser": "新建用户", "newApiKey": "新建API密钥", "newMapApiKey": "新建地图图源API密钥"}, "dbUser": {"nickname": "昵称", "email": "邮箱", "phone": "手机号码", "note": "备注", "userManager": "用户管理", "deleteConfirm": "删除当前数据将不可找回，请确认是否继续？", "canEditUser": "可编辑用户", "operate": "操作", "createTime": "创建时间", "selectTooltip": "按住shift可进行范围选择", "noData": "暂无数据", "wantToDeleteUser": "是否删除该用户", "userPrivilegeSetting": "用户权限设置", "userStatus": "用户状态", "disableSuccess": "用户禁用成功", "enableSuccess": "用户启用成功", "isDisableUser": "是否禁用用户", "wantToDisableUser": "是否禁用该用户？", "wantToEnableUser": "是否启用该用户？"}, "dbProject": {"quotasType": "配额类型", "quotasLimit": "配额数量", "usedQuotas": "当前用量", "quotasCalculateBy": "按{type}计算", "minute": "分钟", "hour": "小时", "day": "天", "month": "月", "unlimited": "无限制", "wantToDisableProject": "是否禁用该项目?", "wantToEnableProject": "是否启用该项目?", "wantToDeleteProject": "是否删除该项目?", "deleteProject": "删除项目", "name": "项目名称", "project": "项目", "projectUpdateFail": "项目设置失败", "quotaUpdateFail": "配额设置失败", "creator": "创建者", "apiCount": "api密钥数量"}, "dbProjectToken": {"googleMap": "谷歌地图", "tianditu": "天地图", "openStreetMap": "OpenStreetMap", "localDirectory": "本地目录", "availableMaps": "可用于哪个地图平台", "expiryDate": "到期日期", "alwaysValid": "永久有效", "wantToDisabledToken": "是否禁用API密钥？", "wantToEnableToken": "是否启用API密钥？", "wantToDeleteToken": "是否删除API密钥？", "disable": "禁用", "enable": "启用", "apiKey": "API密钥", "status": "状态", "defaultRoadmap": "默认街道图", "defaultSatellite": "默认影像图（仅底图）", "defaultHybrid": "默认影像图（带标注）", "sysNameHint": "按下回车键添加", "maxSysName": "最多添加32个系统名称"}, "dbMapProviderToken": {"apiKey": "API密钥", "mapPlatform": "地图平台", "apiKeyByMapPlatform": "地图平台提供的API密钥", "quotaCalcMethod": "配额计算方式", "monthly": "每月", "daily": "每日", "quotasUnlimited": "0为不限额", "googleMapType": "地图API类型", "baseUrl": {"default": "默认请求地址", "google": "请输入Google Maps API的基础URL", "tianditu": "请输入天地图API的基础URL", "osm": "请输入OpenStreetMap API的基础URL", "googleLocal": "请输入Google Maps瓦片的本地目录路径", "tiandituLocal": "请输入天地图瓦片的本地目录路径", "osmLocal": "请输入OpenStreetMap瓦片的本地目录路径"}, "mapApiStatic": "静态地图API", "mapApiTile": "瓦片地图API", "mapApiAll": "全部", "apiName": "API密钥名称", "copyToClipboardSuccess": "成功复制到剪贴板", "clickToCopySysName": "点击复制系统名称", "clickToCopyApiKey": "点击复制API密钥", "sysName": "应用系统名称", "clickToLookSysName": "点击查看可应用的系统名称", "priority": "优先级", "priorityDesc": "优先级越高，越优先使用", "isUseProxy": "是否使用代理"}, "dbOrg": {"name": "单位名称", "owner": "归属人", "wantToDeleteOrg": "是否删除该单位？", "org": "单位"}, "dashboard": {"projectCurrentUsage": "项目当前用量统计", "tokenCurrentUsage": "[{name}]项目API密钥当前用量统计", "mapApiQuotasChartTitle": "地图图源API密钥当前用量统计", "mapApiQuotasChartAllUsageTitle": "地图图源API密钥总用量统计", "apiKey": "API密钥", "usage": "使用量", "projectStatistics": "项目统计", "currentUsage": "当前用量", "totalUsage": "总用量", "alreadyUsedAPICount": "已用API密钥数量", "clickToDetail": "点击查看API密钥当前用量统计", "refresh": "刷新", "back": "返回", "availableMapPlatforms": "可用地图平台", "defaultMapPlatforms": "默认地图平台", "projectAllUsage": "项目总用量统计", "projectTokenAllUsage": "[{name}]项目API密钥总用量统计", "usageNoDetail": "当前项目暂无用量使用", "streetMap": "街道图", "satelliteMap": "影像图", "googleMap": "谷歌地图", "tiandituMap": "天地图", "refreshSuccess": "刷新成功", "refreshFailed": "刷新失败"}, "mapGL": {"lon": "经度", "lat": "纬度", "maxLon": "最大经度", "minLon": "最小经度", "maxLat": "最大纬度", "minLat": "最小纬度", "boxSelectionInMap": "在地图中框选范围", "mapLevel": "地图级别", "mapProvider": "地图提供商", "mapType": "地图类型", "cacheDateHint": "不填默认清除所有时间段的缓存", "clearCache": "清除地图缓存", "onlyBaseMap": "仅底图", "withAnnotation": "带标注", "osmOnlySupportRoadMap": "OSM仅支持街道图", "noCache": "暂无缓存", "clearCacheTime": "清除指定时间之前的缓存", "selectionOperationInfo": "在地图中拖动框选区域，点击确定按钮后，弹出清除地图缓存窗口继续操作。按下ESC键可取消当前操作。", "clearCacheSuccess": "清除缓存成功", "refreshMap": "刷新地图", "clickBtnToOften": "请勿频繁点击刷新按钮", "checkGcj02": "wgs84与gcj02转换", "currentMap": "当前地图", "currentCoordinateType": "当前坐标类型", "setLocate": "设置默认中心点", "jumpDefaultLocate": "跳转默认中心点", "setDefaultMapCenterInfo": "拖动地图选择地图默认中心点，点击确定按钮后，弹出设置默认中心点窗口继续操作。按下ESC键可取消当前操作。"}}