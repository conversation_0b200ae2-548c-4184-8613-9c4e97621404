import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { SessionStorage } from 'quasar'

// Common.Code: 0: setup, !0: not setup
const setupCode = ref<number>(1)

// 正在读取是否有setup标记
export const initSetupCodePending = ref<boolean>(true)

const setupCodeKey = 'setupCode'

export function setServerSetupCode(code: number) {
  setupCode.value = code
  SessionStorage.setItem(setupCodeKey, code)
}

export function getLocalSetupCode(): number | null {
  return SessionStorage.getItem<number>(setupCodeKey)
}

export const useServerSetupStore = defineStore('ServerSetup', () => {
  const alreadySetup = computed(() => setupCode.value === 0)
  const isInitSetupCodePending = computed(() => initSetupCodePending.value)

  // 返回需要暴露给组件的属性和方法
  return {
    alreadySetup,
    isInitSetupCodePending,
    setServerSetupCode,
  }
})
