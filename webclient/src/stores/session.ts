import { defineStore } from 'pinia'
import { reactive, toRaw } from 'vue'
import localforage from 'localforage'
import { type LoginReq } from '@/proto/bfmap.rpc_pb'
import type { MessageInitClean } from '@/utils/types'
import { useRefSessionStorage } from './common'

export const useLoginStatusStore = defineStore('LoginStatus', () => {
  // Session Id
  const { value: sessionId, setData: setSessionId, removeData: removeSessionId } = useRefSessionStorage<string>('sessionId', '')

  // 当前登录状态
  const { value: isLogin, setData: setLoginStatus, removeData: removeLoginStatus } = useRefSessionStorage<boolean>('isLogin', false)

  // 登录用户的组织RID
  const { value: userOrgRid, setData: setUserOrgRid, removeData: removeUserOrgRid } = useRefSessionStorage<string>('userOrgRid', '')

  // 登录用户的RID
  const { value: userRid, setData: setUserRid, removeData: removeUserRid } = useRefSessionStorage<string>('userRid', '')

  // 登录用户的名称
  const { value: userName, setData: setUserName, removeData: removeUserName } = useRefSessionStorage<string>('userName', '')

  // 系统版本
  const { value: version, setData: setVersion, removeData: removeVersion } = useRefSessionStorage<string>('version', '')

  // 登录登录的所有状态
  const clearLoginStatus = () => {
    removeLoginStatus()
    removeSessionId()
    removeUserOrgRid()
    removeUserRid()
    removeUserName()
    removeVersion()
  }

  return {
    sessionId,
    setSessionId,
    isLogin,
    setLoginStatus,
    userOrgRid,
    setUserOrgRid,
    userRid,
    setUserRid,
    userName,
    setUserName,
    version,
    setVersion,
    clearLoginStatus,
  }
})

export type RememberAccount = Partial<MessageInitClean<LoginReq>>

export const useRememberAccountStore = defineStore('rememberAccount', () => {
  const localStoreKey = 'rememberAccount'
  const account = reactive<RememberAccount>({
    Name: '',
    SessionId: '',
  })

  const setAccount = async (value: RememberAccount) => {
    Object.assign(account, value)
    // Store a plain object copy instead of the Protobuf object
    const plainObject = {
      Name: value.Name,
      SessionId: value.SessionId
    }
    await localforage.setItem<RememberAccount>(localStoreKey, plainObject)
  }

  const clearAccount = async () => {
    account.Name = ''
    account.SessionId = ''
    await localforage.removeItem(localStoreKey)
  }

  const clearSessionId = async () => {
    account.SessionId = ''
    const plainObject = {
      Name: account.Name,
      SessionId: account.SessionId
    }
    await localforage.setItem<RememberAccount>(localStoreKey, plainObject)
  }

  const initRememberAccount = async () => {
    const result = await localforage.getItem<RememberAccount | null>(localStoreKey)
    if (!result) {
      return
    }

    Object.assign(account, result)
  }

  initRememberAccount()

  return {
    account,
    setAccount,
    clearAccount,
    clearSessionId
  }
})

export type MapType = 'roadmap' | 'satellite' | 'hybrid'
export type MapProvider = 'google' | 'tianditu' | 'osm'

export type MapStatus = {
  center: [number, number]
  mapType: MapType
  mapProvider: MapProvider
  zoom: number
  maxZoom: number
  minZoom: number
}

export type MapCenterInfo = {
  center: [number, number]
  zoom: number
}

export const useMapStatusStore = defineStore('mapStatus', () => {
  const mapStatusStoreKey = 'MapStatus'
  const defaultMapCenterKey = 'defaultMapCenter'

  const mapStatus = reactive<MapStatus>({
    center: [118.62113, 25.00406],
    mapType: 'roadmap',
    mapProvider: 'google',
    maxZoom: 18,
    minZoom: 1,
    zoom: 12,
  })

  let defaultMapCenterStatus: MapCenterInfo = {
    center: [118.62113, 25.00406],
    zoom: 12
  }

  const setDefaultMapCenter = async (value: MapCenterInfo) => {
    defaultMapCenterStatus = value
    await localforage.setItem<MapCenterInfo>(defaultMapCenterKey, defaultMapCenterStatus)
  }

  const setMapStatus = async (value: MapStatus) => {
    Object.assign(mapStatus, value)
    await localforage.setItem<MapStatus>(mapStatusStoreKey, toRaw(mapStatus))
  }

  const initMapStatus = async () => {
    const result = await localforage.getItem<MapStatus | null>(mapStatusStoreKey)
    if (!result) {
      console.log('res not found')
      return
    }
    console.log('res', result)
    Object.assign(mapStatus, result)
  }

  const getDefaultMapCenterInfo = async () => {
    const result = await localforage.getItem<MapCenterInfo | null>(defaultMapCenterKey)
    if (!result) {
      console.log('res not found')
      return
    }
    console.log('res', result)
    Object.assign(defaultMapCenterStatus, result)
  }

  initMapStatus()

  getDefaultMapCenterInfo()

  return {
    mapStatus,
    setMapStatus,
    initMapStatus,
    defaultMapCenterStatus,
    setDefaultMapCenter,
    getDefaultMapCenterInfo
  }
})
