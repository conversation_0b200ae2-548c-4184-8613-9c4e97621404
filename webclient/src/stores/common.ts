import { ref, computed } from 'vue'
import { SessionStorage } from 'quasar'

// from quasar SessionStorage
export type WebStorageGetMethodReturnType =
  | Date
  | RegExp
  | number
  | boolean
  | string
  | object

// 定义一个方法，将响应式Ref的值与SessionStorage进行同步
export function useRefSessionStorage<T extends WebStorageGetMethodReturnType>(storeKey: string, initialValue: T) {
  const data = ref<T>(SessionStorage.getItem<T>(storeKey) ?? initialValue)
  const value = computed<T>(() => data.value)
  const setData = (value: T) => {
    data.value = value
    SessionStorage.setItem(storeKey, value)
  }
  const removeData = () => {
    data.value = initialValue
    SessionStorage.removeItem(storeKey)
  }

  return {
    data,
    value,
    setData,
    removeData,
  }
}

export const DbProjectRid = '11111111-1111-1111-1111-111111111111'

export const AdminUserRid = '11111111-1111-1111-1111-111111111111'
