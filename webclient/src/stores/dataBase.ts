import { defineStore } from 'pinia'
import { ref, computed, type Ref } from 'vue'
import type {
  <PERSON><PERSON><PERSON><PERSON>,
  Db<PERSON>ser,
  DbProject,
  DbUserPrivilege,
  DbProjectQuotas,
  DbProjectToken,
  DbMapProviderToken,
  DbMapProviderUsedQuotas,
  DbProjectTokenUsage,
  DbMapProviderTokenUsage,
} from '@/proto/db_pb'
import type { MessageInitClean } from '@/utils/types'

function useDatabaseRows<T extends MessageInitClean<T> & { Rid: string } = any>() {
  const _rows = ref<T[]>([]) as Ref<T[]>
  const rows = computed(() => _rows.value)
  const rowsMap = computed<Map<string, T>>(() => {
    const m = new Map<string, T>(
      _rows.value.map(row => {
        return [row.Rid, row]
      })
    )
    return m
  })

  const addRow = (row: T) => {
    _rows.value.push(row)
  }
  const addRows = (rows: T[]) => {
    _rows.value = [..._rows.value, ...rows]
  }

  const updateRow = (row: T) => {
    const index = _rows.value.findIndex(r => r.Rid === row.Rid)
    if (index === -1) {
      addRow(row)
      return
    }
    _rows.value[index] = row as any
  }

  const deleteRow = (row: T) => {
    _rows.value = _rows.value.filter(r => r.Rid !== row.Rid)
  }

  const clean = () => {
    _rows.value = []
  }

  return { rows, rowsMap, addRow, addRows, updateRow, deleteRow, clean }
}

export const useDbOrgStore = defineStore('DbOrg', () => {
  return useDatabaseRows<DbOrg>()
})

export const useDbUserStore = defineStore('DbUser', () => {
  return useDatabaseRows<DbUser>()
})

export const useDbProjectStore = defineStore('DbProject', () => {
  return useDatabaseRows<DbProject>()
})

// 项目配额
export const useDbProjectQuotasStore = defineStore('DbProjectQuotas', () => {
  return useDatabaseRows<DbProjectQuotas>()
})

export const useDbUserPrivilegeStore = defineStore('DbUserPrivilege', () => {
  return useDatabaseRows<DbUserPrivilege>()
})

export const useDbProjectTokenStore = defineStore('DbProjectToken', () => {
  return useDatabaseRows<DbProjectToken>()
})

export const useDbMapProviderTokenStore = defineStore('DbMapProviderToken', () => {
  return useDatabaseRows<DbMapProviderToken>()
})

export const useDbMapProviderUsedQuotasStore = defineStore('DbMapProviderUsedQuotas', () => {
  return useDatabaseRows<DbMapProviderUsedQuotas>()
})

// 项目api密钥用量记录表
export const useDbProjectTokenUsageStore = defineStore('DbProjectTokenUsage', () => {
  return useDatabaseRows<DbProjectTokenUsage>()
})

// 项目api密钥当前用量表
export const useDbProjectTokenCurrentUsageStore = defineStore('DbProjectTokenCurrentUsage', () => {
  return useDatabaseRows<DbProjectTokenUsage>()
})

// 地图api 总用量记录
export const useDbMapProviderTokenUsageStore = defineStore('DbMapProviderTokenUsage', () => {
  return useDatabaseRows<DbMapProviderTokenUsage>()
})

// 地图api 按照配额方式的记录用量
export const useDbMapProviderTokenUsageByQuotaType = defineStore('DbMapProviderTokenCurrentUsage', () => {
  return useDatabaseRows<DbMapProviderTokenUsage>()
})
