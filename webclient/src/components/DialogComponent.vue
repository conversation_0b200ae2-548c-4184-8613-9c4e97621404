<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card style="min-width: 400px" class="p-4">
      <!--header-->
      <q-toolbar v-if="showHeader" class="!px-4">
        <q-avatar v-if="!!props.icon">
          <q-icon :name="props.icon" size="sm"/>
        </q-avatar>
        <q-toolbar-title><span class="text-lg font-medium">{{title}}</span></q-toolbar-title>
        <q-btn v-if="showCloseBtn" flat round dense icon="close" size="sm" v-close-popup @click="onCancelClick"/>
      </q-toolbar>
      <!--body-->
      <slot name="default">
        <div class="p-4">
          <q-icon v-if="!!contentIcon" v-bind="contentIcon"></q-icon>
          <span :class="{'pl-2': !!contentIcon}">
            {{ defaultContent }}
          </span>
        </div>
      </slot>
      <!--footer button-->
      <q-card-actions v-if="!showCloseBtn" :class="['flex', 'justify-end', ...footerClass]">
        <slot name="action">
          <q-btn
              v-if="showCancelBtn"
              v-bind="cancelBtnProps"
              @click="onCancelClick">
          </q-btn>
          <q-btn
              v-if="showOkBtn"
              v-bind="okBtnProps"
              @click="onOKClick"
          >
          </q-btn>
        </slot>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
  import { type QIconProps, useDialogPluginComponent } from 'quasar'
  import type { QBtnProps } from 'quasar'
  import { computed, type Ref, type ComputedRef } from 'vue'

  interface Props {
    icon?: string,
    customClass?: string,
    title?: string,
    showHeader?: boolean,
    footerClass?: Array<string>,
    showCloseBtn?: boolean,
    // label | btn props | 是否显示
    ok: string | QBtnProps | boolean,
    cancel?: string | QBtnProps | boolean,
    defaultContent?: Ref<string> | ComputedRef<string> | string,
    contentIcon?: QIconProps | undefined
  }

  const props = withDefaults(
    defineProps<Props>(),
    {
      showHeader: true,
      showCloseBtn: false,
      footerClass: () => [] as string[],
      cancel: false,
    }
  )

  const showOkBtn = computed(() => {
    return typeof props.ok === 'undefined' ? true : (typeof props.ok === 'object' ? true  : props.ok)
  })

  const showCancelBtn = computed(() => {
    return typeof props.cancel === 'undefined' ? true : (typeof props.cancel === 'object' ? true  : props.cancel)
  })

  const okBtnProps = computed(() => {
    return typeof props.ok === 'object' ? props.ok : {}
  })

  const cancelBtnProps = computed(() => {
    return typeof props.cancel === 'object' ? props.cancel : {}
  })

  // expose component methods
  defineExpose({
    ...useDialogPluginComponent.emits,
  })

    // ...useDialogPluginComponent.emits,
  const emits = defineEmits([
    ...useDialogPluginComponent.emits,
      'okClick',
      'cancelClick'
  ])

  const { dialogRef, onDialogOK, onDialogCancel, onDialogHide } = useDialogPluginComponent()

  function onOKClick () {
    emits('okClick')
    onDialogOK()
  }

  function onCancelClick() {
    emits('cancelClick')
    onDialogCancel()
    onDialogHide()
  }

</script>
