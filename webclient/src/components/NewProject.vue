<template>
  <q-btn
    flat
    round
    dense
    icon="add_circle"
  >
    <q-tooltip :delay="500">
      {{ $t('myLayout.newAction') }}
    </q-tooltip>
    <q-menu
      anchor="bottom end"
      self="top end"
      auto-close
    >
      <q-item
        clickable
        @click.prevent="onNewProject"
      >
        <q-item-section
          avatar
          class="item-avatar"
        >
          <q-icon name="assignment" />
        </q-item-section>
        <q-item-section>{{ $t("myLayout.newProject") }}</q-item-section>
      </q-item>
      <q-item
        clickable
        @click.prevent="onNewUser"
      >
        <q-item-section
          avatar
          class="item-avatar"
        >
          <q-icon name="people" />
        </q-item-section>
        <q-item-section>{{ $t("myLayout.newUser") }}</q-item-section>
      </q-item>
      <q-item
        clickable
        @click.prevent="onNewApiKey"
      >
        <q-item-section
          avatar
          class="item-avatar"
        >
          <q-icon name="api" />
        </q-item-section>
        <q-item-section>{{ $t("myLayout.newApiKey") }}</q-item-section>
      </q-item>
      <q-item
        clickable
        @click.prevent="onNewMapApiKey"
      >
        <q-item-section
          avatar
          class="item-avatar"
        >
          <q-icon name="api" />
        </q-item-section>
        <q-item-section>{{ $t("myLayout.newMapApiKey") }}</q-item-section>
      </q-item>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router'
  import { StrPubSub } from 'ypubsub'
  import { NewApiKeyAction, NewMapApiKeyAction, NewProjectAction, NewUserDialog } from '@/utils/pubSubSubject.ts'

  const router = useRouter()

  const onNewProject = () => {
    router.replace({
      path: '/dbProject'
    }).then(() => {
      StrPubSub.publish(NewProjectAction)
    })
  }
  const onNewUser = () => {
    router.replace({
      path: '/dbUser'
    }).then(() => {
      StrPubSub.publish(NewUserDialog)
    })
  }
  const onNewApiKey = () => {
    router.replace({
      path: '/dbProject'
    }).then(() => {
      StrPubSub.publish(NewApiKeyAction)
    })
  }
  const onNewMapApiKey = () => {
    router.replace({
      path: '/dbMapProviderToken'
    }).then(() => {
      StrPubSub.publish(NewMapApiKeyAction)
    })
  }
</script>

<style lang="scss" scoped>
  .q-item__section--avatar.item-avatar {
    min-width: 40px;
  }
</style>
