<template>
  <q-popup-proxy>
    <q-banner
      dense
      class="max-w-xs"
    >
      <template v-slot:avatar>
        <q-icon
          name="warning"
          color="deep-orange"
        />
      </template>

      {{ message }}

      <template v-slot:action>
        <div class="w-full flex justify-center gap-2">
          <q-btn
            v-close-popup
            flat
            dense
            color="cyan"
            :label="$t('common.cancel')"
            class="px-3"
            @click="onCancel"
          />
          <q-btn
            v-close-popup
            dense
            color="primary"
            :label="$t('common.confirm')"
            class="!px-3"
            @click="onConfirm"
          />
        </div>
      </template>
    </q-banner>
  </q-popup-proxy>
</template>

<script setup lang="ts">
  interface PopupProxyConfirmProps {
    message: string
  }
  withDefaults(defineProps<PopupProxyConfirmProps>(), {})

  interface PopupProxyConfirmEmits {
    // eslint-disable-next-line no-unused-vars
    (evt: 'cancel'): void
    // eslint-disable-next-line no-unused-vars
    (evt: 'confirm'): void
  }
  const emit = defineEmits<PopupProxyConfirmEmits>()

  const onCancel = () => {
    emit('cancel')
  }

  const onConfirm = () => {
    emit('confirm')
  }
</script>

<style scoped lang="scss"></style>
