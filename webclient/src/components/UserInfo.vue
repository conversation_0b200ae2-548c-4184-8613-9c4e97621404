<template>
  <q-btn
    flat
    round
    dense
    icon="person"
  >
    <q-menu
      anchor="bottom end"
      self="top end"
    >
      <q-card
        flat
        class="max-w-64"
      >
        <q-card-section class="text-center">
          <q-item-label class="text-lg line-clamp-2">
            {{ userData?.Nickname ?? userName }}
          </q-item-label>
          <q-item-label class="text-md !text-gray-500 line-clamp-2">
            {{ userName }}
          </q-item-label>
        </q-card-section>
      </q-card>

      <q-separator />

      <q-list separator>
        <q-item
          clickable
          v-close-popup
          @click="onLogout"
        >
          <q-item-section avatar>
            <q-icon
              color="negative"
              name="logout"
            />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ $t('myLayout.logOut') }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { useLoginStatusStore, useRememberAccountStore } from '@/stores/session.ts'
  import { useDbUserStore } from '@/stores/dataBase.ts'
  import { storeToRefs } from 'pinia'
  import { logout } from '@/services/connectRpc.ts'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const loginStatusStore = useLoginStatusStore()
  const { userName, userRid } = storeToRefs(loginStatusStore)
  const { clearLoginStatus } = loginStatusStore
  const dbUserStore = useDbUserStore()
  const { rowsMap } = storeToRefs(dbUserStore)
  const rememberAccountStore = useRememberAccountStore()
  const { clearSessionId } = rememberAccountStore

  const userData = computed(() => {
    return rowsMap.value.get(userRid.value)
  })

  const onLogout = async () => {
    const isOk = await logout()
    if (!isOk) {
      // return
    }

    // 清除登录状态
    clearLoginStatus()
    await clearSessionId()

    router.replace({
      path: '/login'
    })
  }
</script>

<style lang="scss" scoped></style>
