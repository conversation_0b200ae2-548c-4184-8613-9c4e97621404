<template>
  <div :class="['!truncate', classes ]" ref="tdContainer"  :style="{ maxWidth: `${maxWidth}px` }">
    <slot name="defaultContent">
      {{value}}
    </slot>
    <q-tooltip v-if="isOverflow" :delay="500" :max-width="'50vw'">
      <slot name="tooltipContent">
        {{ value }}
      </slot>
    </q-tooltip>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, onUnmounted, ref } from 'vue'

  interface Props {
    value?: string,
    classes?: string,
    maxWidth?: number,
  }

  withDefaults(defineProps<Props>(), {
    maxWidth: 500
  })
  const tdContainer = ref<HTMLDivElement>()
  const isOverflow = ref<boolean>(false)

  let observer: ResizeObserver
  onMounted(() => {
    if (tdContainer.value) {
      const el = tdContainer.value
      const updateOverflow = () => {
        isOverflow.value = el.scrollWidth > el.clientWidth
      }

      observer = new ResizeObserver(updateOverflow)
      observer.observe(el)

      // 初始触发一次
      updateOverflow()
    }
  })

  onUnmounted(() => {
    if (observer && tdContainer.value) {
      observer.unobserve(tdContainer.value)
    }
  })
</script>

<style scoped lang="scss">

</style>
