<template>
  <q-table
      ref="tableRef"
      binary-state-sort
      bordered
      flat
      dense
      virtual-scroll
      row-key="Rid"
      selection="multiple"
      :rows="finalRows"
      :columns="columns"
      :filter="filter"
      v-model:selected="selected"
      v-model:pagination="pagination"
      :rows-per-page-options="[0]"
      @selection="handleSelection"
      class="!border-x-0 !border-t-0 !rounded-none !h-full sticky-header-table api-table"
  >
    <template v-slot:top>
      <q-btn
          size="sm"
          class="!ml-2"
          color="negative"
          :label="$t('form.batchDelete')"
          :disable="selected.length <= 0"
          @click="batchDeleteRows(selected)"
      />
      <q-space/>
      <q-input
          dense
          debounce="300"
          v-model="filter"
          :placeholder="$t('form.search')"
      >
        <template v-slot:append>
          <q-icon name="search"/>
        </template>
      </q-input>
    </template>

    <template v-slot:header-selection="scope">
      <q-checkbox v-model="scope.selected" dense>
        <q-tooltip
            anchor="top middle"
            :offset="[10, 20]"
        >
          {{ $t('dbUser.selectTooltip') }}
        </q-tooltip>
      </q-checkbox>
    </template>

    <template v-slot:body-selection="scope">
      <q-checkbox
          dense
          :model-value="scope.selected"
          @update:model-value="(val, evt) => {
            // @ts-ignore
            Object.getOwnPropertyDescriptor(scope, 'selected').set(val, evt)
          }"
      />
    </template>

    <template v-slot:body-cell-Status="props">
      <q-td auto-width>
        <template v-if="props.row.Status === 1">
          <q-chip
              dense
              clickable
              color="teal"
              text-color="white"
              icon="star"
          >
            {{ $t('dbProjectToken.enable') }}
            <popup-proxy-confirm
                :message="$t('dbProjectToken.wantToDisabledToken')"
                @confirm="toggleDbProjectTokenStatus(props.row, 4)"
            />
          </q-chip>
        </template>
        <template v-else>
          <q-chip
              dense
              clickable
              color="red"
              text-color="white"
              icon="star_border"
          >
            {{ $t('dbProjectToken.disable') }}
            <popup-proxy-confirm
                :message="$t('dbProjectToken.wantToEnableToken')"
                @confirm="toggleDbProjectTokenStatus(props.row, 1)"
            />
          </q-chip>
        </template>
      </q-td>
    </template>

    <template v-slot:body-cell-Token="scope">
      <q-td align="center">
        <span class="mr-2">{{ `...${scope.row.Token.slice(-8)}` }}</span>
        <q-btn flat dense size="xs" icon="content_copy" @click="copyTextToClipboard(scope.row.Token)">
          <q-tooltip>
            {{ $t('dbMapProviderToken.clickToCopyApiKey') }}
          </q-tooltip>
        </q-btn>
      </q-td>
    </template>

    <template v-slot:body-cell-SysName="scope">
      <q-td auto-width align="center">
        {{ scope.row.SysName?.Names[0] ?? '' }}
        <q-chip dense size="md" color="light-blue-7" text-color="white" clickable v-if="scope.row.SysName?.Names.length > 1">
          {{ `+${scope.row.SysName?.Names.length - 1}` }}
          <q-tooltip>
            {{ t('dbMapProviderToken.clickToLookSysName') }}
          </q-tooltip>
          <q-popup-proxy>
            <q-banner :class="{ 'max-w-[32vw]': !isMobile }">
              <q-list :class="{ 'flex gap-1': !isMobile }" dense>
                <q-chip
                    v-for="item in scope.row.SysName?.Names"
                    :key="item"
                    outline
                    dense
                    clickable
                    color="light-blue-7"
                    text-color="white"
                    @click="copyTextToClipboard(item)"
                >
                  {{ item }}
                  <q-tooltip>
                    {{ $t('dbMapProviderToken.clickToCopySysName') }}
                  </q-tooltip>
                </q-chip>
              </q-list>
            </q-banner>
          </q-popup-proxy>
        </q-chip>
      </q-td>
    </template>

    <template v-slot:body-cell-AvailableMapProvider="props">
      <q-td auto-width align="center">
        <OverflowTd :value="getAvailableMapProviderString(props.row.AvailableMapProvider)"></OverflowTd>
      </q-td>
    </template>

    <template v-slot:body-cell-Note="props">
      <q-td auto-width align="center">
        <OverflowTd :value="props.row.Note"></OverflowTd>
      </q-td>
    </template>

    <template v-slot:body-cell-ExpireTime="props">
      <q-td auto-width align="center">
        <span
            :class="{'text-warning': willExpire(props.row.ExpireTime), 'text-negative': hasExpired(props.row.ExpireTime) }">{{
            getExpireTime(props.row.ExpireTime)
          }}</span>
      </q-td>
    </template>

    <template v-slot:body-cell-operate="props">
      <q-td auto-width>
        <q-btn
            round
            outline
            icon="edit"
            size="xs"
            color="primary"
            @click="openProjectTokenDlg(props.row)"
        >
          <q-tooltip>
            {{ $t('form.edit') }}
          </q-tooltip>
        </q-btn>
        <q-btn
            round
            outline
            icon="delete"
            size="xs"
            color="negative"
            class="!ml-2"
        >
          <q-tooltip>
            {{ $t('form.delete') }}
          </q-tooltip>
          <popup-proxy-confirm
              :message="$t('dbProjectToken.wantToDeleteToken')"
              @confirm="deleteDbProjectToken(props.row)"
          />
        </q-btn>
      </q-td>
    </template>

    <template v-slot:no-data>
      <div class="full-width flex justify-center">
        <q-icon
            name="warning"
            size="xs"
        ></q-icon>
        <span>{{ $t('dbUser.noData') }}</span>
      </div>
    </template>
  </q-table>
</template>

<script setup lang="ts">
  import { useDbProjectTokenStore } from '@/stores/dataBase.ts'
  import { useI18n } from 'vue-i18n'
  import { type QTableColumn, useQuasar, QTable } from 'quasar'
  import { ref, defineAsyncComponent, computed } from 'vue'
  import { TAvailableMapProviderSchema, MapProviderEnum, type DbProject } from '@/proto/db_pb.ts'
  import type { DbProjectToken, TAvailableMapProvider } from '@/proto/db_pb.ts'
  import { create, fromJsonString } from '@bufbuild/protobuf'
  import { formatDayjs, dayjs } from '@/utils/dayjs.ts'
  import { storeToRefs } from 'pinia'
  import { setProjectToken } from '@/services/connectRpc'
  import { errorMessage, successMessage } from '@/utils/notify'
  import cloneDeep from 'lodash/cloneDeep'
  import { OpenDialogAction } from '@/utils/types'
  import { SetProjectTokenReqSchema } from '@/proto/bfmap.rpc_pb'
  import OverflowTd from '@/components/OverflowTd.vue'
  import { copyTextToClipboard } from '@/utils/crypto.ts'
  import { confirmAgainBatchDelete, useTableHandleSelection } from '@/utils/common.ts'

  const PopupProxyConfirm = defineAsyncComponent(() => import('@/components/PopupProxyConfirm.vue'))

  const emit = defineEmits<{
    // eslint-disable-next-line no-unused-vars
    (e: 'openProjectTokenDlg', action: OpenDialogAction, row?: DbProjectToken): void
  }>()

  const openProjectTokenDlg = (row: DbProjectToken) => {
    emit('openProjectTokenDlg', OpenDialogAction.Edit, row)
  }

  interface Props {
    project: DbProject
  }

  const props = withDefaults(defineProps<Props>(), {})

  const { t } = useI18n()
  const $q = useQuasar()
  const dbProjectTokenStore = useDbProjectTokenStore()
  const { rows } = storeToRefs(dbProjectTokenStore)
  const { updateRow: updateDbProjectTokenStoreRow, deleteRow: deleteDbProjectTokenStoreRow } = dbProjectTokenStore

  const isMobile = computed(() => {
    return $q.screen.lt.sm
  })

  const finalRows = computed(() => {
    return rows.value.filter(row => row.Status !== 8 && props.project.Rid == row.ProjectRid)
  })
  const { tableRef, selected, handleSelection } = useTableHandleSelection<DbProjectToken>()

  const availableMapProviderOption = computed(() => {
    return [
      {
        label: t('dbProjectToken.googleMap'),
        value: 'Google',
        provider: MapProviderEnum.ProviderGoogle,
      },
      {
        label: t('dbProjectToken.tianditu'),
        value: 'Tianditu',
        provider: MapProviderEnum.ProviderTianditu,
      },
      {
        label: t('dbProjectToken.openStreetMap'),
        value: 'OSM',
        provider: MapProviderEnum.ProviderOSM,
      },
    ]
  })
  const filter = ref('')
  const pagination = ref({
    sortBy: 'CreateTime',
    descending: false,
    page: 1,
    rowsPerPage: 10
  })

  const columns = computed<QTableColumn[]>(() => {
    return [
      {
        name: 'Name',
        label: t('dbMapProviderToken.apiName'),
        align: 'center',
        field: 'Name',
      },
      {
        name: 'Token',
        label: t('dbProjectToken.apiKey'),
        align: 'center',
        field: 'Token',
        style: 'width: 280px',
      },
      {
        name: 'SysName',
        label: t('dbMapProviderToken.sysName'),
        align: 'center',
        field: 'SysName',
      },
      {
        name: 'Status',
        label: t('dbProjectToken.status'),
        align: 'center',
        field: 'Status',
      },
      {
        name: 'CreateTime',
        label: t('dbUser.createTime'),
        align: 'center',
        field: 'CreateTime',
        sortable: true,
        format: (value: bigint) => {
          return formatDayjs(Number(value) * 1000)
        },
      },
      {
        name: 'ExpireTime',
        label: t('dbProjectToken.expiryDate'),
        align: 'center',
        field: 'ExpireTime',
        sortable: true,
      },
      {
        name: 'AvailableMapProvider',
        label: t('dbProjectToken.availableMaps'),
        align: 'center',
        field: 'AvailableMapProvider',
      },
      {
        name: 'DefaultRoadmap',
        label: t('dbProjectToken.defaultRoadmap'),
        align: 'center',
        field: '',
        format: (_, row) => {
          const mapProvider: TAvailableMapProvider = fromJsonString(TAvailableMapProviderSchema, row.AvailableMapProvider, {
            ignoreUnknownFields: true
          })
          return availableMapProviderOption.value.find(item => item.provider === mapProvider.DefaultRoadmap)?.label ?? mapProvider.DefaultRoadmap + ''
        }
      },
      {
        name: 'DefaultSatellite',
        label: t('dbProjectToken.defaultSatellite'),
        align: 'center',
        field: '',
        format: (_, row) => {
          const mapProvider: TAvailableMapProvider = fromJsonString(TAvailableMapProviderSchema, row.AvailableMapProvider, {
            ignoreUnknownFields: true
          })
          if (!mapProvider.Google && !mapProvider.Tianditu) {
            return ''
          }
          return availableMapProviderOption.value.find(item => item.provider === mapProvider.DefaultSatellite)?.label ?? mapProvider.DefaultSatellite + ''
        }
      },
      {
        name: 'DefaultHybrid',
        label: t('dbProjectToken.defaultHybrid'),
        align: 'center',
        field: '',
        format: (_, row) => {
          const mapProvider: TAvailableMapProvider = fromJsonString(TAvailableMapProviderSchema, row.AvailableMapProvider, {
            ignoreUnknownFields: true
          })
          if (!mapProvider.Google && !mapProvider.Tianditu) {
            return ''
          }
          return availableMapProviderOption.value.find(item => item.provider === mapProvider.DefaultHybrid)?.label ?? mapProvider.DefaultHybrid + ''
        }
      },
      {
        name: 'Note',
        label: t('dbUser.note'),
        align: 'center',
        field: 'Note',
      },
      {
        name: 'operate',
        label: t('dbUser.operate'),
        align: 'center',
        field: ''
      },
    ]
  })

  const getAvailableMapProviderString = (value: string) => {
    const mapProvider: TAvailableMapProvider = fromJsonString(TAvailableMapProviderSchema, value, {
      ignoreUnknownFields: true
    })
    const providers: string[] = Object.keys(mapProvider).filter((key) => mapProvider[key as keyof TAvailableMapProvider] === true)
        .map(key => availableMapProviderOption.value.find(item => item.value === key)?.label ?? key)

    return providers.join(', ')
  }

  const getExpireTime = (val: bigint) => {
    return val === 0n ? t('dbProjectToken.alwaysValid') : formatDayjs(Number(val) * 1000, 'YYYY-MM-DD HH:mm')
  }

  const SEVEN_DAYS_IN_MS = 7 * 24 * 60 * 60 * 1000
  const willExpire = (val: bigint) => {
    if (val === BigInt(0)) {
      return false
    }
    const valTime = dayjs(Number(val) * 1000)
    const currentTime = dayjs()
    const diffInMs = valTime.diff(currentTime)
    return diffInMs <= SEVEN_DAYS_IN_MS && diffInMs > 0
  }

  const hasExpired = (val: bigint) => {
    if (val === BigInt(0)) {
      return false
    }
    const valTime = dayjs(Number(val) * 1000)
    const currentTime = dayjs()
    const diffInMs = valTime.diff(currentTime)
    return diffInMs < 0
  }

  async function batchDeleteRows(rows: DbProjectToken[]) {
    const isConfirm = await confirmAgainBatchDelete()
    if (!isConfirm) return

    const dialog = $q.dialog({
      message: t('common.operationProgress', { progress: 0 }),
      progress: true,
      persistent: true,
      ok: false,
      class: 'flex flex-col items-center justify-center'
    })

    try {
      const total = rows.length
      let successCount = 0
      let failCount = 0
      const startTime = Date.now()

      // 创建删除任务数组，每个任务都包含删除操作和更新进度
      const tasks = rows.map((row, index) => {
        const req = create(SetProjectTokenReqSchema, {
          Code: 4,
          ProjectToken: row,
        })
        return setProjectToken(req)
            .then(res => {
              if (res.Code === 0) {
                successCount++
                deleteDbProjectTokenStoreRow(row)
              } else {
                failCount++
              }
              // 更新进度对话框
              const progress = Math.round(((index + 1) / total) * 100)
              dialog.update({
                message: t('common.operationProgress', { progress })
              })
              return res
            })
      })

      // 等待所有删除任务完成
      await Promise.all(tasks)

      // 关闭进度对话框，如果删除时间小600毫秒，则等待600毫秒，避免进度提示闪烁
      if (Date.now() - startTime < 800) {
        await new Promise(resolve => setTimeout(resolve, 600 - (Date.now() - startTime)))
      }
      dialog.hide()

      // 清空选中项
      selected.value = []

      // 显示操作结果
      if (failCount === 0) {
        successMessage(t('common.operationSuccess'))
      } else {
        errorMessage(t('common.operationPartialSuccess', {
          success: successCount,
          fail: failCount
        }))
      }
    } catch (error) {
      dialog.hide()
      errorMessage(t('common.operationFailed') + `: ${error}`)
    }
  }

  // 删除一行数据
  const deleteDbProjectToken = async (row: DbProjectToken): Promise<boolean> => {
    const req = create(SetProjectTokenReqSchema, {
      Code: 4,
      ProjectToken: row,
    })
    const res = await setProjectToken(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      deleteDbProjectTokenStoreRow(row)
      return true
    }

    errorMessage(t('common.operationFailed') + ` ${res.Reason}`)
    return false
  }

  // 切换api启用状态
  const toggleDbProjectTokenStatus = async (row: DbProjectToken, status: number) => {
    const newRow = cloneDeep(row)
    newRow.Status = status
    const req = create(SetProjectTokenReqSchema, {
      Code: 2,
      ProjectToken: newRow,
      UpdateFields: ['Status'],
    })
    const res = await setProjectToken(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      updateDbProjectTokenStoreRow(newRow)
      return
    }

    errorMessage(t('common.operationFailed') + ` ${res.Reason}`)
  }


</script>

<style lang="scss">
  @import "@/css/data_table.scss";
</style>
