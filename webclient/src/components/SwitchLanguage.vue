<template>
  <q-btn
    flat
    round
    dense
    icon="translate"
    size="11px"
    padding="sm"
  >
    <q-menu
      anchor="bottom end"
      self="top end"
      auto-close
    >
      <q-item
        clickable
        :active="locale === appLang.zh"
        @click.prevent="switchLanguage(appLang.zh)"
      >
        <q-item-section
          avatar
          class="language-avatar"
          :class="{ 'invisible': locale !== appLang.zh }"
        >
          <q-icon name="check_circle" />
        </q-item-section>
        <q-item-section>{{ $t("language.zh") }}</q-item-section>
      </q-item>
      <q-item
        clickable
        :active="locale === appLang.en"
        @click.prevent="switchLanguage(appLang.en)"
      >
        <q-item-section
          avatar
          class="language-avatar"
          :class="{ 'invisible': locale !== appLang.en }"
        >
          <q-icon name="check_circle" />
        </q-item-section>
        <q-item-section>{{ $t("language.en") }}</q-item-section>
      </q-item>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
  import { switchLanguage, appLang } from '@/i18n/i18n'
  import { useI18n } from 'vue-i18n'

  const { locale } = useI18n()
</script>

<style lang="scss" scoped>
  .q-item__section--avatar.language-avatar {
    min-width: 40px;
  }
</style>
