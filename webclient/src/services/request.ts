import {
  type DbMap<PERSON>roviderToken,
  DbMapProviderTokenSchema,
  type DbMapProviderTokenUsage,
  DbMapProviderTokenUsageSchema,
  type DbMapProviderUsedQuotas,
  DbMapProviderUsedQuotasSchema,
  type DbOrg,
  DbOrgSchema,
  type DbProject,
  type DbProjectQuotas,
  DbProjectQuotasSchema,
  DbProjectSchema,
  type DbProjectToken,
  DbProjectTokenSchema,
  type DbProjectTokenUsage,
  DbProjectTokenUsageSchema,
  type DbUser,
  type DbUserPrivilege,
  DbUserPrivilegeSchema,
  DbUserSchema,
} from '@/proto/db_pb'
import { getCurrentUsageReq, tableQuery } from './connectRpc'
import logger from '@/utils/logger'
import {
  useDbMapProviderTokenStore,
  useDbMapProviderTokenUsageStore,
  useDbMapProviderUsedQuotasStore,
  useDbOrgStore,
  useDbProjectQuotasStore,
  useDbProjectStore,
  useDbProjectTokenCurrentUsageStore,
  useDbProjectTokenStore,
  useDbProjectTokenUsageStore,
  useDbUserPrivilegeStore,
  useDbUserStore,
  useDbMapProviderTokenUsageByQuotaType,
} from '@/stores/dataBase'
import { WhereOperator } from '@/proto/protodb_pb.ts'
import dayjs, { type UnitType } from 'dayjs'
import { create } from '@bufbuild/protobuf'
import { GetCurrentUsageReqSchema } from '@/proto/bfmap.rpc_pb.ts'
// import { create } from '@bufbuild/protobuf';
// import { GetCurrentUsageReqSchema } from '@/proto/bfmap.rpc_pb.ts';

export async function requestDbOrg(where?: { [key: string]: string }): Promise<Array<DbOrg>> {
  const rows: DbOrg[] = await tableQuery<DbOrg>(DbOrgSchema, where).catch(err => {
    logger.error('requestDbOrg catch error:', err)
    return []
  })

  return rows
}

export async function requestDbUser(where?: { [key: string]: string }): Promise<Array<DbUser>> {
  const rows: DbUser[] = await tableQuery<DbUser>(DbUserSchema, where).catch(err => {
    logger.error('requestDbUser catch error:', err)
    return []
  })

  return rows
}

export async function requestDbProject(where?: { [key: string]: string }): Promise<Array<DbProject>> {
  const rows: DbProject[] = await tableQuery<DbProject>(DbProjectSchema, where).catch(err => {
    logger.error('requestDbProject catch error:', err)
    return []
  })

  return rows
}

// 请求项目配额
export async function requestDbProjectQuotas(where?: { [key: string]: string }): Promise<Array<DbProjectQuotas>> {
  const rows: DbProjectQuotas[] = await tableQuery<DbProjectQuotas>(DbProjectQuotasSchema, where).catch(err => {
    logger.error('requestDbProjectQuotas catch error:', err)
    return []
  })

  return rows
}

export async function requestDbUserPrivilege(where?: { [key: string]: string }): Promise<Array<DbUserPrivilege>> {
  const rows: DbUserPrivilege[] = await tableQuery<DbUserPrivilege>(DbUserPrivilegeSchema, where).catch(err => {
    logger.error('requestDbUserPrivilege catch error:', err)
    return []
  })

  return rows
}

export async function requestDbProjectToken(where?: { [key: string]: string }): Promise<Array<DbProjectToken>> {
  const rows: DbProjectToken[] = await tableQuery<DbProjectToken>(DbProjectTokenSchema, where).catch(err => {
    logger.error('requestDbUserPrivilege catch error:', err)
    return []
  })

  return rows
}

export async function requestDbMapProviderToken(where?: { [key: string]: string }): Promise<Array<DbMapProviderToken>> {
  const rows: DbMapProviderToken[] = await tableQuery<DbMapProviderToken>(DbMapProviderTokenSchema, where).catch(
    err => {
      logger.error('requestDbMapProviderToken catch error:', err)
      return []
    }
  )

  return rows
}

export async function requestDbMapProviderUsedQuotasToken(where?: {
  [key: string]: string
}): Promise<Array<DbMapProviderUsedQuotas>> {
  const rows: DbMapProviderUsedQuotas[] = await tableQuery<DbMapProviderUsedQuotas>(
    DbMapProviderUsedQuotasSchema,
    where
  ).catch(err => {
    logger.error('requestDbMapProviderUsedQuotasToken catch error:', err)
    return []
  })

  return rows
}

// 请求地图API token 用量记录
export async function requestDbMapProviderTokenUsage(
  where2?: { [key: string]: string },
  where2Operator?: { [key: string]: WhereOperator },
  where?: { [key: string]: string }
): Promise<Array<DbMapProviderTokenUsage>> {
  const rows: DbMapProviderTokenUsage[] = await tableQuery<DbMapProviderTokenUsage>(
    DbMapProviderTokenUsageSchema,
    where,
    where2Operator,
    where2
  ).catch(err => {
    logger.error('requestDbMapProviderTokenUsage catch error:', err)
    return []
  })

  return rows
}

export async function requestDbProjectTokenUsage(
  where2?: { [key: string]: string },
  where2Operator?: { [key: string]: WhereOperator },
  where?: { [key: string]: string }
): Promise<Array<DbProjectTokenUsage>> {
  const rows: DbProjectTokenUsage[] = await tableQuery<DbProjectTokenUsage>(
    DbProjectTokenUsageSchema,
    where,
    where2Operator,
    where2
  ).catch(err => {
    logger.error('requestDbProjectTokenUsage catch error:', err)
    return []
  })

  return rows
}

export function getQuotaTimeRange(quotasType: number): { startTime: bigint; endTime: bigint } {
  const now = dayjs() // Get the current time
  let unit: UnitType

  switch (quotasType) {
    case 1: // per minute
      unit = 'minute'
      break
    case 2: // per hour
      unit = 'hour'
      break
    case 3: // per day
      unit = 'day'
      break
    case 4: // per month
      unit = 'month'
      break
    default:
      throw new Error(`Invalid QuotasType provided: ${quotasType}`)
  }
  const startTimeMillis = now.startOf(unit).valueOf()
  const startTimeSeconds = Math.floor(startTimeMillis / 1000)

  const endTimeMillis = now.endOf(unit).valueOf()
  const endTimeSeconds = Math.floor(endTimeMillis / 1000)

  const startTime = BigInt(startTimeSeconds)
  const endTime = BigInt(endTimeSeconds)

  return { startTime, endTime }
}

/**
 * 请求系统初始化所需的基础数据
 */
export async function initSystemBaseData(): Promise<void> {
  // 请求用户数据
  requestDbUser().then(rows => {
    const { clean, addRows } = useDbUserStore()
    logger.debug('requestDbUser:', rows)
    clean()
    addRows(rows)
  })

  // 请求用户权限
  requestDbUserPrivilege().then(rows => {
    const { clean, addRows } = useDbUserPrivilegeStore()
    logger.debug('requestDbUserPrivilege:', rows)
    clean()
    addRows(rows)
  })

  // 请求组织数据
  requestDbOrg().then(rows => {
    const { clean, addRows } = useDbOrgStore()
    logger.debug('requestDbOrg:', rows)
    clean()
    addRows(rows)
  })

  // 请求项目数据
  requestDbProject().then(rows => {
    const { clean, addRows } = useDbProjectStore()
    logger.debug('requestDbProject:', rows)
    clean()
    addRows(rows)
  })

  // 请求项目配额用量数据
  requestDbProjectQuotas().then(rows => {
    const { clean, addRows } = useDbProjectQuotasStore()
    logger.debug('requestDbProjectQuotas:', rows)
    clean()
    addRows(rows)

    //  请求项目API按照配额类型的用量记录表 如token A 在当月使用的总用量
    for (let i = 0; i < rows.length; i++) {
      const quota = rows[i]
      const { startTime, endTime } = getQuotaTimeRange(quota.QuotasType)
      requestDbProjectTokenUsage(
        {
          ProjectRid: quota.Rid,
          StartTime: startTime.toString(),
          EndTime: endTime.toString(),
        },
        {
          ProjectRid: WhereOperator.WOP_EQ,
          StartTime: WhereOperator.WOP_GTE,
          EndTime: WhereOperator.WOP_LT,
        }
      ).then(result => {
        const { addRows } = useDbProjectTokenCurrentUsageStore()
        logger.debug('requestDbProjectTokenCurrentUsageStore:', result)
        addRows(result)
      })
    }
  })

  // 请求项目API数据
  requestDbProjectToken().then(rows => {
    const { clean, addRows } = useDbProjectTokenStore()
    logger.debug('requestDbProjectToken:', rows)
    clean()
    addRows(rows)
  })

  // 请求地图服务API数据
  const mapProviderTokenRows = await requestDbMapProviderToken().then(rows => {
    const { clean, addRows } = useDbMapProviderTokenStore()
    logger.debug('requestDbMapProviderToken:', rows)
    clean()
    addRows(rows)
    return rows
  })

  // 请求地图服务API配额用量数据
  // 这里只是请求了数据库的地图api当前用量
  requestDbMapProviderUsedQuotasToken().then(rows => {
    const { clean, addRow } = useDbMapProviderUsedQuotasStore()
    logger.debug('requestDbMapProviderUsedQuotasToken:', rows)
    clean()

    mapProviderTokenRows.forEach(async row => {
      // 实际的当前用量需要从服务器缓存拿
      const currentUsageReq = create(GetCurrentUsageReqSchema, {
        Code: 1,
        UserRid: row.UserRid,
        Provider: row.Provider,
        ProviderToken: row.Token,
      })

      const res = await getCurrentUsageReq(currentUsageReq)
      if (res.Code == 0) {
        const mapProviderTokenUsage = rows.find(r => r.Rid === row.Rid)!
        mapProviderTokenUsage.UsedQuotas = res.CurrentUsage
        addRow(mapProviderTokenUsage)
      }

      // 还需要请求地图api按照配额方式的用量记录 如provider token a 在当月使用的总用量
      const { startTime, endTime } = getQuotaTimeRange(row.QuotasType)
      requestDbMapProviderTokenUsage(
        {
          ProviderRid: row.Rid,
          StartTime: startTime.toString(),
          RotateTime: endTime.toString(),
        },
        {
          ProviderRid: WhereOperator.WOP_EQ,
          StartTime: WhereOperator.WOP_GTE,
          RotateTime: WhereOperator.WOP_LT,
        }
      ).then(result => {
        const { addRows } = useDbMapProviderTokenUsageByQuotaType()
        logger.debug('requestDbProjectTokenCurrentUsageStore:', result)
        addRows(result)
      })
    })
  })

  // 地图api总用量历史记录
  requestDbMapProviderTokenUsage().then(rows => {
    const { clean, addRows } = useDbMapProviderTokenUsageStore()
    logger.debug('requestDbMapProviderTokenUsage:', rows)
    clean()
    addRows(rows)
  })

  // 请求项目API总用量记录表
  requestDbProjectTokenUsage().then(rows => {
    const { clean, addRows } = useDbProjectTokenUsageStore()
    logger.debug('requestDbProjectTokenUsage:', rows)
    clean()
    addRows(rows)
  })
}
