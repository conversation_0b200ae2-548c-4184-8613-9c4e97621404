import { storeToRefs } from 'pinia'
import type { Interceptor } from '@connectrpc/connect'
import { ConnectError, createClient } from '@connectrpc/connect'
import { createConnectTransport } from '@connectrpc/connect-web'
import type {
  Common,
  CreateUserReq,
  DeleteMapCacheIndexesReq,
  GetCurrentUsageReq,
  GetCurrentUsageResp,
  LoginReq,
  LoginResp,
  SetProjectReq,
  SetProjectTokenReq,
  SetProviderTokenReq,
  SetupReq,
  UpdateDbUserReq
} from '@/proto/bfmap.rpc_pb'
import {
  bfmap,
  CommonRespCode,
  CommonSchema,
  GetCurrentUsageRespSchema,
  LoginMethod,
  LoginReqSchema,
  LoginRespCode,
  LoginRespSchema,
} from '@/proto/bfmap.rpc_pb'
import { rpcUrl } from '@/utils/serverConfig'
import { useLoginStatusStore, useRememberAccountStore } from '@/stores/session'
import { logger } from '@/utils/logger'
import type { CrudResp } from '@/proto/protodb_pb'
import {
  CrudReqCode,
  CrudReqSchema,
  CrudRespSchema,
  CrudResultType,
  ProtoDbSrv,
  QueryReqSchema,
  TableQueryReqSchema,
  WhereOperator
} from '@/proto/protodb_pb'
import type { Message } from '@bufbuild/protobuf'
import { create, fromBinary, toBinary } from '@bufbuild/protobuf'
import type { GenMessage } from '@bufbuild/protobuf/codegenv1'
import { getLocalSetupCode, initSetupCodePending, setServerSetupCode } from '@/stores/serverSetup'
import { utcTime } from '@/utils/dayjs.ts'
import { cacheAsyncIterable, type Deferred, deferred } from '@/utils/common.ts'
import { Dialog } from 'quasar'
import { i18n } from '@/i18n/i18n.ts'
import router from '@/router.ts'

let rpcCallUid = 0
let autoLoginDeffer: Deferred<LoginResp> | null = null

// SessionID失效后，自动以SessionID登录，多个请求可能会触发，全局应该只发一次
async function autoSessionIdLogin(ssid: string): Promise<LoginResp> {
  // 标记正在登录，所有请求都不需要登录
  if (autoLoginDeffer) {
    return autoLoginDeffer
  }

  autoLoginDeffer = deferred<LoginResp>()
  const loginStatusStore = useLoginStatusStore()
  const loginReq = create(LoginReqSchema, {
    LoginMethod: LoginMethod.SessionId,
    Name: loginStatusStore.userName,
    // 编码后的数据库密码
    PasswordHash: '*',
    TimeStr: utcTime(),
    SessionId: ssid,
  })
  const res = await login(loginReq)

  // 如果登录成功，则更新登录状态
  if (res.Code === LoginRespCode.LoginSuccess) {
    const { setSessionId, setLoginStatus, setUserOrgRid, setUserRid, setUserName, setVersion } = loginStatusStore
    setSessionId(res.SessionId)
    setLoginStatus(true)
    setUserOrgRid(res.UserOrgRid)
    setUserRid(res.UserRid)
    setUserName(loginReq.Name)
    setVersion(res.ServerVersion)

    // 记住账号密码
    const rememberAccountStore = useRememberAccountStore()
    const { account, setAccount } = rememberAccountStore
    if (account.SessionId === ssid) {
      await setAccount({ ...loginReq, SessionId: res.SessionId })
    }
  } else {
    const { clearLoginStatus } = loginStatusStore
    // 如果登录失败，弹出对话框提示用户退出登录
    Dialog.create({
      title: i18n.global.t('common.sessionExpired'),
      message: i18n.global.t('common.sessionExpiredDesc'),
      persistent: true,
      ok: {
        label: i18n.global.t('common.backToLogin'),
        color: 'primary',
        // flat: true,
        autofocus: true,
      },
    }).onOk(async () => {

      // 清除登录状态
      clearLoginStatus()
      router.replace({
        path: '/login'
      })
    })
  }

  // 向等待的请求返回登录结果
  autoLoginDeffer?.resolve(res)
  autoLoginDeffer = null
  return res
}

// 拦截器，主要用于添加Session-Id字段，和打印请求参数与响应结果
const rpcInterceptor: Interceptor = (next) => {
  const loginStatusStore = useLoginStatusStore()
  // storeToRefs  确保响应性
  const { isLogin, sessionId } = storeToRefs(loginStatusStore)

  // 如果是流式请求，将message异步可迭代对象提前消费缓存下来，出错时二次请求
  let iterableFactory: (() => AsyncIterable<unknown>) | null = null
  const callAgainWithLogin = async (req: any, callUid: number) => {
    const loginResp = await autoSessionIdLogin(sessionId.value)
    // 如果登录成功，重新请求
    if (loginResp.Code === LoginRespCode.LoginSuccess) {
      req.header.set('Session-Id', loginResp.SessionId)
      // 是流式请求且异步可迭代对象已被缓存
      if (req.stream && iterableFactory) {
        req.message = iterableFactory()
      }
      const res2 =  await next(req)
      logger.debug(`[rpc interceptor] callAgainWithLogin result: callUid=${callUid}`, res2.message)
      iterableFactory = null
      return res2
    }
  }

  return async (req: any) => {
    const callUid = ++rpcCallUid
    const apiFrom = new Error().stack?.split('\n')?.[5]?.trim()
    logger.debug(`%c--- new rpc call ---`, 'color:blue')
    logger.debug(`[rpc interceptor] from: ${apiFrom}`)
    logger.debug(`[rpc interceptor] req: isLogin=${isLogin.value}, sessionId='${sessionId.value}', callUid=${callUid}`, req.message)

    // 第一次进入将异步可迭代对象缓存下来，避免二次请求时重新消费
    if (req.stream) {
      iterableFactory = await cacheAsyncIterable(req.message)
      req.message = iterableFactory()
    }

    // @connectrpc一般默认自带header
    if (!req.header) {
      req.header = new Headers({
        'Content-Type': 'application/proto'
      })
    }

    if (isLogin.value) {
      req.header.set('Session-Id', sessionId.value)
      // req.header.set('Ygrpc-Err-Header', '1')
      // req.header.set('Ygrpc-Err-Max', '512')
    }

    try {
      const res = await next(req)
      logger.debug(`[rpc interceptor] result: callUid=${callUid}`, res.message)
      // check response session id is valid
      // after login, auto request api again
      const ErrInfo= res.header.get('ygrpc-err')
      if (ErrInfo === 'session not found') {
        const res2 = await callAgainWithLogin(req, callUid)
        if (res2) {
          iterableFactory = null
          return res2
        }
      }
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      if (!res.stream && (res.message.$typeName === 'rpc.Common' || res.message.$typeName === 'rpc.GetCurrentUsageResp')) {
        const message = res.message as Common
        if (message.Code === CommonRespCode.InvalidSessionId) {
          const res2 = await callAgainWithLogin(req, callUid)
          if (res2) {
            return res2
          }
        }
      }
      return res
    } catch (err) {
      logger.error(`[rpc interceptor] error: callUid=${callUid}`, err)
      let connErr = ConnectError.from(err)
      if (connErr.message.includes('session not found')) {
       try {
         const res2 = await callAgainWithLogin(req, callUid)
         if (res2) {
           iterableFactory = null
           return res2
         }
       } catch (err2) {
         logger.error(`[rpc interceptor] error2: callUid=${callUid}`, err2)
         connErr = ConnectError.from(err2)
       }
      }

      throw  connErr
    }
  }
}

// A transport for clients using the Connect protocol with fetch()
const transport = createConnectTransport({
  baseUrl: rpcUrl,
  useBinaryFormat: true,
  interceptors: [
    rpcInterceptor,
  ],
  defaultTimeoutMs: 15 * 1000, // 15s
})
export const bfmapClient = createClient<typeof bfmap>(bfmap, transport)

// 用一个函数包装rpcApi，处理通用错误
async function withCommonResponse(rpcApi: Promise<Common>): Promise<Common> {
  return await rpcApi
    .catch(err => {
      logger.error('rpcApi error:', err)
      return create(CommonSchema, {
        Code: -100,
        Reason: 'rpcApi error',
        Msg: JSON.stringify(err),
      })
    })
}

// 请求服务器是否有初始化过
export async function isSetup(): Promise<number> {
  const res: Common = await withCommonResponse(bfmapClient.isSetup({}))
  return res.Code
}

// 第一次初始化后，需要设置标记
export async function setup(req: SetupReq): Promise<boolean> {
  const res: Common = await withCommonResponse(bfmapClient.setup(req))
  return res.Code === 0
}

// 登录请求
export async function login(req: LoginReq): Promise<LoginResp> {
  const res = await bfmapClient.login(req)
    .catch(err => {
      logger.error('login error:', err)
      return create(LoginRespSchema, {
        // 内部错误失败
        Code: 9,
        Reason: JSON.stringify(err),
      })
    })
  return res
}

// 退出登录
export async function logout(): Promise<boolean> {
  const res: Common = await withCommonResponse(bfmapClient.logout({}))
  return res.Code === 0
}

// 设置地图服务商token
export async function setProviderToken(req: SetProviderTokenReq): Promise<Common> {
  return await withCommonResponse(bfmapClient.setProviderToken(req))
}

// 设置项目级token
export async function setProjectToken(req: SetProjectTokenReq): Promise<Common> {
  return await withCommonResponse(bfmapClient.setProjectToken(req))
}

// 设置项目
export async function setProject(req: SetProjectReq): Promise<Common> {
  return await withCommonResponse(bfmapClient.setProject(req))
}

// 添加用户及其权限（会给用户设置一个默认项目）
export async function createUser(req: CreateUserReq): Promise<Common> {
  return await withCommonResponse(bfmapClient.createUser(req))
}

// 请求指定api/项目的用量 （从服务器缓存） provider token / project / project token
export async function getCurrentUsageReq(req: GetCurrentUsageReq): Promise<GetCurrentUsageResp> {
  const res = await bfmapClient.getCurrentUsage(req)
    .catch(err => {
      logger.error('GetCurrentUsageReq error:', err)
      return create(GetCurrentUsageRespSchema, {
        // 内部错误失败
        Code: 9,
        Reason: JSON.stringify(err),
      })
    })
  return res
}

// 清楚指定的地图缓存
export async function clearMapCache(req: DeleteMapCacheIndexesReq): Promise<Common> {
  return await withCommonResponse(bfmapClient.deleteMapCacheIndexes(req))
}

// 请求临时地图token
export async function createTempMapToken(): Promise<Common> {
  return withCommonResponse(bfmapClient.createTempMapProjectToken({}))
}

// 更新用户信息
export async function updateDbUser(req: UpdateDbUserReq): Promise<Common> {
  return withCommonResponse(bfmapClient.updateDbUser(req))
}

/*
 * 以下为数据库表crud接口
 */

export const dbClient = createClient<typeof ProtoDbSrv>(ProtoDbSrv, transport)

/**
 * 数据库表增、删、改接口
 */
export async function crudWithReqCode<T extends Message>(schema: GenMessage<T>, row: T, reqCode: CrudReqCode): Promise<CrudResp> {
  // logger.debug('crudWithReqCode: ',schema, row, reqCode)
  const bytes = toBinary(schema, row)
  const req = create(CrudReqSchema, {
    Code: reqCode,
    ResultType: CrudResultType.DMLResult,
    TableName: schema.name,
    MsgBytes: bytes,
  })
  return dbClient.crud(req)
    .catch(err => {
      return create(CrudRespSchema, {
        RowsAffected: BigInt(0),
        ErrInfo: JSON.stringify(err),
      })
    })
}

/**
 * 插入一行数据
 */
export async function insertRow<T extends Message>(schema: GenMessage<T>, row: T): Promise<CrudResp> {
  return crudWithReqCode(schema, row, CrudReqCode.INSERT)
}

/**
 * 更新一行数据
 */
export async function updateRow<T extends Message>(schema: GenMessage<T>, row: T): Promise<CrudResp> {
  return crudWithReqCode(schema, row, CrudReqCode.UPDATE)
}

/**
 * 更新一行数据部分字段
 */
export async function partialUpdateRow<T extends Message>(schema: GenMessage<T>, row: T): Promise<CrudResp> {
  return crudWithReqCode(schema, row, CrudReqCode.PARTIALUPDATE)
}

/**
 * 删除一行数据
 */
export async function deleteRow<T extends Message>(schema: GenMessage<T>, row: T): Promise<CrudResp> {
  return crudWithReqCode(schema, row, CrudReqCode.DELETE)
}

/**
 * 根据条件查询数据表
 * @param schema
 * @param where sql where condition Fieldname == Value
 * @param where2Operator sql Where2Operator condition Fieldname operator
 * @param where2 sql where condition Fieldname == Value (need with where2Operator)
 */
export async function tableQuery<T extends Message>(
  schema: GenMessage<T>,
  where?: { [key: string]: string },
  where2Operator?: { [key: string]: WhereOperator },
  where2?: { [key: string]: string }
): Promise<Array<T>> {
  const req = create(TableQueryReqSchema, {
    TableName: schema.name,
    ResultColumnNames: schema.fields.map((f) => f.name),
    PreferBatchSize: 500,
    Where: { ...where },
    Where2Operator: { ...where2Operator },
    Where2: { ...where2 }
  })
  logger.debug('tableQuery:', schema, req)
  const stream = dbClient.tableQuery(req)

  let rows: Array<T> = []
  for await (const resp of stream) {
    switch (resp.MsgFormat) {
      case 0:
        rows.push(...resp.MsgBytes.map(bytes => fromBinary(schema, bytes)))
        break
      case 1:
        // rows.push(...resp.MsgBytes.map(bytes => fromJson(schema, bytes)))
        break
    }
  }

  return rows
}

/**
 * 根据条件查询数据表
 * @param schema
 * @param where sql where condition Fieldname == Value
 */
export async function dbQuery<T extends Message>(schema: GenMessage<T>, where?: {
  [key: string]: string
}): Promise<Array<T>> {
  const req = create(QueryReqSchema, {
    QueryName: schema.name,
    ResultColumnNames: schema.fields.map((f) => f.name),
    PreferBatchSize: 500,
    Where: { ...where },
  })
  logger.debug('dbQuery:', schema, req)
  const stream = dbClient.query(req)

  let rows: Array<T> = []
  for await (const resp of stream) {
    switch (resp.MsgFormat) {
      case 0:
        rows.push(...resp.MsgBytes.map(bytes => fromBinary(schema, bytes)))
        break
      case 1:
        // rows.push(...resp.MsgBytes.map(bytes => fromJson(schema, bytes)))
        break
    }
  }

  return rows
}

// 初始化服务器setup状态
export async function initSetupCode() {
  let setupCode = 1
  initSetupCodePending.value = true

  try {
    const code = getLocalSetupCode()
    if (code !== null) {
      setupCode = code
      logger.log('from local setupCode:', setupCode)
      return
    }

    setupCode = await isSetup()
    logger.log('from server setupCode:', setupCode)
  } catch (error: any) {
    logger.error('initSetupCode catch error:', error)
    setupCode = 1
  } finally {
    initSetupCodePending.value = false
    setServerSetupCode(setupCode)
  }
}
