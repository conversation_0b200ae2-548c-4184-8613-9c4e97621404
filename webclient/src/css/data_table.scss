.sticky-header-table {
  & > :first-child {
    border-bottom: solid 1px #E0E0E0;
  }
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th {
    /* bg color is important for th; just specify one */
    background-color: #fff;
  }

  thead tr th {
    position: sticky;
    z-index: 1;
  }

  thead tr:first-child th {
    top: 0;
  }

  /* this is when the loading indicator appears */
  &.q-table--loading thead tr:last-child th {
    /* height of all previous header rows */
    top: 48px;
  }

  /* prevent scrolling behind sticky top row on focus */
  tbody {
    /* height of all previous header rows */
    scroll-margin-top: 48px;
  }

  .q-table {
    border-bottom: 1px solid #E0E0E0;
  }
}

.api-table {
  & > .q-table__middle > .q-table {
    border-bottom: none;
  }
}
