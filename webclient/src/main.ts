import { createApp } from 'vue'
import { Quasar, Dialog, Notify, SessionStorage } from 'quasar'
import quasarIconSet from 'quasar/icon-set/svg-mdi-v7'

// Import icon libraries
import '@quasar/extras/material-icons/material-icons.css'
import '@quasar/extras/mdi-v7/mdi-v7.css'

// Import Quasar css
import 'quasar/src/css/index.sass'

import '@/css/tailwind.css'

// Assumes your root component is App.vue
// and placed in same folder as main.js
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import { initSetupCode } from '@/services/connectRpc'
import { i18n, initLocales } from '@/i18n/i18n'
import clearFix from '@/utils/directives.ts'

const myApp = createApp(App)
const pinia = createPinia()  // 创建 Pinia 实例

myApp.use(Quasar, {
  plugins: { Dialog, Notify, SessionStorage }, // import Quasar plugins and add here
  iconSet: quasarIconSet,
})

myApp.use(i18n)
myApp.use(pinia)
myApp.use(router)
myApp.directive('clear-fix', clearFix)

initLocales()
initSetupCode()

// Assumes you have a <div id="app"></div> in your index.html
myApp.mount('#app')
