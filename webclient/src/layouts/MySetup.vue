<template>
  <main class="w-screen h-screen flex flex-col items-center">
    <q-banner class="min-w-[60vw] text-center mx-3 my-6">
      <div class="text-4xl">{{ $t('welcome') }}</div>
      <div class="text-xl text-red-600 mt-3">{{ $t('welcomeInfo') }}</div>
    </q-banner>

    <q-card class="w-lg xs:w-full p-4">
      <q-form
        class="w-full"
        @submit.prevent="submitAdminAccount"
      >
        <q-input
          filled
          v-model="setupReq.Name"
          :label="$t('userName')"
          :maxlength="16"
          :rules="[rules.required]"
        />

        <q-input
          filled
          :type="isPwd ? 'password' : 'text'"
          v-model="password"
          :label="$t('password')"
          :maxlength="16"
          :rules="[rules.required, rules.allowedChars, rules.password]"
        >
          <template v-slot:append>
            <q-icon
              :name="isPwd ? 'visibility_off' : 'visibility'"
              class="cursor-pointer"
              @click="isPwd = !isPwd"
            />
          </template>
        </q-input>

        <q-input
          filled
          v-model="setupReq.OrgName"
          :label="$t('defaultOrgName')"
          :maxlength="16"
          :rules="[rules.required]"
        />

        <q-input
          filled
          v-model="setupReq.ProjectName"
          :label="$t('defaultProjectName')"
          :maxlength="16"
          :rules="[rules.required]"
        />

        <div class="flex justify-center mt-2">
          <q-btn
            type="submit"
            color="primary"
            :label="$t('form.submit')"
            :loading="submitting"
            :disable="submitting"
            class="!w-40"
          />
        </div>
      </q-form>
    </q-card>
  </main>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { Dialog } from 'quasar'
  import { SetupReqSchema } from '@/proto/bfmap.rpc_pb'
  import { create } from "@bufbuild/protobuf"
  import { setup } from '@/services/connectRpc'
  import { encodeUserPassword } from '@/utils/crypto'
  import { useServerSetupStore } from '@/stores/serverSetup'
  import { useI18n } from 'vue-i18n'
  import DialogComponent from '@/components/DialogComponent.vue'
  import { required, validatePasswordWithAllowedChars, passwordAtLeast } from '@/utils/validation'

  const i18n = useI18n()
  const router = useRouter()
  const serverSetupStore = useServerSetupStore()
  const isPwd = ref(true)
  // 表单明文密码
  const password = ref('')
  const setupReq = reactive(create(SetupReqSchema, {
    Name: 'admin',
    // 编码后的数据库密码
    Password: '',
    OrgName: 'Default',
    ProjectName: 'Default',
  }))

  const rules = computed(() => ({
    required: (val: string | null | undefined) => required(val),
    allowedChars: (val: string) => validatePasswordWithAllowedChars(val),
    password: (val: string) => passwordAtLeast(val),
  }))

  const submitting = ref(false)
  const confirmSubmit = () => {
    return new Promise((resolve) => {
      Dialog.create({
        component: DialogComponent,
        componentProps: {
          showHeader: false,
          defaultContent: i18n.t('setup.confirmSubmitMsg'),
          ok: {
            label: i18n.t('form.confirm'),
            color: 'positive',
            autofocus: true,
            flat: false
          },
          cancel: {
            label: i18n.t('form.cancel'),
            color: 'primary',
            flat: true
          }
        }
      }).onOk(() => {
        resolve(true)
      }).onCancel(() => {
        resolve(false)
      })
    })
  }

  const jumpToHomeSecond = ref(5)
  const jumpToHomeContent = computed(() => {
    return i18n.t('setup.willJumpToHome', { second: jumpToHomeSecond.value })
  })
  const goToHomeAlert = async () => {
    return new Promise((resolve) => {
      const dlg = Dialog.create({
        component: DialogComponent,
        componentProps: {
          showHeader: true,
          title: i18n.t('setup.setSuccess'),
          defaultContent: jumpToHomeContent,
          ok: {
            color: 'primary',
            label: i18n.t('setup.jumpNow'),
            flat: false
          }
        },
      }).onDismiss(() => {
        resolve(true)
        clearInterval(timer)
        jumpToHomeSecond.value = 5
      })

      const timer = setInterval(() => {
        jumpToHomeSecond.value--

        if (jumpToHomeSecond.value >= 0) {
          dlg.update({
            componentProps: {
              defaultContent: jumpToHomeContent
            }
          })
        } else {
          clearInterval(timer)
          dlg.hide()
          jumpToHomeSecond.value = 5
          resolve(true)
        }
      }, 1000)
    })
  }
  const submitAdminAccount = async () => {
    // 二次确认
    const confirm = await confirmSubmit()
    if (!confirm) return

    // 设置setup信息
    setupReq.Password = encodeUserPassword(setupReq.Name, password.value)
    submitting.value = true
    const isSetup = await setup(setupReq)
    if (!isSetup) {
      submitting.value = false
      Dialog.create({
        component: DialogComponent,
        componentProps: {
          showCloseBtn: false,
          title: i18n.t('setup.setFailed'),
          defaultContent: i18n.t('setup.setupFailedMessage'),
          ok: {
            label: i18n.t('form.confirm'),
            color: 'primary',
            flat: false
          }
        },
      }).onOk(() => {
        // console.log('OK')
      }).onCancel(() => {
        // console.log('Cancel')
      }).onDismiss(() => {
      })
      return
    }

    // 完成setup,提示用户跳转首页
    submitting.value = false
    await goToHomeAlert()

    serverSetupStore.setServerSetupCode(0)
    router.replace({ path: '/login' })
  }
</script>
