<template>
  <q-layout>
    <q-page-container>
      <q-page class="flex flex-center bg-gray-100 my-login relative">
        <switch-language class="absolute top-3 right-4 z-10" />

        <q-img
          src="@/assets/bg_login.svg"
          class="fixed inset-0"
        />

        <q-card class="w-sm max-w-[90vw] !rounded-xl">
          <q-card-section class="bg-primary text-white text-2xl text-center !p-6">
            {{ $t('welcome') }}
          </q-card-section>

          <q-card-section class="!p-6">
            <q-form
              @submit="onSubmit"
              class="grid gap-2 grid-cols-1 login-form"
            >
              <q-input
                v-model="loginReq.Name"
                outlined
                :label="$t('myLogin.userName')"
                lazy-rules
                :rules="[rules.required]"
                :maxlength="16"
                :debounce="500"
                @update:model-value="clearSessionId()"
              >
                <template #prepend>
                  <i class="mdi mdi-account"></i>
                </template>
              </q-input>

              <q-input
                v-model="password"
                autocomplete="new-password"
                outlined
                :type="isPwd ? 'password' : 'text'"
                :label="$t('myLogin.password')"
                lazy-rules
                :rules="[rules.required, rules.allowedChars, rules.password]"
                :maxlength="16"
                :debounce="500"
                @update:model-value="clearSessionId()"
              >
                <template #prepend>
                  <i class="mdi mdi-lock"></i>
                </template>
                <template #append>
                  <q-icon
                    :name="isPwd ? 'visibility_off' : 'visibility'"
                    class="cursor-pointer"
                    @click="isPwd = !isPwd"
                  />
                </template>
              </q-input>

              <q-checkbox
                v-model="rememberAccount"
                :label="$t('myLogin.rememberAccount')"
              />

              <div class="flex justify-center actions">
                <q-btn
                  :label="$t('myLogin.signIn')"
                  type="submit"
                  color="primary"
                  unelevated
                  no-caps
                  icon="login"
                  :disabled="isLogging"
                  :loading="isLogging"
                  class="!w-full text-white"
                >
                  <template #loading>
                    <q-spinner-facebook />
                  </template>
                </q-btn>
              </div>
            </q-form>
          </q-card-section>
        </q-card>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, defineAsyncComponent } from 'vue'
  import { useRouter } from 'vue-router'
  import { useQuasar } from 'quasar'
  import { create } from "@bufbuild/protobuf"
  import { LoginReqSchema, LoginRespCode, LoginMethod } from '@/proto/bfmap.rpc_pb'
  import type { LoginResp } from '@/proto/bfmap.rpc_pb'
  import { login } from '@/services/connectRpc'
  import { encodeLoginPassword } from '@/utils/crypto'
  import { utcTime } from '@/utils/dayjs'
  import { useLoginStatusStore, useRememberAccountStore } from '@/stores/session'
  import { v7 as uuidV7 } from 'uuid'
  import { storeToRefs } from 'pinia'
  import * as validation from '@/utils/validation'
  import { useI18n } from 'vue-i18n'

  const switchLanguage = defineAsyncComponent(() => import('@/components/SwitchLanguage.vue'))
  const $q = useQuasar()
  const { t } = useI18n()
  const router = useRouter()
  const loginStatusStore = useLoginStatusStore()
  const { setSessionId, setLoginStatus, setUserOrgRid, setUserRid, setUserName, setVersion } = loginStatusStore
  const rememberAccountStore = useRememberAccountStore()
  const { account } = storeToRefs(rememberAccountStore)
  const { setAccount, clearAccount } = rememberAccountStore

  const isPwd = ref(true)
  const password = ref('')
  const rememberAccount = ref(true)
  const loginReq = reactive(create(LoginReqSchema, {
    LoginMethod: LoginMethod.Password,
    Name: '',
    // 编码后的数据库密码
    PasswordHash: '',
    TimeStr: '',
    SessionId: '',
  }))

  watch(account, (newVal) => {
    if (!newVal) {
      return
    }
    Object.assign(loginReq, newVal, { LoginMethod: LoginMethod.SessionId })
    if (newVal.SessionId) {
      password.value = '*'.repeat(6)
    }
  }, { immediate: true, deep: true })


  const rules = computed(() => ({
    required: (val: string | null | undefined) => validation.required(val),
    allowedChars: (val: string) => validation.validatePasswordWithAllowedChars(val),
    password: (val: string) => validation.passwordAtLeast(val),
  }))

  const clearSessionId = () => {
    loginReq.LoginMethod = LoginMethod.Password
  }

  // 登录成功，跳转到首页
  const onLoginSuccess = (resp: LoginResp) => {
    $q.notify({
      color: 'positive',
      icon: 'done',
      message: t('myLogin.loginSuccessful'),
      position: 'top',
      timeout: 1000
    })

    setSessionId(resp.SessionId)
    setLoginStatus(true)
    setUserOrgRid(resp.UserOrgRid)
    setUserRid(resp.UserRid)
    setUserName(loginReq.Name)

    // 记住账号密码
    if (rememberAccount.value) {
      setAccount({...loginReq, SessionId: resp.SessionId})
    } else {
      clearAccount()
    }

    // 设置版本号
    setVersion(resp.ServerVersion)

    router.replace({ path: '/' })
  }

  // 登录失败，弹出错误信息
  const onLoginFail = (resp: LoginResp) => {
    let message = t('myLogin.loginFailed')

    switch (resp.Code) {
      case LoginRespCode.ReqTimeTooOld:
        message = t('myLogin.ReqTimeTooOld')
        break
      case LoginRespCode.UserNotExist:
        message = t('myLogin.UserNotExist')
        break
      case LoginRespCode.PasswordNotMatch:
        message = t('myLogin.PasswordNotMatch')
        break
      case LoginRespCode.SessionIdNotExist:
        message = t('myLogin.SessionIdNotExist')
        break
      case LoginRespCode.SessionIdExpired:
        message = t('myLogin.SessionIdExpired')
        break
      case LoginRespCode.SessionAlreadyLogin:
        message = t('myLogin.SessionAlreadyLogin')
        break
      case LoginRespCode.FailWithInternalError:
        message = t('myLogin.FailWithInternalError')
        break
      case LoginRespCode.UserDisabled:
        message = t('myLogin.UserDisabled')
        break

      default:
        message += ` ${resp.Reason}`
        break
    }

    $q.notify({
      color: 'negative',
      icon: 'error',
      message: message,
      position: 'top',
      timeout: 3500
    })
  }

  const isLogging = ref(false)
  const onSubmit = async () => {
    loginReq.TimeStr = utcTime()
    if (loginReq.LoginMethod === LoginMethod.Password) {
      loginReq.PasswordHash = encodeLoginPassword(loginReq.Name, password.value, loginReq.TimeStr)
      loginReq.SessionId = uuidV7()
    } else {
      loginReq.PasswordHash = '*'
    }
    isLogging.value = true
    const res = await login(loginReq)
    isLogging.value = false
    if (res.Code === LoginRespCode.LoginSuccess) {
      onLoginSuccess(res)
    } else {
      onLoginFail(res)
    }
  }
</script>

<style scoped></style>
