<template>
  <q-layout view="hHh lpR fFf">
    <q-header
      reveal
      elevated
      class=""
    >
      <q-toolbar>
        <q-btn
          @click="leftDrawerOpen = !leftDrawerOpen"
          round
          dense
          flat
          icon="menu"
        />

        <q-toolbar-title v-if="!$q.screen.xs">
          <q-avatar
            icon="map"
            size="lg"
            font-size="38px"
          />
          {{ $t('siteTitle') }}
        </q-toolbar-title>
        <q-space v-else />

        <div class="flex gap-3">
          <new-project />

          <switch-language />

          <user-info />
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      side="left"
      show-if-above
      bordered
      class="flex flex-col flex-nowrap left-drawer"
    >
      <q-item
        v-if="$q.screen.xs"
        class="flex-none bg-primary text-white max-h-64 !px-5 !py-4 items-center"
      >
        <q-item-section avatar>
          <q-icon
            name="map"
            size="lg"
          />
        </q-item-section>

        <q-item-label class="text-xl">
          {{ $t('siteTitle') }}
        </q-item-label>
      </q-item>

      <q-scroll-area class="w-full flex-auto">
        <q-list padding>
          <template
            v-for="route in filteredRoutes"
            :key="route.name"
          >
            <!-- 有子路由的情况 -->
            <q-expansion-item
              v-if="route.children?.length > 0"
              :icon="route.meta?.icon || 'description'"
              :label="(route.meta?.title || route.name)?.toString()"
              default-opened
            >
              <q-item
                v-for="child in route.children"
                :key="child.name"
                clickable
                v-ripple
                :to="child.path"
                exact
              >
                <q-item-section avatar>
                  <q-icon :name="child.meta?.icon || 'description'" />
                </q-item-section>
                <q-item-section>
                  {{ child.meta?.title || child.name }}
                </q-item-section>
              </q-item>
            </q-expansion-item>

            <!-- 没有子路由的情况 -->
            <q-item
              v-else
              clickable
              v-ripple
              :to="route.path"
              exact
            >
              <q-item-section avatar>
                <q-icon :name="route.meta?.icon || 'description'" />
              </q-item-section>
              <q-item-section>
                {{ route.meta?.title || route.name }}
              </q-item-section>
            </q-item>
          </template>
        </q-list>
      </q-scroll-area>

      <q-card
        flat
        class="flex-none text-center text-sm !text-gray-500"
      >
        <q-card-section>
          <q-item-label>v{{ version }}</q-item-label>
          <q-item-label>{{ $t('copyrightInfo') }}</q-item-label>
        </q-card-section>
      </q-card>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useLoginStatusStore } from '@/stores/session'
  import { useRouter } from 'vue-router'
  import { initSystemBaseData } from '@/services/request'
  import { useQuasar } from 'quasar'
  import NewProject from '@/components/NewProject.vue'
  import SwitchLanguage from '@/components/SwitchLanguage.vue'
  import UserInfo from '@/components/UserInfo.vue'

  const $q = useQuasar()
  const router = useRouter()
  const loginStatusStore = useLoginStatusStore()
  const { version } = storeToRefs(loginStatusStore)
  const leftDrawerOpen = ref(false)

  // 过滤出需要显示在侧边栏的路由
  const filteredRoutes = computed(() => {
    return router.getRoutes().filter(route => {
      return !!route.meta?.nav
    })
  })

  initSystemBaseData()
</script>

<style scoped></style>
