import { createI18n } from 'vue-i18n'
import { StrPubSub } from 'ypubsub'
import { Quasar, type QuasarLanguage } from 'quasar'
import { ToggleI18nLang } from '@/utils/pubSubSubject'
import localforage from 'localforage'
import logger from '@/utils/logger.ts'

const StoreLangKey = 'locale'
// 已经加载过的语言
const ResolveLanguage: Set<string> = new Set()

export enum appLang {
  // eslint-disable-next-line no-unused-vars
  zh = 'zh',
  // eslint-disable-next-line no-unused-vars
  en = 'en',
}

const appLangList: string[] = [appLang.zh, appLang.en]

export const i18n = createI18n({
  legacy: false,
  locale: appLang.zh,
  fallbackLocale: appLang.en,
  messages: {},
  globalInjection: true,
})

// 修改系统标题
function changeSiteTitle(): void {
  document.title = i18n.global.t('siteTitle') as string
}

// 返回当前系统的语言: 缓存 > 配置 > 浏览器语言
export async function getSystemLang(): Promise<string> {
  const localLang = await localforage.getItem<string>(StoreLangKey)
  let locale = String(localLang || window.navigator.language)
  locale = appLangList.includes(locale) ? locale : locale.startsWith('zh') ? appLang.zh : appLang.en
  return locale
}

// 初始化i18n
export async function initLocales() {
  const lang = await getSystemLang()
  // 不使用发布订阅的方式去第一次加载语言包，直接使用执行函数的方式，避免语言包还未加载完，页面就开始渲染，
  await loadAndSubscribeChangeLang(lang)
}

// import.meta.glob，生成异步导入的模块，只支持字符串配置
// 路径配置规则 https://github.com/mrmlnc/fast-glob#pattern-syntax
// quasar语言包只加载支持的中文、英文两种
const _quasarLocales = import.meta.glob('/node_modules/quasar/lang/(zh-CN|en-US).js')
const quasarLocales = Object.keys(_quasarLocales)
  .filter(key => appLangList.some(l => key.includes(l)))
  .map(key => {
    let name = key.split('/').pop() ?? ''
    name = name.replace(/\.js$/g, '')

    return {
      [name]: _quasarLocales[key],
    }
  })
  .reduce((p, c) => Object.assign(p, c), {})

// 按需要加载quasar框架语言包
async function getQuasarLang(lang: string): Promise<QuasarLanguage> {
  let quasarLang = 'en-US'
  if (lang.startsWith('zh')) {
    quasarLang = 'zh-CN'
  } else if (lang.startsWith('en')) {
    quasarLang = 'en-US'
  }
  const res: any = await quasarLocales[quasarLang]?.()
  return res?.default ?? {}
}

// 请求对应的语言文件
async function getLanguage(lang: string): Promise<{ [key: string]: any }> {
  const timestamp = Math.floor(Date.now() / 1000 / 60)
  const res: any = await fetch(`/locales/${lang}.json?t=${timestamp}`)
  if (!res.ok) {
    logger.warn(`[i18n] failed to load ${lang}.json`, res.status)
    return {}
  }
  try {
    return await res.json()
  } catch (e) {
    logger.error(`[i18n] invalid json in ${lang}.json`, e)
    return {}
  }
}

// 切换语言时加载未加载的语言包
export async function loadAndSubscribeChangeLang(lang: string) {
  // storage lang to local
  await localforage.setItem<string>(StoreLangKey, lang)
  // quasar lang package must be loading when locale is changed
  const quasarLanguagePack = await getQuasarLang(lang)
  Quasar.lang.set(quasarLanguagePack)

  // i18n lang package do not loading again
  if (ResolveLanguage.has(lang)) {
    i18n.global.locale.value = lang
    changeSiteTitle()
    return
  }
  // 没有加载过语言包，需要加载到i18n中
  const selfLanguagePack = await getLanguage(lang)
  i18n.global.setLocaleMessage(lang, selfLanguagePack)
  i18n.global.locale.value = lang
  ResolveLanguage.add(lang)
  changeSiteTitle()
}

// 监听切换语言
StrPubSub.subscribe(ToggleI18nLang, async (lang: string) => {
  await loadAndSubscribeChangeLang(lang)
})

// 切换语言
export function switchLanguage(lang: appLang) {
  StrPubSub.publish(ToggleI18nLang, lang)
}
