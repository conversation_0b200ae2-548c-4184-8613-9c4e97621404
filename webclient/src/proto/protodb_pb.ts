// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file protodb.proto (package protodb, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenExtension, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, extDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { FieldOptions, FileOptions, MessageOptions } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_descriptor } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file protodb.proto.
 */
export const file_protodb: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_descriptor]);

/**
 * @generated from message protodb.PDBFile
 */
export type PDBFile = Message<"protodb.PDBFile"> & {
  /**
   * name style(msg & field)
   * empty='go': default, go name style, better performance in crud operation in
   * go (like: UserName) 'snake': snake name style (like: user_name)
   *
   * @generated from field: string NameStyle = 1;
   */
  NameStyle: string;

  /**
   * comment for file
   *
   * @generated from field: repeated string Comment = 2;
   */
  Comment: string[];
};

/**
 * Describes the message protodb.PDBFile.
 * Use `create(PDBFileSchema)` to create a new message.
 */
export const PDBFileSchema: GenMessage<PDBFile> = /*@__PURE__*/
  messageDesc(file_protodb, 0);

/**
 * @generated from message protodb.PDBMsg
 */
export type PDBMsg = Message<"protodb.PDBMsg"> & {
  /**
   * comment for table
   *
   * @generated from field: repeated string Comment = 1;
   */
  Comment: string[];

  /**
   * sql prepends before create table
   *
   * @generated from field: repeated string SQLPrepend = 2;
   */
  SQLPrepend: string[];

  /**
   * sql appends before )
   *
   * @generated from field: repeated string SQLAppend = 3;
   */
  SQLAppend: string[];

  /**
   * sql appends after ) before ;
   *
   * @generated from field: repeated string SQLAppendsAfter = 4;
   */
  SQLAppendsAfter: string[];

  /**
   * sql appends after ;
   *
   * @generated from field: repeated string SQLAppendsEnd = 5;
   */
  SQLAppendsEnd: string[];

  /**
   * generate proto msg { {msg}}List in  xxx.list.proto
   * 0: auto if msg name start with db then generate { {msg}}List
   * 1: always generate { {msg}}List
   * 4: never generate { {msg}}List
   *
   * @generated from field: int32 MsgList = 6;
   */
  MsgList: number;

  /**
   * do not generate db table for this message
   *
   * @generated from field: bool NotDB = 7;
   */
  NotDB: boolean;

  /**
   * sql for migrate table
   *
   * @generated from field: repeated string SQLMigrate = 8;
   */
  SQLMigrate: string[];
};

/**
 * Describes the message protodb.PDBMsg.
 * Use `create(PDBMsgSchema)` to create a new message.
 */
export const PDBMsgSchema: GenMessage<PDBMsg> = /*@__PURE__*/
  messageDesc(file_protodb, 1);

/**
 * @generated from message protodb.PDBField
 */
export type PDBField = Message<"protodb.PDBField"> & {
  /**
   * do not generate db field in create table
   * when in update, do not update this field
   *
   * @generated from field: bool NotDB = 1;
   */
  NotDB: boolean;

  /**
   * is primary key
   *
   * @generated from field: bool Primary = 2;
   */
  Primary: boolean;

  /**
   * is unique key, if the unique include multiple columns, specify the UniqueName
   *
   * @generated from field: bool Unique = 3;
   */
  Unique: boolean;

  /**
   * is not null
   *
   * @generated from field: bool NotNull = 4;
   */
  NotNull: boolean;

  /**
   * reference to other table, sql like:  REFERENCES other_table(other_field)
   *
   * @generated from field: string Reference = 5;
   */
  Reference: string;

  /**
   * default value
   *
   * @generated from field: string DefaultValue = 6;
   */
  DefaultValue: string;

  /**
   * append sql before ,
   *
   * @generated from field: repeated string SQLAppend = 7;
   */
  SQLAppend: string[];

  /**
   * append sql after ,
   *
   * @generated from field: repeated string SQLAppendsEnd = 8;
   */
  SQLAppendsEnd: string[];

  /**
   * db no update
   * when in update, do not update this field, for example, create time
   *
   * @generated from field: bool NoUpdate = 9;
   */
  NoUpdate: boolean;

  /**
   * serial type 0:not serial type 2:smallint(serial2) 4:integer(serial4) 8:bigint(serial8)
   * strong advice not use serial type,it's hard in distributed system
   *
   * @generated from field: int32 SerialType = 10;
   */
  SerialType: number;

  /**
   * db type
   *
   * @generated from field: protodb.FieldDbType DbType = 11;
   */
  DbType: FieldDbType;

  /**
   * use custom db type when DbType is not good fit
   *
   * @generated from field: string DbTypeStr = 12;
   */
  DbTypeStr: string;

  /**
   * zero value treat as null for insert,update, especially for reference field
   *
   * @generated from field: bool ZeroAsNull = 13;
   */
  ZeroAsNull: boolean;

  /**
   * db no insert
   * when in insert, do not insert this field, for example, database has default value
   *
   * @generated from field: bool NoInsert = 14;
   */
  NoInsert: boolean;

  /**
   * comment for field
   *
   * @generated from field: repeated string Comment = 15;
   */
  Comment: string[];

  /**
   * unique group name
   * when a unique constrain include multiple column, specify the a group name for it
   *
   * @generated from field: string UniqueName = 16;
   */
  UniqueName: string;
};

/**
 * Describes the message protodb.PDBField.
 * Use `create(PDBFieldSchema)` to create a new message.
 */
export const PDBFieldSchema: GenMessage<PDBField> = /*@__PURE__*/
  messageDesc(file_protodb, 2);

/**
 * crud request
 *
 * @generated from message protodb.CrudReq
 */
export type CrudReq = Message<"protodb.CrudReq"> & {
  /**
   * @generated from field: protodb.CrudReqCode Code = 1;
   */
  Code: CrudReqCode;

  /**
   * @generated from field: protodb.CrudResultType ResultType = 2;
   */
  ResultType: CrudResultType;

  /**
   * @generated from field: string SchemeName = 3;
   */
  SchemeName: string;

  /**
   * @generated from field: string TableName = 4;
   */
  TableName: string;

  /**
   * @generated from field: bytes MsgBytes = 5;
   */
  MsgBytes: Uint8Array;

  /**
   * msg format 0:protobuf 1:protobuf json
   *
   * @generated from field: int32 MsgFormat = 6;
   */
  MsgFormat: number;

  /**
   * @generated from field: int32 MsgLastFieldNo = 7;
   */
  MsgLastFieldNo: number;

  /**
   * @generated from field: repeated string PartialUpdateFields = 8;
   */
  PartialUpdateFields: string[];

  /**
   * @generated from field: repeated string SelectResultFields = 9;
   */
  SelectResultFields: string[];

  /**
   * @generated from field: repeated string SelectOneKeyFields = 10;
   */
  SelectOneKeyFields: string[];
};

/**
 * Describes the message protodb.CrudReq.
 * Use `create(CrudReqSchema)` to create a new message.
 */
export const CrudReqSchema: GenMessage<CrudReq> = /*@__PURE__*/
  messageDesc(file_protodb, 3);

/**
 * Crud response
 *
 * @generated from message protodb.CrudResp
 */
export type CrudResp = Message<"protodb.CrudResp"> & {
  /**
   * RowsAffected in dml operation
   *
   * @generated from field: int64 RowsAffected = 1;
   */
  RowsAffected: bigint;

  /**
   * err info when error happened
   *
   * @generated from field: string ErrInfo = 2;
   */
  ErrInfo: string;

  /**
   * old row as msg
   *
   * @generated from field: bytes OldMsgBytes = 3;
   */
  OldMsgBytes: Uint8Array;

  /**
   * new row as msg
   *
   * @generated from field: bytes NewMsgBytes = 4;
   */
  NewMsgBytes: Uint8Array;

  /**
   * msg format 0:protobuf 1:protobuf json
   *
   * @generated from field: int32 MsgFormat = 8;
   */
  MsgFormat: number;
};

/**
 * Describes the message protodb.CrudResp.
 * Use `create(CrudRespSchema)` to create a new message.
 */
export const CrudRespSchema: GenMessage<CrudResp> = /*@__PURE__*/
  messageDesc(file_protodb, 4);

/**
 * @generated from message protodb.TableQueryReq
 */
export type TableQueryReq = Message<"protodb.TableQueryReq"> & {
  /**
   * @generated from field: string SchemeName = 1;
   */
  SchemeName: string;

  /**
   * @generated from field: string TableName = 2;
   */
  TableName: string;

  /**
   * result column names, need same as proto msg field name
   *
   * @generated from field: repeated string ResultColumnNames = 3;
   */
  ResultColumnNames: string[];

  /**
   * Fieldname == Value
   *
   * @generated from field: map<string, string> Where = 4;
   */
  Where: { [key: string]: string };

  /**
   * limit 0:no limit
   *
   * @generated from field: int32 Limit = 5;
   */
  Limit: number;

  /**
   * @generated from field: int64 Offset = 6;
   */
  Offset: bigint;

  /**
   * prefer batch size
   *
   * @generated from field: int32 PreferBatchSize = 7;
   */
  PreferBatchSize: number;

  /**
   * msg format 0:protobuf 1:protobuf json
   *
   * @generated from field: int32 MsgFormat = 8;
   */
  MsgFormat: number;

  /**
   * where2 field operator, fieldname -> op
   *
   * @generated from field: map<string, protodb.WhereOperator> Where2Operator = 9;
   */
  Where2Operator: { [key: string]: WhereOperator };

  /**
   * where2 field value, fieldname -> value
   *
   * @generated from field: map<string, string> Where2 = 10;
   */
  Where2: { [key: string]: string };
};

/**
 * Describes the message protodb.TableQueryReq.
 * Use `create(TableQueryReqSchema)` to create a new message.
 */
export const TableQueryReqSchema: GenMessage<TableQueryReq> = /*@__PURE__*/
  messageDesc(file_protodb, 5);

/**
 * @generated from message protodb.QueryResp
 */
export type QueryResp = Message<"protodb.QueryResp"> & {
  /**
   * response batch no, start from 0
   *
   * @generated from field: int64 ResponseNo = 1;
   */
  ResponseNo: bigint;

  /**
   * if it is last response
   *
   * @generated from field: bool ResponseEnd = 2;
   */
  ResponseEnd: boolean;

  /**
   * err info when error happened
   *
   * @generated from field: string ErrInfo = 3;
   */
  ErrInfo: string;

  /**
   * @generated from field: repeated bytes MsgBytes = 4;
   */
  MsgBytes: Uint8Array[];

  /**
   * msg format 0:protobuf 1:protobuf json
   *
   * @generated from field: int32 MsgFormat = 8;
   */
  MsgFormat: number;
};

/**
 * Describes the message protodb.QueryResp.
 * Use `create(QueryRespSchema)` to create a new message.
 */
export const QueryRespSchema: GenMessage<QueryResp> = /*@__PURE__*/
  messageDesc(file_protodb, 6);

/**
 * @generated from message protodb.QueryReq
 */
export type QueryReq = Message<"protodb.QueryReq"> & {
  /**
   * history sql
   *
   * @generated from field: string QueryName = 1;
   */
  QueryName: string;

  /**
   * result column names, need same as proto msg field name
   *
   * @generated from field: repeated string ResultColumnNames = 3;
   */
  ResultColumnNames: string[];

  /**
   * sql where condition Fieldname == Value
   *
   * @generated from field: map<string, string> Where = 4;
   */
  Where: { [key: string]: string };

  /**
   * custom where protobuf msg bytes
   *
   * @generated from field: bytes WhereMsgBytes = 5;
   */
  WhereMsgBytes: Uint8Array;

  /**
   * limit 0:no limit
   *
   * @generated from field: int32 Limit = 6;
   */
  Limit: number;

  /**
   * @generated from field: int64 Offset = 7;
   */
  Offset: bigint;

  /**
   * prefer batch size
   *
   * @generated from field: int32 PreferBatchSize = 8;
   */
  PreferBatchSize: number;

  /**
   * msg format 0:protobuf 1:protobuf json
   *
   * @generated from field: int32 MsgFormat = 9;
   */
  MsgFormat: number;

  /**
   * where2 field operator, fieldname op
   *
   * @generated from field: map<string, protodb.WhereOperator> Where2Operator = 10;
   */
  Where2Operator: { [key: string]: WhereOperator };

  /**
   * where2 field value, fieldname op value
   *
   * @generated from field: map<string, string> Where2 = 11;
   */
  Where2: { [key: string]: string };
};

/**
 * Describes the message protodb.QueryReq.
 * Use `create(QueryReqSchema)` to create a new message.
 */
export const QueryReqSchema: GenMessage<QueryReq> = /*@__PURE__*/
  messageDesc(file_protodb, 7);

/**
 * @generated from enum protodb.FieldDbType
 */
export enum FieldDbType {
  /**
   * auto match db type if DbTypeStr not set
   * pb type -> db type
   * bool -> bool
   * string -> text
   * int32 -> int
   * int64,uint32 -> bigint
   * float -> float
   * double -> double precision
   * bytes -> bytea
   *
   * @generated from enum value: AutoMatch = 0;
   */
  AutoMatch = 0,

  /**
   * @generated from enum value: BOOL = 1;
   */
  BOOL = 1,

  /**
   * @generated from enum value: INT32 = 2;
   */
  INT32 = 2,

  /**
   * @generated from enum value: INT64 = 3;
   */
  INT64 = 3,

  /**
   * @generated from enum value: FLOAT = 4;
   */
  FLOAT = 4,

  /**
   * @generated from enum value: DOUBLE = 5;
   */
  DOUBLE = 5,

  /**
   * @generated from enum value: TEXT = 6;
   */
  TEXT = 6,

  /**
   * @generated from enum value: JSONB = 7;
   */
  JSONB = 7,

  /**
   * @generated from enum value: UUID = 8;
   */
  UUID = 8,

  /**
   * @generated from enum value: TIMESTAMP = 9;
   */
  TIMESTAMP = 9,

  /**
   * @generated from enum value: DATE = 10;
   */
  DATE = 10,

  /**
   * @generated from enum value: BYTEA = 11;
   */
  BYTEA = 11,

  /**
   * ipv4 or ipv6 address
   *
   * @generated from enum value: INET = 12;
   */
  INET = 12,

  /**
   * @generated from enum value: UINT32 = 13;
   */
  UINT32 = 13,
}

/**
 * Describes the enum protodb.FieldDbType.
 */
export const FieldDbTypeSchema: GenEnum<FieldDbType> = /*@__PURE__*/
  enumDesc(file_protodb, 0);

/**
 * crud api code
 *
 * @generated from enum protodb.CrudReqCode
 */
export enum CrudReqCode {
  /**
   * @generated from enum value: INSERT = 0;
   */
  INSERT = 0,

  /**
   * @generated from enum value: UPDATE = 1;
   */
  UPDATE = 1,

  /**
   * @generated from enum value: PARTIALUPDATE = 2;
   */
  PARTIALUPDATE = 2,

  /**
   * @generated from enum value: DELETE = 3;
   */
  DELETE = 3,

  /**
   * @generated from enum value: SELECTONE = 4;
   */
  SELECTONE = 4,

  /**
   * @generated from enum value: QUERY = 5;
   */
  QUERY = 5,
}

/**
 * Describes the enum protodb.CrudReqCode.
 */
export const CrudReqCodeSchema: GenEnum<CrudReqCode> = /*@__PURE__*/
  enumDesc(file_protodb, 1);

/**
 * crud result type for return
 *
 * @generated from enum protodb.CrudResultType
 */
export enum CrudResultType {
  /**
   * only info dml result(affected rows)
   *
   * @generated from enum value: DMLResult = 0;
   */
  DMLResult = 0,

  /**
   * return new row as msg, selectone use this too
   *
   * @generated from enum value: NewMsg = 1;
   */
  NewMsg = 1,

  /**
   * return old row as msg and new row as msg
   *
   * @generated from enum value: OldMsgAndNewMsg = 2;
   */
  OldMsgAndNewMsg = 2,
}

/**
 * Describes the enum protodb.CrudResultType.
 */
export const CrudResultTypeSchema: GenEnum<CrudResultType> = /*@__PURE__*/
  enumDesc(file_protodb, 2);

/**
 * where operator
 *
 * @generated from enum protodb.WhereOperator
 */
export enum WhereOperator {
  /**
   * unknown
   *
   * @generated from enum value: WOP_UNKNOWN = 0;
   */
  WOP_UNKNOWN = 0,

  /**
   * > greater than
   *
   * @generated from enum value: WOP_GT = 1;
   */
  WOP_GT = 1,

  /**
   * < less than
   *
   * @generated from enum value: WOP_LT = 2;
   */
  WOP_LT = 2,

  /**
   * >= greater than or equal
   *
   * @generated from enum value: WOP_GTE = 3;
   */
  WOP_GTE = 3,

  /**
   * <= less than or equal
   *
   * @generated from enum value: WOP_LTE = 4;
   */
  WOP_LTE = 4,

  /**
   * like
   *
   * @generated from enum value: WOP_LIKE = 5;
   */
  WOP_LIKE = 5,

  /**
   * equal
   *
   * @generated from enum value: WOP_EQ = 6;
   */
  WOP_EQ = 6,
}

/**
 * Describes the enum protodb.WhereOperator.
 */
export const WhereOperatorSchema: GenEnum<WhereOperator> = /*@__PURE__*/
  enumDesc(file_protodb, 3);

/**
 * protodb service
 *
 * @generated from service protodb.ProtoDbSrv
 */
export const ProtoDbSrv: GenService<{
  /**
   * crud
   *
   * @generated from rpc protodb.ProtoDbSrv.Crud
   */
  crud: {
    methodKind: "unary";
    input: typeof CrudReqSchema;
    output: typeof CrudRespSchema;
  },
  /**
   * table query
   *
   * @generated from rpc protodb.ProtoDbSrv.TableQuery
   */
  tableQuery: {
    methodKind: "server_streaming";
    input: typeof TableQueryReqSchema;
    output: typeof QueryRespSchema;
  },
  /**
   * general query
   *
   * @generated from rpc protodb.ProtoDbSrv.Query
   */
  query: {
    methodKind: "server_streaming";
    input: typeof QueryReqSchema;
    output: typeof QueryRespSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_protodb, 0);

/**
 * @generated from extension: optional protodb.PDBFile pdbf = 1888;
 */
export const pdbf: GenExtension<FileOptions, PDBFile> = /*@__PURE__*/
  extDesc(file_protodb, 0);

/**
 * @generated from extension: optional protodb.PDBMsg pdbm = 1888;
 */
export const pdbm: GenExtension<MessageOptions, PDBMsg> = /*@__PURE__*/
  extDesc(file_protodb, 1);

/**
 * @generated from extension: optional protodb.PDBField pdb = 1888;
 */
export const pdb: GenExtension<FieldOptions, PDBField> = /*@__PURE__*/
  extDesc(file_protodb, 2);

