// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file bfmap.rpc.proto (package rpc, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { DbMapProviderToken, DbProject, DbProjectQuotas, DbProjectToken, DbUser, DbUserPrivilege, MapProviderEnum } from "./db_pb";
import { file_db } from "./db_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file bfmap.rpc.proto.
 */
export const file_bfmap_rpc: GenFile = /*@__PURE__*/
  fileDesc("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", [file_db]);

/**
 * empty msg
 *
 * @generated from message rpc.Empty
 */
export type Empty = Message<"rpc.Empty"> & {
};

/**
 * Describes the message rpc.Empty.
 * Use `create(EmptySchema)` to create a new message.
 */
export const EmptySchema: GenMessage<Empty> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 0);

/**
 * common msg
 *
 * @generated from message rpc.Common
 */
export type Common = Message<"rpc.Common"> & {
  /**
   * @generated from field: int32 Code = 1;
   */
  Code: number;

  /**
   * @generated from field: string Reason = 2;
   */
  Reason: string;

  /**
   * @generated from field: string Msg = 3;
   */
  Msg: string;

  /**
   * @generated from field: bytes Body = 4;
   */
  Body: Uint8Array;
};

/**
 * Describes the message rpc.Common.
 * Use `create(CommonSchema)` to create a new message.
 */
export const CommonSchema: GenMessage<Common> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 1);

/**
 * @generated from message rpc.SetupReq
 */
export type SetupReq = Message<"rpc.SetupReq"> & {
  /**
   * @generated from field: string Name = 1;
   */
  Name: string;

  /**
   * password, base64(sha256(Name+Password))
   *
   * @generated from field: string Password = 2;
   */
  Password: string;

  /**
   * @generated from field: string OrgName = 3;
   */
  OrgName: string;

  /**
   * @generated from field: string ProjectName = 4;
   */
  ProjectName: string;
};

/**
 * Describes the message rpc.SetupReq.
 * Use `create(SetupReqSchema)` to create a new message.
 */
export const SetupReqSchema: GenMessage<SetupReq> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 2);

/**
 * @generated from message rpc.LoginReq
 */
export type LoginReq = Message<"rpc.LoginReq"> & {
  /**
   * @generated from field: string Name = 1;
   */
  Name: string;

  /**
   * 0: password login，1: session id login
   *
   * @generated from field: rpc.LoginMethod LoginMethod = 2;
   */
  LoginMethod: LoginMethod;

  /**
   * value is base64(sha256(time_str+base64(sha256(Name+Password))))
   *
   * @generated from field: string PasswordHash = 3;
   */
  PasswordHash: string;

  /**
   * format in utc time: yyyy-mm-dd hh:mm:ss
   *
   * @generated from field: string TimeStr = 4;
   */
  TimeStr: string;

  /**
   * @generated from field: string SessionId = 5;
   */
  SessionId: string;
};

/**
 * Describes the message rpc.LoginReq.
 * Use `create(LoginReqSchema)` to create a new message.
 */
export const LoginReqSchema: GenMessage<LoginReq> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 3);

/**
 * @generated from message rpc.LoginResp
 */
export type LoginResp = Message<"rpc.LoginResp"> & {
  /**
   * @generated from field: rpc.LoginRespCode Code = 1;
   */
  Code: LoginRespCode;

  /**
   * if login fail, the reason will be set
   *
   * @generated from field: string Reason = 2;
   */
  Reason: string;

  /**
   * @generated from field: string SessionId = 3;
   */
  SessionId: string;

  /**
   * @generated from field: string UserRid = 4;
   */
  UserRid: string;

  /**
   * @generated from field: string UserOrgRid = 5;
   */
  UserOrgRid: string;

  /**
   * @generated from field: string ServerVersion = 6;
   */
  ServerVersion: string;
};

/**
 * Describes the message rpc.LoginResp.
 * Use `create(LoginRespSchema)` to create a new message.
 */
export const LoginRespSchema: GenMessage<LoginResp> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 4);

/**
 * @generated from message rpc.CreateUserReq
 */
export type CreateUserReq = Message<"rpc.CreateUserReq"> & {
  /**
   * @generated from field: dbproto.DbUser User = 1;
   */
  User?: DbUser;

  /**
   * @generated from field: dbproto.DbUserPrivilege UserPrivilege = 2;
   */
  UserPrivilege?: DbUserPrivilege;
};

/**
 * Describes the message rpc.CreateUserReq.
 * Use `create(CreateUserReqSchema)` to create a new message.
 */
export const CreateUserReqSchema: GenMessage<CreateUserReq> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 5);

/**
 * @generated from message rpc.SetProviderTokenReq
 */
export type SetProviderTokenReq = Message<"rpc.SetProviderTokenReq"> & {
  /**
   * 1:create
   * 2:update,if UpdateFields is empty, all the fields will be updated
   * 4:delete, mark old one as "delete"
   * 8:force delete,delete in db
   * Common.Body is DbMapProviderToken
   *
   * @generated from field: int32 Code = 1;
   */
  Code: number;

  /**
   * @generated from field: dbproto.DbMapProviderToken ProviderToken = 2;
   */
  ProviderToken?: DbMapProviderToken;

  /**
   * @generated from field: repeated string UpdateFields = 3;
   */
  UpdateFields: string[];
};

/**
 * Describes the message rpc.SetProviderTokenReq.
 * Use `create(SetProviderTokenReqSchema)` to create a new message.
 */
export const SetProviderTokenReqSchema: GenMessage<SetProviderTokenReq> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 6);

/**
 * @generated from message rpc.SetProjectReq
 */
export type SetProjectReq = Message<"rpc.SetProjectReq"> & {
  /**
   * 1:create
   * 2:update,if UpdateFields is empty, all the fields will be updated,
   * 4:delete, mark old one as "delete"
   * 8:force delete,delete in db
   * Common.Body is DbMapProviderToken
   *
   * @generated from field: int32 Code = 1;
   */
  Code: number;

  /**
   * set empty if do not want to update this
   *
   * @generated from field: dbproto.DbProject DbProject = 2;
   */
  DbProject?: DbProject;

  /**
   * set empty if do not want to update this
   *
   * @generated from field: dbproto.DbProjectQuotas DbProjectQuotas = 3;
   */
  DbProjectQuotas?: DbProjectQuotas;

  /**
   * @generated from field: repeated string UpdateProjectFields = 4;
   */
  UpdateProjectFields: string[];

  /**
   * @generated from field: repeated string UpdateQuotasFields = 5;
   */
  UpdateQuotasFields: string[];
};

/**
 * Describes the message rpc.SetProjectReq.
 * Use `create(SetProjectReqSchema)` to create a new message.
 */
export const SetProjectReqSchema: GenMessage<SetProjectReq> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 7);

/**
 * @generated from message rpc.SetProjectTokenReq
 */
export type SetProjectTokenReq = Message<"rpc.SetProjectTokenReq"> & {
  /**
   * 1:create
   * 2:update, if UpdateFields is empty, all the fields will be updated
   * 3:rotate token,create new one and mark old one status as "delete",
   * new DbProjectToken return in Common.Body
   * 4:delete, mark old one as "delete"
   * 8:force delete,delete in db
   * Common.Body is DbMapProviderToken
   *
   * @generated from field: int32 Code = 1;
   */
  Code: number;

  /**
   * @generated from field: dbproto.DbProjectToken ProjectToken = 2;
   */
  ProjectToken?: DbProjectToken;

  /**
   * @generated from field: repeated string UpdateFields = 3;
   */
  UpdateFields: string[];
};

/**
 * Describes the message rpc.SetProjectTokenReq.
 * Use `create(SetProjectTokenReqSchema)` to create a new message.
 */
export const SetProjectTokenReqSchema: GenMessage<SetProjectTokenReq> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 8);

/**
 * @generated from message rpc.DeleteMapCacheIndexesReq
 */
export type DeleteMapCacheIndexesReq = Message<"rpc.DeleteMapCacheIndexesReq"> & {
  /**
   * map type: 1:roadmap 2:satellite 3: hybrid
   *
   * @generated from field: int32 MapType = 1;
   */
  MapType: number;

  /**
   * @generated from field: repeated dbproto.MapProviderEnum Providers = 2;
   */
  Providers: MapProviderEnum[];

  /**
   * max zoom is 22
   *
   * @generated from field: int32 Zoom = 3;
   */
  Zoom: number;

  /**
   * @generated from field: double MinLon = 4;
   */
  MinLon: number;

  /**
   * @generated from field: double MaxLon = 5;
   */
  MaxLon: number;

  /**
   * @generated from field: double MinLat = 6;
   */
  MinLat: number;

  /**
   * @generated from field: double MaxLat = 7;
   */
  MaxLat: number;

  /**
   * <=0: no limit
   *
   * @generated from field: int64 CacheTime = 8;
   */
  CacheTime: bigint;
};

/**
 * Describes the message rpc.DeleteMapCacheIndexesReq.
 * Use `create(DeleteMapCacheIndexesReqSchema)` to create a new message.
 */
export const DeleteMapCacheIndexesReqSchema: GenMessage<DeleteMapCacheIndexesReq> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 9);

/**
 * @generated from message rpc.GetCurrentUsageReq
 */
export type GetCurrentUsageReq = Message<"rpc.GetCurrentUsageReq"> & {
  /**
   * 1:provider token 2:project 3:project token
   *
   * @generated from field: int32 Code = 1;
   */
  Code: number;

  /**
   * for provider token
   *
   * @generated from field: string UserRid = 2;
   */
  UserRid: string;

  /**
   * for provider token
   *
   * @generated from field: dbproto.MapProviderEnum Provider = 3;
   */
  Provider: MapProviderEnum;

  /**
   * for provider token
   *
   * @generated from field: string ProviderToken = 4;
   */
  ProviderToken: string;

  /**
   * for project
   *
   * @generated from field: string ProjectRid = 5;
   */
  ProjectRid: string;

  /**
   * for project token
   *
   * @generated from field: string ProjectToken = 6;
   */
  ProjectToken: string;
};

/**
 * Describes the message rpc.GetCurrentUsageReq.
 * Use `create(GetCurrentUsageReqSchema)` to create a new message.
 */
export const GetCurrentUsageReqSchema: GenMessage<GetCurrentUsageReq> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 10);

/**
 * @generated from message rpc.GetCurrentUsageResp
 */
export type GetCurrentUsageResp = Message<"rpc.GetCurrentUsageResp"> & {
  /**
   * 0:success, !0:fail
   *
   * @generated from field: int32 Code = 1;
   */
  Code: number;

  /**
   * @generated from field: string Reason = 2;
   */
  Reason: string;

  /**
   * @generated from field: int32 CurrentUsage = 3;
   */
  CurrentUsage: number;
};

/**
 * Describes the message rpc.GetCurrentUsageResp.
 * Use `create(GetCurrentUsageRespSchema)` to create a new message.
 */
export const GetCurrentUsageRespSchema: GenMessage<GetCurrentUsageResp> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 11);

/**
 * @generated from message rpc.UpdateDbUserReq
 */
export type UpdateDbUserReq = Message<"rpc.UpdateDbUserReq"> & {
  /**
   * @generated from field: dbproto.DbUser User = 1;
   */
  User?: DbUser;

  /**
   * Optional: If provided, only update these fields
   *
   * @generated from field: repeated string UpdateFields = 2;
   */
  UpdateFields: string[];
};

/**
 * Describes the message rpc.UpdateDbUserReq.
 * Use `create(UpdateDbUserReqSchema)` to create a new message.
 */
export const UpdateDbUserReqSchema: GenMessage<UpdateDbUserReq> = /*@__PURE__*/
  messageDesc(file_bfmap_rpc, 12);

/**
 * some response code in common means
 *
 * @generated from enum rpc.CommonRespCode
 */
export enum CommonRespCode {
  /**
   * @generated from enum value: Success = 0;
   */
  Success = 0,

  /**
   * @generated from enum value: InvalidParam = 1;
   */
  InvalidParam = 1,

  /**
   * @generated from enum value: InvalidSessionId = 2;
   */
  InvalidSessionId = 2,

  /**
   * @generated from enum value: UnmarshalError = 3;
   */
  UnmarshalError = 3,

  /**
   * @generated from enum value: DataBaseError = 4;
   */
  DataBaseError = 4,

  /**
   * @generated from enum value: ServerError = 5;
   */
  ServerError = 5,

  /**
   * @generated from enum value: PermissionDenied = 6;
   */
  PermissionDenied = 6,

  /**
   * @generated from enum value: NotFound = 7;
   */
  NotFound = 7,

  /**
   * @generated from enum value: AlreadyExist = 8;
   */
  AlreadyExist = 8,

  /**
   * @generated from enum value: Unknown = 9;
   */
  Unknown = 9,
}

/**
 * Describes the enum rpc.CommonRespCode.
 */
export const CommonRespCodeSchema: GenEnum<CommonRespCode> = /*@__PURE__*/
  enumDesc(file_bfmap_rpc, 0);

/**
 * @generated from enum rpc.LoginMethod
 */
export enum LoginMethod {
  /**
   * @generated from enum value: Password = 0;
   */
  Password = 0,

  /**
   * @generated from enum value: SessionId = 1;
   */
  SessionId = 1,
}

/**
 * Describes the enum rpc.LoginMethod.
 */
export const LoginMethodSchema: GenEnum<LoginMethod> = /*@__PURE__*/
  enumDesc(file_bfmap_rpc, 1);

/**
 * @generated from enum rpc.LoginRespCode
 */
export enum LoginRespCode {
  /**
   * @generated from enum value: LoginSuccess = 0;
   */
  LoginSuccess = 0,

  /**
   * @generated from enum value: InvalidLoginParam = 1;
   */
  InvalidLoginParam = 1,

  /**
   * the time in LoginReq is too old, it should be less than 5 minutes from now
   *
   * @generated from enum value: ReqTimeTooOld = 2;
   */
  ReqTimeTooOld = 2,

  /**
   * the time in LoginReq is too new, it should not be more than 1 minute from now
   *
   * @generated from enum value: ReqTimeTooNew = 3;
   */
  ReqTimeTooNew = 3,

  /**
   * @generated from enum value: UserNotExist = 4;
   */
  UserNotExist = 4,

  /**
   * @generated from enum value: PasswordNotMatch = 5;
   */
  PasswordNotMatch = 5,

  /**
   * @generated from enum value: SessionIdNotExist = 6;
   */
  SessionIdNotExist = 6,

  /**
   * @generated from enum value: SessionIdExpired = 7;
   */
  SessionIdExpired = 7,

  /**
   * @generated from enum value: SessionAlreadyLogin = 8;
   */
  SessionAlreadyLogin = 8,

  /**
   * @generated from enum value: FailWithInternalError = 9;
   */
  FailWithInternalError = 9,

  /**
   * @generated from enum value: UserDisabled = 10;
   */
  UserDisabled = 10,
}

/**
 * Describes the enum rpc.LoginRespCode.
 */
export const LoginRespCodeSchema: GenEnum<LoginRespCode> = /*@__PURE__*/
  enumDesc(file_bfmap_rpc, 2);

/**
 * bfmap rpc service
 * need add "Session-Id" in header
 *
 * @generated from service rpc.bfmap
 */
export const bfmap: GenService<{
  /**
   * is server has setup，no session id required
   * Common.Code: 0: setup, !0: not setup
   *
   * @generated from rpc rpc.bfmap.IsSetup
   */
  isSetup: {
    methodKind: "unary";
    input: typeof EmptySchema;
    output: typeof CommonSchema;
  },
  /**
   * Setup to setup the admin account,no session id required
   * Common.Code: 0: success, !0: fail, may refer to Common.Reason, see CommonRespCode
   *
   * @generated from rpc rpc.bfmap.Setup
   */
  setup: {
    methodKind: "unary";
    input: typeof SetupReqSchema;
    output: typeof CommonSchema;
  },
  /**
   * Login to login the server,no session id required
   *
   * @generated from rpc rpc.bfmap.Login
   */
  login: {
    methodKind: "unary";
    input: typeof LoginReqSchema;
    output: typeof LoginRespSchema;
  },
  /**
   * Logout to logout the server, use "Session-Id" in header to identify the user
   * Common.Code: 0: success, !0: fail, may refer to Common.Reason, see CommonRespCode
   *
   * @generated from rpc rpc.bfmap.Logout
   */
  logout: {
    methodKind: "unary";
    input: typeof EmptySchema;
    output: typeof CommonSchema;
  },
  /**
   * Ping to keep the session alive,every hour
   * Common.Code: 0: success, !0: fail, see CommonRespCode
   *
   * @generated from rpc rpc.bfmap.Ping
   */
  ping: {
    methodKind: "unary";
    input: typeof EmptySchema;
    output: typeof CommonSchema;
  },
  /**
   * @generated from rpc rpc.bfmap.CreateUser
   */
  createUser: {
    methodKind: "unary";
    input: typeof CreateUserReqSchema;
    output: typeof CommonSchema;
  },
  /**
   * 1:create
   * 2:update
   * 3:rotate token,create new one and mark old one status as "delete"
   * only for token, new one return in Common.Body
   * 4:delete, mark old one as "delete"
   * Common.Body is DbMapProviderToken
   * in resp,Common.Code: 0: success, !0: fail, see CommonRespCode
   *
   * @generated from rpc rpc.bfmap.SetProviderToken
   */
  setProviderToken: {
    methodKind: "unary";
    input: typeof SetProviderTokenReqSchema;
    output: typeof CommonSchema;
  },
  /**
   * @generated from rpc rpc.bfmap.SetProject
   */
  setProject: {
    methodKind: "unary";
    input: typeof SetProjectReqSchema;
    output: typeof CommonSchema;
  },
  /**
   * Common.Msg is DbProjectToken, code is same as SetProviderToken
   * **ProjectToken Rid and ProjectRid should be in SetProjectReq**
   *
   * @generated from rpc rpc.bfmap.SetProjectToken
   */
  setProjectToken: {
    methodKind: "unary";
    input: typeof SetProjectTokenReqSchema;
    output: typeof CommonSchema;
  },
  /**
   * in resp,Common.Code: 0: success, !0: fail, see CommonRespCode
   * result in Common.Msg is token value
   *
   * @generated from rpc rpc.bfmap.CreateTempMapProjectToken
   */
  createTempMapProjectToken: {
    methodKind: "unary";
    input: typeof EmptySchema;
    output: typeof CommonSchema;
  },
  /**
   * @generated from rpc rpc.bfmap.DeleteMapCacheIndexes
   */
  deleteMapCacheIndexes: {
    methodKind: "unary";
    input: typeof DeleteMapCacheIndexesReqSchema;
    output: typeof CommonSchema;
  },
  /**
   * @generated from rpc rpc.bfmap.GetCurrentUsage
   */
  getCurrentUsage: {
    methodKind: "unary";
    input: typeof GetCurrentUsageReqSchema;
    output: typeof GetCurrentUsageRespSchema;
  },
  /**
   * Update a DbUser
   * Common.Code: 0: success, !0: fail, see CommonRespCode
   *
   * @generated from rpc rpc.bfmap.UpdateDbUser
   */
  updateDbUser: {
    methodKind: "unary";
    input: typeof UpdateDbUserReqSchema;
    output: typeof CommonSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_bfmap_rpc, 0);

