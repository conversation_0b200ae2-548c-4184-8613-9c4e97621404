import type { Message } from "@bufbuild/protobuf"

/**
 * 将继承于Message类型中的字段删除
 * 如将DbOrg继承于Message类型的$typeName、$unknown删除，其他字段保留
 */
export type MessageInitClean<T> = {
  [P in keyof T as T[P] extends Message ? never : P extends keyof Message ? never : P]: T[P]
}

// 数据管理dialog操作类型，添加、编辑
export enum OpenDialogAction {
  // eslint-disable-next-line no-unused-vars
  Add,
  // eslint-disable-next-line no-unused-vars
  Edit,
}
