import sha256 from 'crypto-js/sha256'
import Base64 from 'crypto-js/enc-base64'
import WordArray from 'crypto-js/lib-typedarrays'
import utf8 from 'crypto-js/enc-utf8'
import { successMessage } from '@/utils/notify.ts';
import { i18n } from '@/i18n/i18n.ts';

// import encHex from 'crypto-js/enc-hex'

export function wordArray2Uint8Array(wordArray: WordArray): Uint8Array {
  // Shortcuts
  const words = wordArray.words
  const sigBytes = wordArray.sigBytes

  // Convert
  // var hexChars = []
  const res = new Uint8Array(sigBytes)
  for (let i = 0; i < sigBytes; i++) {
    // hexChars.push((bite >>> 4))
    // hexChars.push((bite & 0x0f))
    res[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff
  }

  return res
}

export function string2Uint8Array(encode: string): Uint8Array {
  return Uint8Array.from(encode, (s) => s.charCodeAt(0))
}

export function uint8Array2String(decode: Uint8Array): string {
  let str = ''
  decode.forEach((el) => (str += String.fromCharCode(el)))
  return str
}

export function toBase64(bytes: Uint8Array): string {
  const word = WordArray.create(bytes)
  return word.toString(Base64)
}

/**
 * 字符串转换成base64
 * @param {string} str 字符串
 * @returns {string} base64编码字符串
 */
export function str2Base64(str: string): string {
  return utf8.parse(str).toString(Base64)
}

/**
 * 将base64转换成字符串
 * @param {string} bs64 base64字符串
 * @returns {string} 字符串
 */
export function base642Str(bs64: string): string {
  return utf8.stringify(Base64.parse(bs64))
}

// 编码用户密码, base64(sha256(Name+Password))
export function encodeUserPassword(name: string, pwd: string): string {
  const word = sha256(name + pwd)
  return word.toString(Base64)
}

/**
 * 编码登录时的密码
 * @param pwd 已经使用用户名和密码编码后的字符串
 * @param time utc时间字符串
 */
// base64(sha256(time_str+base64(sha256(Name+Password))))
export function encodeLoginPassword(name: string, pwd: string, time: string): string {
  const userPwd = encodeUserPassword(name, pwd)
  return encodeUserPassword(time, userPwd)
}

// 复制文本到剪贴板
export async function copyTextToClipboard(text: string): Promise<boolean> {
  if (navigator.clipboard && window.isSecureContext) {
    try {
      await navigator.clipboard.writeText(text)
      successMessage(i18n.global.t('dbMapProviderToken.copyToClipboardSuccess'))
      return true
    } catch {
      return false
    }
  } else {
    return copyTextWithExecCommand(text)
  }
}

function copyTextWithExecCommand(text: string): boolean {
  const textArea = document.createElement('textarea');
  textArea.value = text
  textArea.style.position = 'fixed'
  textArea.style.top = '-9999px'
  textArea.style.left = '-9999px'
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  let success = false;
  try {
    success = document.execCommand('copy')
    if (success) {
      successMessage(i18n.global.t('dbMapProviderToken.copyToClipboardSuccess'))
    }
  } catch (err) {
    console.error('执行 execCommand 时发生错误: ', err)
  }

  document.body.removeChild(textArea)
  return success
}
