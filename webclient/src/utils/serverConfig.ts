let { protocol, hostname, port } = window.location
const { VITE_SERVER_PROTOCOL, VITE_SERVER_HOSTNAME, VITE_SERVER_PORT, DEV } = import.meta.env
if (DEV) {
  protocol = VITE_SERVER_PROTOCOL
  hostname = VITE_SERVER_HOSTNAME
  port = VITE_SERVER_PORT
}

if (port === '') {
  port = protocol === 'https' ? '443' : '80'
}

// 去年协议名最后的冒号
protocol = protocol.endsWith(':') ? protocol.slice(0, -1) : protocol

export const rpcUrl = `${protocol}://${hostname}:${port}`

const wsProtocol = protocol === 'https' ? 'wss' : 'ws'

export const wsUrl = `${wsProtocol}://${hostname}:${port}`
