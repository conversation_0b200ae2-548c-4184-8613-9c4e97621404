import { i18n } from '@/i18n/i18n'

const emailReg = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
const phoneReg = /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/

// 必填
export function required(value: string | null | undefined): boolean | string {
  return !!value || i18n.global.t('validate.required')
}

// 只能输入字母和数字
export function onlyNumLetter(value: string | null | undefined): boolean | string {
  return (!!value && /^[a-zA-Z0-9]+$/.test(value)) || i18n.global.t('validate.onlyNumLetter')
}

// 密码检验
export function passwordAtLeast(value: string | null | undefined, minLength: number = 6): boolean | string {
  return (!!value && value.length >= minLength) || i18n.global.t('validate.passwordAtLeast', { length: minLength })
}

export function validatePhone(value: string | null | undefined): boolean | string {
  if (!value) {
    return true
  }

  if (!phoneReg.test(value.trim())) {
    return i18n.global.t('validate.phoneValidFail')
  }

  return true
}

export function validateEmail(value: string | null | undefined): boolean | string {
  if (!value) {
    return true
  }

  if (!emailReg.test(value.trim())) {
    return i18n.global.t('validate.emailValidFail')
  }

  return true
}

/**
 * 检查密码是否只包含字母、数字和指定的特殊符号
 * @param value 密码字符串
 * @param allowedSpecialChars 允许的特殊符号字符串（如 "!@#$%"）
 */
export function validatePasswordWithAllowedChars(
  value: string,
  allowedSpecialChars: string='!@#$%^&*_-+;:,.',
): boolean | string {
  // 转义特殊字符
  const escaped = allowedSpecialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')
  // 构造正则：只允许字母、数字和指定符号
  const reg = new RegExp(`[^a-zA-Z0-9${escaped}]`)
  if (reg.test(value)) {
    return i18n.global.t('validate.allowedSpecifyChars', { chars: allowedSpecialChars })
  }
  return true
}
