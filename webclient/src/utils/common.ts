import { Dialog, QTable } from 'quasar'
import DialogComponent from '@/components/DialogComponent.vue'
import { i18n } from '@/i18n/i18n.ts'
import { nextTick, ref, toRaw } from 'vue'

export const confirmAgainBatchDelete = () => {
  return new Promise<boolean>((resolve) => {
    Dialog.create({
      component: DialogComponent,
      componentProps: {
        showHeader: false,
        defaultContent: i18n.global.t('dbUser.deleteConfirm'),
        ok: {
          color: 'negative',
          label: i18n.global.t('form.confirm'),
          flat: true,
          autofocus: true,
        },
        cancel: {
          color: 'primary',
          label: i18n.global.t('form.cancel'),
          flat: true
        },
        contentIcon: {
          name: 'warning',
          color: 'negative',
          size: 'sm'
        }
      },
    }).onOk(async () => {
      resolve(true)
    }).onCancel(() => {
      resolve(false)
    })
  })
}

export function getNextName(input: string): string {
  const match = input.match(/(.*?)(\d+)$/)
  if (match) {
    const [, prefix, numStr] = match
    const nextNum = (parseInt(numStr) + 1).toString().padStart(numStr.length, '0')
    return prefix + nextNum
  } else {
    return input + '01'
  }
}

type HasRid = { Rid: string | number }

export function useTableHandleSelection<T extends HasRid>() {
  const tableRef = ref<QTable>()
  const selected = ref<Array<T>>([])
  let storedSelectedRow: T

  function handleSelection({ rows, added, evt }: { rows: readonly T[], readonly added: boolean, evt: Event }) {
    if (rows.length !== 1 || evt === void 0) return

    const oldSelectedRow = storedSelectedRow
    const [newSelectedRow] = rows
    const { shiftKey } = evt as MouseEvent

    if (!shiftKey) {
      storedSelectedRow = newSelectedRow
    }

    nextTick(() => {
      if (shiftKey) {
        const tableRows = tableRef.value!.filteredSortedRows
        let firstIndex = tableRows.indexOf(oldSelectedRow)
        let lastIndex = tableRows.indexOf(newSelectedRow)

        if (firstIndex < 0) {
          firstIndex = 0
        }

        if (firstIndex > lastIndex) {
          [firstIndex, lastIndex] = [lastIndex, firstIndex]
        }

        const rangeRows = tableRows.slice(firstIndex, lastIndex + 1)
        const selectedRows = selected.value.map(toRaw)
        if (added) {
          const rowsSet = new Set(selected.value.concat(rangeRows.filter(row => !selectedRows.includes(row))))
          selected.value = Array.from(rowsSet)
        } else {
          selected.value = selectedRows.filter(row => !rangeRows.find(r => r.Rid === row.Rid))
        }
      }
    })
  }

  return {
    tableRef,
    selected,
    handleSelection,
  }
}

export function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}


export interface Deferred<T> extends Promise<T> {
  /**
   * Resolves the Deferred to a value T
   * @param value
   */
  // eslint-disable-next-line no-unused-vars
  resolve: (value?: T | PromiseLike<T>) => void;
  /**
   * Rejects the Deferred
   * @param reason
   */
  // eslint-disable-next-line no-unused-vars
  reject: (reason?: any) => void;
}

/**
 * Returns a Promise that has a resolve/reject methods that can
 * be used to resolve and defer the Deferred.
 */
export function deferred<T>(): Deferred<T> {
  let methods = {};
  const p = new Promise<T>((resolve, reject): void => {
    methods = { resolve, reject };
  });
  return Object.assign(p, methods) as Deferred<T>;
}

/**
 * 将一个异步可迭代对象的所有值收集到缓存数组中，并返回一个工厂函数。
 * 该工厂函数每次调用都会从缓存创建一个新的异步可迭代对象。
 *
 * @template T 异步可迭代对象产生的值的类型。
 * @param {AsyncIterable<T>} sourceIterable 原始的异步可迭代对象。
 * @returns {Promise<() => AsyncIterable<T>>} 一个 Promise，解析为一个工厂函数。
 *                                            该工厂函数返回一个新的基于缓存的异步可迭代对象。
 * @throws 如果在消费原始迭代器时发生错误，Promise 将会 reject。
 */
export async function cacheAsyncIterable<T>(
  sourceIterable: AsyncIterable<T>
): Promise<() => AsyncIterable<T>> {
  const cache: T[] = []

  try {
    // 完全消费源迭代器并将所有值存入缓存数组
    for await (const item of sourceIterable) {
      cache.push(item)
    }
  } catch (error) {
    // 如果在消费原始流时出错，则向上抛出错误
    console.error("Error occurred while caching the async iterable:", error)
    throw error
  }

  // 返回一个工厂函数
  return function createCachedAsyncIterable(): AsyncIterable<T> {
    // 这个内部的异步生成器函数会从缓存中逐个 yield 值
    return (async function* generatedFromCache() {
      for (const cachedItem of cache) {
        yield cachedItem
      }
    })()
  }
}
