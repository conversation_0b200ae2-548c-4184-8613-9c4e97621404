/// <reference types="vite/client" />
import { type Logger } from 'loglevel'
import type { ComputedRef } from 'vue'

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'vue-router' {
  interface RouteMeta {
    nav?: boolean
    icon?: string
    title?: string | ComputedRef<string>
  }
}

declare global {
  interface Window {
    bfLogger: Logger
    switchLanguage: Function
  }
}
