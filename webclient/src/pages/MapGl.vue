<template>
  <div class="w-full relative map-container">
    <div ref="mapContainer" class="w-full h-full"></div>
    <!-- 地图信息展示 -->
    <div class="map-info">
      <div>{{
          t('mapGL.currentMap')
        }}：{{ `${getMapProviderLabel(currentMapProvider)}(${getMapTypeLabel(currentMapType)})` }}
      </div>
      <div v-if="currentMapProvider === 'google'">
        {{ `${t('mapGL.currentCoordinateType')}：${gcj02 === 1 ? 'gcj02' : 'wgs84'}` }}
      </div>
    </div>
    <!-- 点击清除地图缓存按钮后提示的操作信息 -->
    <div class="clear-map-info" v-if="isSelectionActive">
      {{ $t('mapGL.selectionOperationInfo') }}
    </div>
    <!-- 点击清除地图缓存按钮后提示的操作信息 -->
    <div class="clear-map-info" v-if="isSettingLocate">
      {{ $t('mapGL.selectionOperationInfo') }}
    </div>
    <!-- 地图层级显示 -->
    <div class="zoom-level">{{ $t('mapGL.mapLevel') }}：{{ currentZoom }}</div>
  </div>

  <q-dialog
      v-model="visible"
      persistent
  >
    <q-card class="w-full p-4 pt-2">
      <q-bar class="bg-white !px-4 mt-2">
        <q-icon
            class="clear-map-icon"
            size="24px"
            color="primary"
        />
        <span class="pl-2">{{ $t('mapGL.clearCache') }}</span>
        <q-space></q-space>
        <q-btn
            flat
            round
            dense
            icon="close"
            size="sm"
            v-close-popup
        />
      </q-bar>
      <q-form ref="clearMapForm" class="grid p-2 grid-cols-2 gap-x-4" @submit="clearCache">
        <q-input filled v-model="deleteMapCacheData.MaxLon" :rules="[rules.required]" :label="$t('mapGL.maxLon')"
                 stack-label dense/>
        <q-input filled v-model="deleteMapCacheData.MaxLat" :rules="[rules.required]" :label="$t('mapGL.maxLat')"
                 stack-label dense/>
        <q-input filled v-model="deleteMapCacheData.MinLon" :rules="[rules.required]" :label="$t('mapGL.minLon')"
                 stack-label dense/>
        <q-input filled v-model="deleteMapCacheData.MinLat" :rules="[rules.required]" :label="$t('mapGL.minLat')"
                 stack-label dense/>

        <q-input filled
                 v-model="deleteMapCacheData.Zoom"
                 type="number"
                 min="1" max="18"
                 :rules="[rules.required]"
                 :label="$t('mapGL.mapLevel')"
                 stack-label dense/>
        <q-select
            v-model="deleteMapCacheData.Providers"
            :label="$t('mapGL.mapProvider')"
            filled
            dense
            multiple
            emit-value
            map-options
            options-dense
            :options="providerOptions"
            @update:model-value="providersChange"
        />
        <q-select
            v-model="deleteMapCacheData.MapType"
            :label="$t('mapGL.mapType')"
            filled
            dense
            emit-value
            map-options
            options-dense
            :options="mapTypeOptions"
        />
        <q-input :label="$t('mapGL.clearCacheTime')" dense filled v-model="cacheDate" clearable
                 :hint="$t('mapGL.cacheDateHint')">
          <template v-slot:append>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-date v-model="cacheDate" :mask="DateMask">
                  <div class="row items-center justify-end">
                    <q-btn v-close-popup label="Close" color="primary" flat/>
                  </div>
                </q-date>
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>

        <div class="col-span-2 flex justify-center mt-4">
          <q-btn color="primary" type="submit" class="!w-[200px]">{{ $t('mapGL.clearCache') }}</q-btn>
        </div>
      </q-form>
    </q-card>
  </q-dialog>

  <q-dialog
      v-model="locateVisible"
      persistent
  >
    <q-card class="w-full p-4 pt-2">
      <q-bar class="bg-white !px-4 mt-2">
        <q-icon
            class="set-locate-icon"
            size="24px"
            color="primary"
        />
        <span class="pl-2">{{ $t('mapGL.setLocate') }}</span>
        <q-space></q-space>
        <q-btn
            flat
            round
            dense
            icon="close"
            size="sm"
            v-close-popup
        />
      </q-bar>
      <q-form ref="locateMapForm" class="p-2" @submit="setMapCenter">
        <q-input filled v-model="mapCenterStatusInfo.center[0]" :rules="[rules.required]" :label="$t('mapGL.lon')"
                 stack-label dense clearable/>
        <q-input filled v-model="mapCenterStatusInfo.center[1]" :rules="[rules.required]" :label="$t('mapGL.lat')"
                 stack-label dense clearable/>
        <q-input filled
                 v-model="mapCenterStatusInfo.zoom"
                 type="number"
                 min="1" max="18"
                 :rules="[rules.required]"
                 :label="$t('mapGL.mapLevel')"
                 stack-label dense/>

        <div class="col-span-2 flex justify-center mt-2">
          <q-btn color="primary" type="submit" class="!w-[200px]">{{ $t('common.confirm') }}</q-btn>
        </div>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed, watch, type WatchHandle, onUnmounted, effect } from 'vue'
  import type {
    Map as MapLibreMap,
    MapMouseEvent,
    ErrorEvent,
    RequestParameters,
    GetResourceResponse
  } from 'maplibre-gl'
  import maplibregl from 'maplibre-gl'
  import type { LngLat, StyleSpecification } from 'maplibre-gl'
  import 'maplibre-gl/dist/maplibre-gl.css'
  import { useI18n } from 'vue-i18n'
  import { DeleteMapCacheIndexesReqSchema } from '@/proto/bfmap.rpc_pb.ts'
  import { create } from '@bufbuild/protobuf'
  import { MapProviderEnum } from '@/proto/db_pb.ts'
  import { bigIntUnixTimeToString, DateMask, getTimestampSixMonthsAgo, } from '@/utils/dayjs.ts'
  import dayjs from 'dayjs'
  import { type QForm, useQuasar } from 'quasar'
  import * as validation from '@/utils/validation.ts'
  import { clearMapCache, createTempMapToken } from '@/services/connectRpc.ts'
  import { rpcUrl } from '@/utils/serverConfig.ts'
  import { successMessage } from '@/utils/notify.ts'
  import {
    type MapCenterInfo,
    type MapProvider,
    type MapType,
    useLoginStatusStore,
    useMapStatusStore
  } from '@/stores/session.ts'
  import logger from '@/utils/logger.ts'
  import { AdminUserRid } from '@/stores/common.ts'
  import type { ReactiveEffectRunner } from 'vue'

  const { t, locale, messages } = useI18n()
  const $q = useQuasar()
  const mapContainer = ref<HTMLElement | null>(null)
  let map: MapLibreMap | null = null
  const clearMapForm = ref<QForm>()
  const locateMapForm = ref<QForm>()
  const visible = ref(false)
  const locateVisible = ref(false)
  const loginStatusStore = useLoginStatusStore()

  const { mapStatus, setMapStatus, initMapStatus, defaultMapCenterStatus, setDefaultMapCenter } = useMapStatusStore()
  const currentZoom = ref<number>(mapStatus.zoom)
  const deleteMapCacheData = ref(create(DeleteMapCacheIndexesReqSchema, {
    MapType: 1,
    Providers: [MapProviderEnum.ProviderTianditu],
    Zoom: currentZoom.value,
    MaxLat: 0,
    MaxLon: 0,
    MinLat: 0,
    MinLon: 0,
    CacheTime: getTimestampSixMonthsAgo()
  }))
  const mapCenterStatusInfo = ref<MapCenterInfo>(defaultMapCenterStatus)

  // 框选相关状态变量
  const isSelectionActive = ref(false)
  let selectionBox: maplibregl.Marker | null = null
  let selectionStart: LngLat | null = null
  let selectionElement: HTMLDivElement | null = null
  let confirmButton: HTMLButtonElement | null = null

  // ignore enum no-unused-vars)
  /* eslint-disable */
  enum MapTypeEnum {
    roadmap = 1,
    satellite,
    hybrid
  }

  enum EnumMapProvider {
    google = 'ProviderGoogle',
    tianditu = 'ProviderTianditu',
    osm = 'ProviderOSM'
  }

  /* eslint-enable */
  const currentMapProvider = ref<MapProvider>(mapStatus.mapProvider)
  const currentMapType = ref<MapType>(mapStatus.mapType)
  const tempToken = ref('')
  const gcj02 = ref(0)

  initMapStatus().then(() => {
    currentMapProvider.value = mapStatus.mapProvider
    currentMapType.value = mapStatus.mapType
    currentZoom.value = mapStatus.zoom
  })

  const BASE_URL = `${rpcUrl}/map`

  const urlLang = computed(() => {
    return locale.value.startsWith('zh') ? 'zh-CN' : 'en'
  })

  watch(locale, () => {
    // 切换语言后，只有google才需要重新请求地图，只有google地图才有不同语言的瓦片
    if (currentMapProvider.value === 'google' && map) {
      const style = getStyle(new Date().getTime())
      map.setStyle(style)
    }
  })

  const i18nLoadOk = computed(() => {
    return messages.value[locale.value] !== undefined
  })

  const requestUrl = computed(() => {
    let url = `${BASE_URL}?token=${tempToken.value}&mtype=${currentMapType.value}&provider=${currentMapProvider.value}&lang=${urlLang.value}&x={x}&y={y}&z={z}`
    if (currentMapProvider.value === 'google') {
      url += `&gcj02=${gcj02.value}`
    }
    return url
  })

  const mapStyle = ref<Record<string, maplibregl.StyleSpecification | {}>>({
    // google map or tianditu or osm， osm only has street maps
    street: {},
    // google map or tianditu
    satellite: {},
    // google map or tianditu
    hybrid: {},
  })

  const providerOptions = computed(() => {
    return [
      {
        label: t('dbProjectToken.googleMap'),
        value: MapProviderEnum.ProviderGoogle
      },
      {
        label: t('dbProjectToken.tianditu'),
        value: MapProviderEnum.ProviderTianditu
      },
      {
        label: t('dbProjectToken.openStreetMap'),
        value: MapProviderEnum.ProviderOSM
      },
    ]
  })

  const mapTypeOptions = computed(() => {
    return [
      {
        label: t('dbProjectToken.defaultRoadmap'),
        value: 1
      },
      {
        label: t('dbProjectToken.defaultSatellite'),
        value: 2
      },
      {
        label: t('dbProjectToken.defaultHybrid'),
        value: 3
      },
    ]
  })

  const cacheDate = computed({
    get: () => {
      if (deleteMapCacheData.value.CacheTime === BigInt(0)) {
        return ''
      }
      return bigIntUnixTimeToString(deleteMapCacheData.value.CacheTime, DateMask)
    },
    set: (value) => {
      if (!value) {
        deleteMapCacheData.value.CacheTime = BigInt(0)
        return
      }
      // 使用 inputFormat 精确解析日期字符串
      const dayjsObj = dayjs(value, DateMask, true) // 第三个参数 true 表示严格模式解析

      if (!dayjsObj.isValid()) {
        throw new Error(`无效的日期字符串 "${value}" 或格式 "${DateMask}" 不匹配。`)
      }

      // dayjsObj.unix() 返回 number 类型的秒数
      const timestampSecondsNumber = dayjsObj.unix()
      // 转换为 bigint
      deleteMapCacheData.value.CacheTime = BigInt(timestampSecondsNumber)
    }
  })

  const rules = computed(() => {
    return {
      required: (val: string | null | undefined) => validation.required(val),
    }
  })

  const providersChange = (val: Array<number>) => {
    if (val.length === 0) {
      deleteMapCacheData.value.Providers = [MapProviderEnum[EnumMapProvider[currentMapProvider.value]]]
    }
  }

  function getMapProviderLabel(providerKey: MapProvider) {
    switch (providerKey) {
      case 'google':
        return t('dashboard.googleMap')
      case 'tianditu':
        return t('dashboard.tiandituMap')
      case 'osm':
        return 'OpenStreetMap'
      default:
        return ''
    }
  }

  function getMapTypeLabel(providerKey: MapType) {
    switch (providerKey) {
      case 'roadmap':
        return t('dashboard.streetMap')
      case 'satellite':
        return t('dashboard.satelliteMap') + `(${t('mapGL.onlyBaseMap')})`
      case 'hybrid':
        return t('dashboard.satelliteMap') + `(${t('mapGL.withAnnotation')})`
      default:
        return ''
    }
  }

  // 地图提供商切换控件
  class MapProviderStyleControl implements maplibregl.IControl {
    private _map: MapLibreMap | undefined
    private _container: HTMLDivElement
    private _buttons: { [key: string]: HTMLButtonElement } = {}
    private _handleEffect: ReactiveEffectRunner  | null = null

    constructor() {
      this._container = document.createElement('div')
      this._container.className = 'maplibregl-ctrl maplibregl-ctrl-group'

      // 谷歌地图按钮
      const googleButton = document.createElement('button')
      googleButton.type = 'button'
      googleButton.className = 'google-icon'
      googleButton.onclick = () => this._changeMapStyle('google')
      this._buttons['google'] = googleButton

      // 天地图按钮
      const tianButton = document.createElement('button')
      tianButton.type = 'button'
      tianButton.className = 'tianditu-icon'
      tianButton.onclick = () => this._changeMapStyle('tianditu')
      this._buttons['tianditu'] = tianButton

      // OSM
      const osmButton = document.createElement('button')
      osmButton.type = 'button'
      osmButton.className = 'osm-icon'
      osmButton.onclick = () => this._changeMapStyle('osm')
      this._buttons['osm'] = osmButton

      this._buttons[currentMapProvider.value].className += ' highlight-btn'

      // 添加按钮到容器
      this._container.appendChild(googleButton)
      this._container.appendChild(tianButton)
      this._container.appendChild(osmButton)

      // 初始化按钮标题
      this._updateButtonTitles()

      this._handleEffect = effect(() =>{
        if (i18nLoadOk.value) {
          this._updateButtonTitles()
        }
      })
    }

    onAdd(map: MapLibreMap): HTMLElement {
      this._map = map
      return this._container
    }

    onRemove(): void {
      this._container.parentNode?.removeChild(this._container)
      this._map = undefined
      this._handleEffect?.effect.stop()
      this._handleEffect = null
    }

    // 更新按钮标题为当前语言
    async _updateButtonTitles() {
      this._buttons['google'].title = t('dashboard.googleMap')
      this._buttons['tianditu'].title = t('dashboard.tiandituMap')
      this._buttons['osm'].title = 'OSM'
    }

    _changeBtnHighLight(key: string): void {
      for (const btnKey in this._buttons) {
        if (btnKey === key) {
          this._buttons[btnKey].className += ' highlight-btn'
        } else {
          this._buttons[btnKey].className = this._buttons[btnKey].className.replace(' highlight-btn', '')
        }
      }
    }

    _changeMapStyle(styleKey: MapProvider): void {
      if (!this._map) return

      if (currentMapType.value !== 'roadmap' && styleKey === 'osm') {
        $q.notify({
          message: t('mapGL.osmOnlySupportRoadMap'),
          position: 'top',
          timeout: 1000
        })
        return
      }

      // 保存当前地图状态
      const center = this._map.getCenter()
      const zoom = this._map.getZoom()
      const pitch = this._map.getPitch()
      const bearing = this._map.getBearing()

      currentMapProvider.value = styleKey

      mapStatus.mapProvider = styleKey

      const style = getStyle()

      // 设置新样式
      this._map.setStyle(style)

      this._changeBtnHighLight(styleKey)
      // 重新加载完成后恢复视图状态
      this._map.once('styledata', () => {
        if (!this._map) return
        this._map.setCenter(center)
        this._map.setZoom(zoom)
        this._map.setPitch(pitch)
        this._map.setBearing(bearing)
        setMapStatus({ ...mapStatus })
      })
    }
  }

  // 地图样式切换控件
  class MapStyleControl implements maplibregl.IControl {
    private _map: MapLibreMap | undefined
    private _container: HTMLDivElement
    private _buttons: { [key: string]: HTMLButtonElement } = {}
    private _handleEffect: ReactiveEffectRunner | null = null

    constructor() {
      this._container = document.createElement('div')
      this._container.className = 'maplibregl-ctrl maplibregl-ctrl-group'

      // 街道图按钮
      const streetButton = document.createElement('button')
      streetButton.type = 'button'
      streetButton.className = 'roadmap-icon'
      streetButton.onclick = () => this._changeMapStyle('roadmap')
      this._buttons['roadmap'] = streetButton

      // 卫星图按钮
      const satelliteButton = document.createElement('button')
      satelliteButton.type = 'button'
      satelliteButton.className = 'satellite-icon'
      satelliteButton.onclick = () => this._changeMapStyle('satellite')
      this._buttons['satellite'] = satelliteButton

      // 混合图按钮
      const hybridButton = document.createElement('button')
      hybridButton.type = 'button'
      hybridButton.className = 'hybrid-icon'
      hybridButton.onclick = () => this._changeMapStyle('hybrid')
      this._buttons['hybrid'] = hybridButton

      // 添加按钮到容器
      this._container.appendChild(streetButton)
      this._container.appendChild(satelliteButton)
      this._container.appendChild(hybridButton)

      this._buttons[currentMapType.value].className += ' highlight-btn'
      // 初始化按钮标题
      this._updateButtonTitles()

      this._handleEffect = effect(() =>{
        if (i18nLoadOk.value) {
          this._updateButtonTitles()
        }
      })
    }

    onAdd(map: MapLibreMap): HTMLElement {
      this._map = map
      return this._container
    }

    onRemove(): void {
      this._container.parentNode?.removeChild(this._container)
      this._map = undefined
      this._handleEffect?.effect.stop()
      this._handleEffect = null
    }

    // 更新按钮标题为当前语言
    async _updateButtonTitles() {
      this._buttons['roadmap'].title = t('dashboard.streetMap')
      this._buttons['satellite'].title = t('dashboard.satelliteMap') + `(${t('mapGL.onlyBaseMap')})`
      this._buttons['hybrid'].title = t('dashboard.satelliteMap') + `(${t('mapGL.withAnnotation')})`
    }

    _changeBtnHighLight(key: string): void {
      for (const btnKey in this._buttons) {
        if (btnKey === key) {
          this._buttons[btnKey].className += ' highlight-btn'
        } else {
          this._buttons[btnKey].className = this._buttons[btnKey].className.replace(' highlight-btn', '')
        }
      }
    }

    _changeMapStyle(styleKey: MapType): void {
      if (!this._map) return

      if (currentMapProvider.value === 'osm' && styleKey !== 'roadmap') {
        $q.notify({
          message: t('mapGL.osmOnlySupportRoadMap'),
          position: 'top',
          timeout: 1000
        })
        return
      }

      // 保存当前地图状态
      const center = this._map.getCenter()
      const zoom = this._map.getZoom()
      const pitch = this._map.getPitch()
      const bearing = this._map.getBearing()
      mapStatus.mapType = styleKey

      currentMapType.value = styleKey

      const style = getStyle()
      // 设置新样式
      this._map.setStyle(style)

      this._changeBtnHighLight(styleKey)
      // 重新加载完成后恢复视图状态
      this._map.once('styledata', () => {
        if (!this._map) return
        this._map.setCenter(center)
        this._map.setZoom(zoom)
        this._map.setPitch(pitch)
        this._map.setBearing(bearing)
        setMapStatus({ ...mapStatus })
      })
    }
  }

  // 删除地图缓存按钮控件
  class OperateMapControl implements maplibregl.IControl {
    // private _map: MapLibreMap | undefined
    private _container: HTMLDivElement
    private _buttons: { [key: string]: HTMLButtonElement } = {}
    private _handleEffect: ReactiveEffectRunner | null = null
    private _providerWatch: WatchHandle | null = null

    constructor() {
      this._container = document.createElement('div')
      this._container.className = 'maplibregl-ctrl maplibregl-ctrl-group'

      const clearButton = document.createElement('button')
      clearButton.type = 'button'
      clearButton.className = 'clear-map-icon'
      clearButton.onclick = () => this._clickClearBtn()
      this._buttons['clear'] = clearButton

      const refreshButton = document.createElement('button')
      refreshButton.type = 'button'
      refreshButton.className = 'refresh-map-icon'
      refreshButton.onclick = () => this._clickRefreshBtn()
      this._buttons['refresh'] = refreshButton

      const switchButton = document.createElement('button')
      switchButton.type = 'button'
      switchButton.className = 'check-gcj02-icon'
      switchButton.onclick = () => this._checkGcj02()
      this._buttons['switch'] = switchButton

      this._container.appendChild(refreshButton)
      if (currentMapProvider.value === 'google') {
        this._container.appendChild(switchButton)
      }
      // 只有admin才可以清楚地图缓存
      if (loginStatusStore.userRid === AdminUserRid) {
        this._container.appendChild(clearButton)
      }

      this._updateButtonTitles()

      this._handleEffect = effect(() =>{
        if (i18nLoadOk.value) {
          this._updateButtonTitles()
        }
      })

      this._providerWatch = watch(currentMapProvider, (newVal) => {
        if (newVal === 'google') {
          this._container.insertBefore(this._buttons['switch'], this._buttons['refresh'])
        } else {
          if (this._container.contains(this._buttons['switch'])) {
            this._container.removeChild(this._buttons['switch'])
          }
        }
      })
    }

    _clickClearBtn(): void {
      // 正在框选
      if (isSelectionActive.value) {
        return
      }
      toggleSelection()
    }

    // 更新按钮标题为当前语言
    async _updateButtonTitles() {
      this._buttons['clear'].title = t('mapGL.clearCache')
      this._buttons['refresh'].title = t('mapGL.refreshMap')
      this._buttons['switch'].title = t('mapGL.checkGcj02')
    }

    _clickRefreshBtn(): void {
      if (!map) return

      const style = getStyle(new Date().getTime())
      map.setStyle(style)
    }


    _checkGcj02(): void {
      if (!map) return
      gcj02.value = gcj02.value === 0 ? 1 : 0

      const style = getStyle()
      map.setStyle(style)
    }

    onAdd(): HTMLElement {
      // this._map = map
      return this._container
    }

    onRemove(): void {
      this._container.parentNode?.removeChild(this._container)
      this._handleEffect?.effect.stop()
      this._handleEffect = null
      this._providerWatch?.stop()
      this._providerWatch = null
      // this._map = undefined
    }
  }

  const isSettingLocate = ref(false)
  // 地图定位相关控件
  class MapLocateControl implements maplibregl.IControl {
    private _container: HTMLDivElement
    private _buttons: { [key: string]: HTMLButtonElement } = {}
    private _handleEffect: ReactiveEffectRunner | null = null
    // eslint-disable-next-line no-unused-vars
    private _clickHandler: ((e: MapMouseEvent) => void) | null = null

    constructor() {
      this._container = document.createElement('div')
      this._container.className = 'maplibregl-ctrl maplibregl-ctrl-group'

      const locateBUtton = document.createElement('button')
      locateBUtton.type = 'button'
      locateBUtton.className = 'locate-icon'
      locateBUtton.onclick = () => this._locate()
      this._buttons['locate'] = locateBUtton

      const setDefaultLocateBtn = document.createElement('button')
      setDefaultLocateBtn.type = 'button'
      setDefaultLocateBtn.className = 'set-locate-icon'
      setDefaultLocateBtn.onclick = () => this._setLocate()
      this._buttons['setLocate'] = setDefaultLocateBtn

      this._container.appendChild(setDefaultLocateBtn)
      this._container.appendChild(locateBUtton)

      this._updateButtonTitles()

      this._handleEffect = effect(() =>{
        if (i18nLoadOk.value) {
          this._updateButtonTitles()
        }
      })
    }

    // 更新按钮标题为当前语言
    async _updateButtonTitles() {
      this._buttons['setLocate'].title = t('mapGL.setLocate')
      this._buttons['locate'].title = t('mapGL.jumpDefaultLocate')
    }

    onAdd(): HTMLElement {
      return this._container
    }

    onRemove(): void {
      this._container.parentNode?.removeChild(this._container)
      this._handleEffect?.effect.stop()
      this._handleEffect = null
      // 确保清理设置定位状态
      this._cleanupLocateSetting()
    }

    _locate(): void {
      if (map) {
        map.flyTo({
          center: mapCenterStatusInfo.value.center,
          zoom: mapCenterStatusInfo.value.zoom,
        })
      }
    }

    _setLocate(): void {
      if (!map) return
      
      isSettingLocate.value = true
      if (map.getCanvas()) {
        map.getCanvas().style.cursor = 'crosshair'
      }

      // 添加点击事件监听器
      this._clickHandler = (e: MapMouseEvent) => {
        if (!isSettingLocate.value) return
        
        // 获取点击位置的经纬度
        const lngLat = e.lngLat
        mapCenterStatusInfo.value.center = [lngLat.lng, lngLat.lat]
        mapCenterStatusInfo.value.zoom = roundByHalf(currentZoom.value)
        
        // 打开对话框
        locateVisible.value = true
        
        // 清理事件监听器和状态
        this._cleanupLocateSetting()
      }

      map.on('click', this._clickHandler)

      // 添加 ESC 键监听
      const escHandler = (event: KeyboardEvent) => {
        if (event.key === 'Escape' && isSettingLocate.value) {
          this._cleanupLocateSetting()
        }
      }
      window.addEventListener('keydown', escHandler)
    }

    private _cleanupLocateSetting(): void {
      if (!map) return

      // 移除点击事件监听器
      if (this._clickHandler) {
        map.off('click', this._clickHandler)
        this._clickHandler = null
      }

      // 恢复默认光标
      if (map.getCanvas()) {
        map.getCanvas().style.cursor = ''
      }

      // 重置状态
      isSettingLocate.value = false
    }
  }

  async function setMapCenter() {
    const valid = await locateMapForm.value?.validate()
    if (!valid) {
      return
    }
    // 将响应式对象转换为普通对象
    const centerInfo: MapCenterInfo = {
      center: [...mapCenterStatusInfo.value.center],
      zoom: mapCenterStatusInfo.value.zoom
    }
    setDefaultMapCenter(centerInfo)
    locateVisible.value = false
    successMessage(t('common.operationSuccess'))
  }

  function roundByHalf(num: number) {
    const integerPart = Math.floor(num)
    const decimalPart = num - integerPart

    if (decimalPart >= 0.5) {
      return integerPart + 1
    } else {
      return integerPart
    }
  }

  // 切换框选模式
  function toggleSelection() {
    isSelectionActive.value = !isSelectionActive.value

    if (!isSelectionActive.value) {
      deleteMapCacheData.value.MapType = MapTypeEnum[currentMapType.value]
      deleteMapCacheData.value.Providers = [MapProviderEnum[EnumMapProvider[currentMapProvider.value]]]
      deleteMapCacheData.value.Zoom = roundByHalf(currentZoom.value)
      visible.value = true
      // 清除框选区域
      clearSelection()
      // 恢复默认光标
      if (map && map.getCanvas()) {
        map.getCanvas().style.cursor = ''
      }
    } else {
      // 设置为十字线光标
      if (map && map.getCanvas()) {
        map.getCanvas().style.cursor = 'crosshair'
      }
    }
  }

  function getCenter() {
    const center = map?.getCenter()

    return [center?.lng ?? 0, center?.lat ?? 0]
  }

  // 清除框选区域
  function clearSelection() {
    if (selectionBox) {
      selectionBox.remove()
      selectionBox = null
    }

    selectionStart = null

    if (selectionElement) {
      selectionElement.remove()
      selectionElement = null
    }

    if (confirmButton) {
      confirmButton.remove()
      confirmButton = null
    }
  }

  // 初始化框选功能
  function initSelectionFeature() {
    if (!map) return

    // 鼠标按下事件 - 开始框选
    map.on('mousedown', (e: MapMouseEvent) => {
      if (!isSelectionActive.value || !map) return

      e.preventDefault()

      // 清除之前的框选
      clearSelection()

      // 记录起始点
      selectionStart = e.lngLat

      // 创建框选元素
      selectionElement = document.createElement('div')
      selectionElement.className = 'selection-box'
      selectionElement.style.position = 'absolute'
      selectionElement.style.border = '2px solid #0078ff'
      selectionElement.style.backgroundColor = 'rgba(0, 120, 255, 0.1)'
      selectionElement.style.pointerEvents = 'none'

      // 添加到地图容器
      map.getContainer().appendChild(selectionElement)

      // 记录鼠标按下的屏幕坐标
      const startPoint = map.project(e.lngLat)
      selectionElement.style.left = `${startPoint.x}px`
      selectionElement.style.top = `${startPoint.y}px`
      selectionElement.style.width = '0px'
      selectionElement.style.height = '0px'

      // 添加鼠标移动事件
      map.on('mousemove', onMouseMove)

      // 添加鼠标释放事件
      map.once('mouseup', onMouseUp)
    })

    // 当光标离开地图区域时，如果没有进行框选操作，恢复默认光标
    map.on('mouseout', () => {
      if (isSelectionActive.value && !selectionStart && map && map.getCanvas()) {
        map.getCanvas().style.cursor = ''
      }
    })

    // 当光标重新进入地图区域时，如果框选模式激活，设置为十字线光标
    map.on('mouseover', () => {
      if (isSelectionActive.value && map && map.getCanvas()) {
        map.getCanvas().style.cursor = 'crosshair'
      }
    })

    // 鼠标移动事件 - 更新框选区域
    function onMouseMove(e: MapMouseEvent) {
      if (!selectionStart || !selectionElement || !map) return

      e.preventDefault()

      const currentPoint = map.project(e.lngLat)
      const startPoint = map.project(selectionStart)

      // 计算框选区域的位置和大小
      const minX = Math.min(startPoint.x, currentPoint.x)
      const minY = Math.min(startPoint.y, currentPoint.y)
      const width = Math.abs(startPoint.x - currentPoint.x)
      const height = Math.abs(startPoint.y - currentPoint.y)

      // 更新框选元素样式
      selectionElement.style.left = `${minX}px`
      selectionElement.style.top = `${minY}px`
      selectionElement.style.width = `${width}px`
      selectionElement.style.height = `${height}px`
    }

    // 鼠标释放事件 - 完成框选
    function onMouseUp(e: MapMouseEvent) {
      if (!selectionStart || !selectionElement || !map) return

      // 移除鼠标移动事件监听
      map.off('mousemove', onMouseMove)

      // 获取框选区域的地理坐标
      const endPoint = e.lngLat

      // 计算框选区域的边界坐标
      const topLeft = new maplibregl.LngLat(
          Math.min(selectionStart.lng, endPoint.lng),
          Math.max(selectionStart.lat, endPoint.lat)
      )

      const bottomRight = new maplibregl.LngLat(
          Math.max(selectionStart.lng, endPoint.lng),
          Math.min(selectionStart.lat, endPoint.lat)
      )

      // 添加确认按钮
      const currentPoint = map.project(bottomRight)
      confirmButton = document.createElement('button')
      confirmButton.className = 'selection-confirm-button'
      confirmButton.innerHTML = t('common.confirm')
      confirmButton.style.position = 'absolute'
      confirmButton.style.left = `${currentPoint.x - 44}px`
      confirmButton.style.top = `${currentPoint.y - 30}px`
      confirmButton.style.backgroundColor = '#4CAF50'
      confirmButton.style.color = 'white'
      confirmButton.style.padding = '5px 10px'
      confirmButton.style.border = 'none'
      confirmButton.style.borderRadius = '4px'
      confirmButton.style.cursor = 'pointer'
      confirmButton.style.zIndex = '1000'

      confirmButton.onclick = () => {
        // 更新坐标显示
        deleteMapCacheData.value.MaxLon = bottomRight.lng
        deleteMapCacheData.value.MaxLat = topLeft.lat
        deleteMapCacheData.value.MinLon = topLeft.lng
        deleteMapCacheData.value.MinLat = bottomRight.lat
        toggleSelection()
      }

      map.getContainer().appendChild(confirmButton)
    }
  }

  // 地图层级变化处理函数
  function handleZoomChange() {
    if (!map) return
    currentZoom.value = Number(map.getZoom().toFixed(2))
    mapStatus.zoom = currentZoom.value
    setMapStatus({ ...mapStatus })
  }

  // 地图移动
  function handleMapMoveEnd() {
    if (!map) return
    mapStatus.center = getCenter() as [number, number]
    setMapStatus({ ...mapStatus })
  }

  async function clearCache() {
    const valid = await clearMapForm.value?.validate()
    if (!valid) {
      return
    }
    const dialog = $q.dialog({
      message: t('common.operationProgress', { progress: 0 }),
      progress: true,
      persistent: true,
      ok: false,
      class: 'flex flex-col items-center justify-center'
    })
    const startTime = Date.now()
    const res = await clearMapCache(deleteMapCacheData.value)
    if (res.Code === 0) {
      successMessage(t('mapGL.clearCacheSuccess'))
      visible.value = false
      if (map) {
        // 设置新样式
        const style = getStyle(new Date().getTime())
        map.setStyle(style)
        if (Date.now() - startTime < 800) {
          await new Promise(resolve => setTimeout(resolve, 600 - (Date.now() - startTime)))
        }
        dialog.hide()
      }
    }
  }

  function getStyle(timeStamp?: number) {
    const mapTypeName = `${currentMapProvider.value}_${currentMapType.value}`
    const tileSize = 256

    // 使用自定义协议包装URL
    let tileUrl = `getMapTile://${requestUrl.value.replace('{x}', '{x}').replace('{y}', '{y}').replace('{z}', '{z}')}`

    if (timeStamp) {
      tileUrl += `&t=${timeStamp}`
    }

    const style: StyleSpecification = {
      version: 8,
      // glyphs: glyphs,
      // sprite: sprite,
      name: mapTypeName,
      sources: {
        [mapTypeName]: {
          type: 'raster',
          tiles: [tileUrl],
          tileSize,
          minzoom: 1,
          maxzoom: 18,
        },
      },
      layers: [
        {
          id: mapTypeName,
          type: 'raster',
          source: mapTypeName,
        },
      ],
    }
    return style
  }

  let tokenExpireTimer: any = null

  async function setMapStyle() {
    const res = await createTempMapToken()
    tempToken.value = res.Msg
    // 清除已有定时器
    if (tokenExpireTimer) {
      clearTimeout(tokenExpireTimer)
    }

    // 9分钟后自动刷新 token（给自己留点 buffer）
    tokenExpireTimer = setTimeout(() => {
      setMapStyle()
    }, 9 * 60 * 1000)
    const styleKey = currentMapType.value === 'roadmap' ? 'street' : currentMapType.value
    mapStyle.value[styleKey] = getStyle()
  }

  function getMapStyle() {
    const styleKey = currentMapType.value === 'roadmap' ? 'street' : currentMapType.value
    return mapStyle.value[styleKey]
  }

  // 全局canvas 优先使用OffsetScreenCanvas
  const hasOffscreenCanvas = typeof OffscreenCanvas !== 'undefined'

  function createCanvas(): OffscreenCanvas | HTMLCanvasElement {
    if (hasOffscreenCanvas) {
      return new OffscreenCanvas(256, 256)
    } else {
      const canvas = document.createElement('canvas')
      canvas.width = 256
      canvas.height = 256
      return canvas
    }
  }

  const canvas = createCanvas()
  const ctx = canvas.getContext('2d')

  async function createNoCacheCanvas(x: string, y: string, z: string): Promise<Uint8Array | null> {
    if (!ctx) {
      return null
    }

    // 清空并绘制背景
    ctx.clearRect(0, 0, 256, 256)
    ctx.fillStyle = 'rgba(220, 220, 220, 0.7)'
    ctx.fillRect(0, 0, 256, 256)

    ctx.fillStyle = '#708090'
    ctx.font = '16px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(`x: ${x}, y: ${y}, z: ${z}`, 128, 100)

    // 添加文本
    ctx.fillStyle = '#708090'
    ctx.font = '16px Arial'
    ctx.textAlign = 'center'
    i18nLoadOk.value && ctx.fillText(t('mapGL.noCache'), 128, 140)

    // 绘制边框
    ctx.strokeStyle = '#708090'
    ctx.lineWidth = 1
    ctx.strokeRect(0, 0, 256, 256)

    try {
      if (hasOffscreenCanvas && canvas instanceof OffscreenCanvas) {
        const blob = await canvas.convertToBlob()
        const buffer = await blob.arrayBuffer()
        return new Uint8Array(buffer)
      } else if (canvas instanceof HTMLCanvasElement) {
        const blob = await new Promise<Blob>((resolve, reject) => {
          canvas.toBlob((blob) => {
            if (blob) resolve(blob)
            else reject(new Error('HTMLCanvasElement.toBlob 失败'))
          })
        })
        const buffer = await blob.arrayBuffer()
        return new Uint8Array(buffer)
      } else {
        return null
      }
    } catch (err) {
      logger.error('createNoCacheCanvas: ', err)
      return null
    }
  }

  // 注册自定义协议处理器，处理图片请求失败的情况
  function registerCustomProtocol() {
    maplibregl.addProtocol('getMapTile', (params: RequestParameters, abortController: AbortController) => {
      return new Promise<GetResourceResponse<Uint8Array>>((resolve, reject) => {
        const url = params.url.replace('getMapTile://', '')
        fetch(url, { signal: abortController.signal })
            .then(response => {
              if (!response.ok) {
                throw new Error(`请求失败: ${response.status}`)
              }
              const cacheTime = response.headers.get('cache-time')
              return Promise.all([response.blob(), cacheTime])
            })
            .then(res => {
              const data = res[0]
              const time = res[1]
              createImageBitmap(data)
                  .then(imageBitmap => {
                    if (!ctx) {
                      reject(new Error('无法创建canvas上下文'))
                      return
                    }
                    // Draw the original tile image
                    ctx.drawImage(imageBitmap, 0, 0, 256, 256)

                    // 绘制边界
                    if (currentMapType.value === 'roadmap') {
                      ctx.strokeStyle = '#708090'
                    } else {
                      ctx.strokeStyle = '#F2D09D'
                    }
                    ctx.lineWidth = 1
                    ctx.strokeRect(0, 0, canvas.width, canvas.height)

                    if (time) {
                      const date = bigIntUnixTimeToString(BigInt(time), DateMask)
                      const [year, month, day] = date.split('-')
                      const formatDate = `${year.slice(-2)}.${parseInt(month)}.${parseInt(day)}`
                      ctx.font = '16px Arial'
                      ctx.textAlign = 'center'
                      ctx.textBaseline = 'middle'

                      // 设置描边样式
                      ctx.lineWidth = 3
                      ctx.strokeStyle = 'white' // 描边颜色
                      ctx.strokeText(formatDate, 128, 130)

                      // 设置填充样式
                      ctx.fillStyle = 'red'
                      ctx.fillText(formatDate, 128, 130)
                    }
                    imageBitmap.close()

                    if (hasOffscreenCanvas) {
                      (canvas as OffscreenCanvas).convertToBlob()
                          .then(blob => {
                            if (!blob) {
                              reject(new Error('无法创建Blob对象'))
                              return
                            }
                            blob.arrayBuffer().then(a => resolve({ data: new Uint8Array(a) }))
                          })
                    } else {
                      (canvas as HTMLCanvasElement).toBlob((blob) => {
                        if (!blob) {
                          reject(new Error('无法创建Blob对象'))
                          return
                        }
                        blob.arrayBuffer().then(a => resolve({ data: new Uint8Array(a) }))
                      })
                    }
                  })
                  .catch(error => {
                    logger.error('createImageBitmap error:', error)
                    // getErrorPlaceholderImage(callback)
                  })
            })
            .catch(() => {
              const params = new URLSearchParams(new URL(url).search)
              createNoCacheCanvas(params.get('x')!, params.get('y')!, params.get('z')!)
                  .then(noCacheData => {
                    resolve({
                      data: noCacheData ?? new Uint8Array(0),
                    })
                  })
            })
      })
    })
  }

  // 监听ESC键按下事件
  function handleEscKey(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      if (!isSelectionActive.value) {
        return
      }
      isSelectionActive.value = false
      // 清除框选区域
      clearSelection()
      // 恢复默认光标
      if (map && map.getCanvas()) {
        map.getCanvas().style.cursor = ''
      }
    }
  }

  onMounted(async () => {
    if (!mapContainer.value) return
    await setMapStyle()

    const style = getMapStyle() as maplibregl.StyleSpecification
    try {
      // 注册自定义协议
      registerCustomProtocol()

      map = new maplibregl.Map({
        container: mapContainer.value,
        style: style, // 默认使用街道图样式
        center: mapStatus.center ?? [118.62113, 25.00406],
        zoom: mapStatus.zoom ?? 12,
        maxZoom: mapStatus.maxZoom ?? 18,
        minZoom: mapStatus.minZoom ?? 1,
        pitch: 0,
        bearing: 0,
        attributionControl: {
          compact: true
        },
        maplibreLogo: false,
      })

      // 添加导航控件
      map.addControl(new maplibregl.NavigationControl(), 'top-right')

      // 添加地图切换控件
      map.addControl(new MapStyleControl(), 'top-right')

      map.addControl(new MapProviderStyleControl(), 'top-right')

      map.addControl(new OperateMapControl(), 'top-right')

      map.addControl(new MapLocateControl(), 'top-right')

      // 初始化地图层级显示
      currentZoom.value = Number(map.getZoom().toFixed(2))

      // 监听地图缩放事件
      map.on('zoom', handleZoomChange)
      map.on('zoomend', handleZoomChange)

      // 地图加载完成事件
      map.on('load', () => {
        initSelectionFeature()
        // 设置初始缩放级别
        handleZoomChange()

        deleteMapCacheData.value.Zoom = currentZoom.value
        deleteMapCacheData.value.MaxLon = getCenter()[0]
        deleteMapCacheData.value.MaxLat = getCenter()[1]
        deleteMapCacheData.value.MinLon = getCenter()[0]
        deleteMapCacheData.value.MinLat = getCenter()[1]
      })

      map.on('moveend', handleMapMoveEnd)

      // 错误处理
      map.on('error', (e: ErrorEvent) => {
        logger.error('地图错误:', e)
      })
    } catch (error) {
      logger.error('地图初始化失败:', error)
    }

    window.addEventListener('keydown', handleEscKey)
  })

  onUnmounted(() => {
    if (map) {
      map.remove()
      map = null
    }
    // 清除临时token定时器
    if (tokenExpireTimer) {
      clearTimeout(tokenExpireTimer)
      tokenExpireTimer = null
    }
    window.removeEventListener('keydown', handleEscKey)
    // 确保清理设置定位模式
    isSettingLocate.value = false
  })
</script>

<style scoped lang="scss">
  .map-container {
    height: calc(100vh - 50px);
  }

  .map-info {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    color: #ff0000;
    z-index: 1;
  }

  .clear-map-info {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.6);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    color: #ffffff;
    z-index: 1;
  }

  // 地图层级显示样式
  .zoom-level {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    color: #ff0000;
    font-weight: bold;
    z-index: 1;
  }

  // 确认按钮悬停样式
  :deep(.selection-confirm-button:hover) {
    background-color: #45a049 !important;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  }

  :deep(.maplibregl-ctrl-group button) {
    width: 30px;
    height: 30px;
    display: block;
    padding: 0;
    outline: none;
    border: 0;
    box-sizing: border-box;
    background-color: transparent;
    cursor: pointer;
  }

  :deep(.maplibregl-ctrl-group button:hover) {
    background-color: rgba(0, 0, 0, 0.05);
  }

  // 框选控件样式
  :deep(.maplibregl-ctrl-selection) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.maplibregl-ctrl-selection.active) {
    background-color: rgba(0, 0, 0, 0.1);
  }

  :deep(.selection-icon) {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 1px solid #333;
    position: relative;
  }

  :deep(.selection-icon::before) {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 8px;
    height: 8px;
    border: 1px dashed #333;
  }

  :deep(.google-icon) {
    background: url(@/images/google.png) no-repeat center center;
  }

  :deep(.tianditu-icon) {
    background: url(@/images/tianditu.png) no-repeat center center;
  }

  :deep(.osm-icon) {
    background: url(@/images/osm.png) no-repeat center center;
  }

  :deep(.roadmap-icon) {
    background: url(@/images/street.png) no-repeat center center;
  }

  :deep(.satellite-icon) {
    background: url(@/images/satellite.png) no-repeat center center;
  }

  :deep(.hybrid-icon) {
    background: url(@/images/hybrid.png) no-repeat center center;
  }

  :deep(.clear-map-icon) {
    background: url(@/images/clearMap.png) no-repeat center center;
  }

  :deep(.refresh-map-icon) {
    background: url(@/images/refresh.png) no-repeat center center;
  }

  :deep(.check-gcj02-icon) {
    background: url(@/images/check-gcj02.png) no-repeat center center;
  }

  :deep(.highlight-btn) {
    background-color: #E0E0E0 !important;
  }

  :deep(.locate-icon) {
    background: url(@/images/locate.png) no-repeat center center;
  }

  :deep(.set-locate-icon) {
    background: url(@/images/setLocate.png) no-repeat center center;
  }

  // 坐标显示区域样式
  .selection-coords {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: white;
    padding: 8px 12px;
    border-radius: 4px;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
    font-size: 12px;
    max-width: 300px;
    z-index: 1;
  }
</style>
