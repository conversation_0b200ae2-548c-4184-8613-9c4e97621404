<template>
  <q-page>
    <q-table
        style="height: calc(100vh - 50px)"
        ref="tableRef"
        binary-state-sort
        bordered
        flat
        virtual-scroll
        row-key="Rid"
        selection="multiple"
        :rows="finalRows"
        :columns="columns"
        :filter="filter"
        v-model:selected="selected"
        v-model:pagination="pagination"
        :rows-per-page-options="[0]"
        @selection="handleSelection"
        class="!border-x-0 !border-t-0 !rounded-none sticky-header-table"
    >
      <template v-slot:top>
        <q-btn
            color="primary"
            :label="`${$t('form.add')}${$t('dbProject.project')}`"
            @click="openDialogBeforeAction(OpenDialogAction.Add)"
        />
        <q-btn
            class="!ml-2"
            color="primary"
            :label="`${$t('form.add')}${$t('dbProjectToken.apiKey')}`"
            @click="openDialogNewApiKey(OpenDialogAction.Add)"
        />
        <q-btn
            class="!ml-2"
            color="negative"
            :label="$t('form.batchDelete')"
            :disable="selected.length <= 0"
            @click="batchDeleteRows(selected)"
        />
        <q-space/>
        <q-input
            dense
            debounce="300"
            v-model="filter"
            :placeholder="$t('form.search')"
        >
          <template v-slot:append>
            <q-icon name="search"/>
          </template>
        </q-input>
      </template>

      <template v-slot:header-selection="scope">
        <q-checkbox v-model="scope.selected">
          <q-tooltip
              anchor="top middle"
              :offset="[10, 20]"
          >
            {{ $t('dbUser.selectTooltip') }}
          </q-tooltip>
        </q-checkbox>
      </template>

      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td auto-width>
            <q-checkbox
                :model-value="props.selected"
                @update:model-value="(val, e) => toggleRowSelection(val, props.row, e)"
            />
          </q-td>
          <q-td v-for="col in props.cols" :key="col.name" :props="props">
            <template v-if="col.name === 'expand'">
              <q-btn size="sm" color="accent" round dense @click="props.expand = !props.expand"
                     :icon="props.expand ? 'remove' : 'add'"/>
            </template>
            <template v-else-if="col.name === 'Status'">
              <template v-if="props.row.Status === 1">
                <q-chip
                    dense
                    clickable
                    color="teal"
                    text-color="white"
                    icon="star"
                >
                  {{ $t('dbProjectToken.enable') }}
                  <popup-proxy-confirm
                      :message="$t('dbProject.wantToDisableProject')"
                      @confirm="toggleProjectStatus(props.row)"
                  />
                </q-chip>
              </template>
              <template v-else>
                <q-chip
                    dense
                    clickable
                    color="red"
                    text-color="white"
                    icon="star_border"
                >
                  {{ $t('dbProjectToken.disable') }}
                  <popup-proxy-confirm
                      :message="$t('dbProject.wantToEnableProject')"
                      @confirm="toggleProjectStatus(props.row)"
                  />
                </q-chip>
              </template>
            </template>
            <template v-else-if="col.name === 'Note'">
              <OverflowTd :value="props.row.Note"></OverflowTd>
            </template>
            <template v-else-if="col.name === 'operate'">
              <q-btn
                  round
                  outline
                  icon="edit"
                  size="xs"
                  color="primary"
                  @click="openDialogBeforeAction(OpenDialogAction.Edit, props.row)"
              >
                <q-tooltip>
                  {{ $t('form.edit') }}
                </q-tooltip>
              </q-btn>
              <q-btn
                  round
                  outline
                  icon="delete"
                  size="xs"
                  color="negative"
                  class="!ml-2"
              >
                <q-tooltip>
                  {{ $t('form.delete') }}
                </q-tooltip>
                <popup-proxy-confirm
                    :message="$t('dbProject.wantToDeleteProject')"
                    @confirm="deleteDbProjectRow(props.row)"
                />
              </q-btn>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>
        <q-tr v-show="props.expand" :props="props">
          <q-td colspan="100%" style="background-color: #f0f0f0" class="!p-4 !h-auto">
            <DbProjectTokenTable
                :project="props.row"
                @open-project-token-dlg="openDialogNewApiKey"
            >
            </DbProjectTokenTable>
          </q-td>
        </q-tr>
      </template>

      <!--      <template v-slot:no-data>-->
      <!--        <div class="full-width flex justify-center">-->
      <!--          <q-icon-->
      <!--              name="warning"-->
      <!--              size="xs"-->
      <!--          ></q-icon>-->
      <!--          <span>{{ $t('dbUser.noData') }}</span>-->
      <!--        </div>-->
      <!--      </template>-->
    </q-table>

    <q-dialog v-model="visible" persistent>
      <q-card class="w-fit !max-w-none p-4 pt-2">
        <!--header-->
        <q-bar class="bg-white !px-2 mt-2">
          <q-icon name="people" size="24px" color="primary"/>
          <span class="pl-2">{{
              `${$t('dbProject.project')}-${dialogAction === OpenDialogAction.Add ? $t('form.add') : $t('form.edit')}`
            }}</span>
          <q-space></q-space>
          <q-btn flat round dense icon="close" size="sm" v-close-popup/>
        </q-bar>

        <!--body-->
        <q-card-section class="w-fit !p-2">
          <q-form class="fit grid gap-x-4 user-form" :class="{ 'min-w-xl': !isMobile }" ref="formRef">
            <q-input
                v-model="projectData.Name"
                :label="$t('dbProject.name')"
                outlined
                dense
                autofocus
                clearable
                lazy-rules
                :maxlength="16"
                :rules="[rules.required, rules.nameUnique]"
            />
            <q-input
                type="textarea"
                v-model="projectData.Note"
                :label="$t('dbUser.note')"
                outlined
                dense
                autogrow
                clearable
                counter
                lazy-rules
                :maxlength="256"
                :rules="[]"
                v-clear-fix="[projectData, 'Note']"
            />

            <div>
              <div class="rounded-borders">
                <div class="text-ellipsis mb-2">{{ $t('dbMapProviderToken.quotaCalcMethod') }}</div>
                <q-option-group
                    v-model="quotaData.QuotasType"
                    :options="quotasTypeOptions"
                    type="radio"
                    inline
                    dense
                />
              </div>

              <q-input
                  v-model.number="quotaData.QuotasLimit"
                  :label="$t('dbProject.quotasLimit')"
                  :rules="[rules.quotasLimit]"
                  type="number"
                  :hint="$t('dbMapProviderToken.quotasUnlimited')"
              />
            </div>

            <div class="flex justify-center gap-2 mt-4">
              <q-btn
                  color="primary"
                  class="!w-[150px]"
                  :label="dialogAction === OpenDialogAction.Add ? $t('form.add') : $t('form.confirm')"
                  @click="submit(false)"
              />
              <q-btn
                  v-if="dialogAction === OpenDialogAction.Add"
                  color="secondary"
                  class="!w-[150px]"
                  :label="$t('form.keepAdd')"
                  @click="submit(true)"
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <q-dialog
        v-model="tokenDlgVisible"
        persistent
    >
      <q-card class="w-fit !max-w-[50vw]">
        <!--header-->
        <q-bar class="bg-white !px-4 mt-2">
          <q-icon
              name="api"
              size="24px"
              color="primary"
          />
          <span class="pl-2">{{
              `${$t('pages.dbProjectToken')}-${dialogAction === OpenDialogAction.Add ? $t('form.add')
                  : $t('form.edit')}`
            }}</span>
          <q-space></q-space>
          <q-btn
              flat
              round
              dense
              icon="close"
              size="sm"
              v-close-popup
          />
        </q-bar>

        <!--body-->
        <q-card-section class="w-fit">
          <q-form
              class="grid grid-cols-1 gap-2 user-form"
              :class="{ 'min-w-xl': !isMobile }"
              ref="tokenFormRef"
          >
            <q-input
                v-model="dbProjectToken.Name"
                :label="$t('dbMapProviderToken.apiName')"
                outlined
                dense
                clearable
                lazy-rules
                :maxlength="16"
                :rules="[rules.required, rules.apiNameUnique]"
            />
            <q-select
                outlined
                dense
                emit-value
                map-options
                options-dense
                v-model="dbProjectToken.ProjectRid"
                :options="projectRidOptions"
                :rules="[rules.required]"
                :label="$t('dbProject.name')"
            >
              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section>
                    <q-item-label>{{ scope.opt.label }}</q-item-label>
                    <q-item-label caption>{{ getProjectUser(scope.opt.userRid) }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </q-select>

            <q-select
             :label="$t('dbMapProviderToken.sysName')"
              v-model="dbProjectToken.SysName!.Names"
              use-input
              use-chips
              multiple
              hide-dropdown-icon
              input-debounce="0"
              outlined
              dense
              clearable
              maxlength="16"
              :rules="[rules.required, rules.apiSysNameURequired]"
              :hint="t('dbProjectToken.sysNameHint')"
              @input-value="sysNameInput"
              @blur="sysNameBlur"
              @new-value="handleNewSysName"
            >
              <template v-slot:selected-item="scope">
                <q-chip
                    removable
                    dense
                    @remove="scope.removeAtIndex(scope.index)"
                    :tabindex="scope.tabindex"
                    color="light-blue-7"
                    text-color="white"
                >
                  {{ scope.opt }}
                </q-chip>
              </template>
            </q-select>

            <q-input
                v-model="dbProjectToken.Note"
                :label="$t('dbUser.note')"
                type="textarea"
                autogrow
                outlined
                dense
                lazy-rules
                :maxlength="0xFF"
                class="pb-5"
            />

            <div class="mb-3 rounded-borders">
              <div class="text-ellipsis mb-2">{{ $t('dbProjectToken.availableMaps') }}</div>
              <q-option-group
                  v-model="availableMapProvider"
                  @update:model-value="availableMapProviderChange"
                  :options="availableMapProviderOption"
                  type="checkbox"
                  inline
                  dense
              />
            </div>

            <div class="mb-3 rounded-borders">
              <div class="text-ellipsis mb-2">{{ $t('dbProjectToken.defaultRoadmap') }}</div>
              <q-option-group
                  v-model="availableMapProviderDefault.DefaultRoadmap"
                  :options="defaultMapProvideOption"
                  type="radio"
                  inline
                  dense
              />
            </div>

            <div class="mb-3 rounded-borders" v-if="!notShowSatelliteAndHybrid">
              <div class="text-ellipsis mb-2">{{ $t('dbProjectToken.defaultSatellite') }}</div>
              <q-option-group
                  v-model="availableMapProviderDefault.DefaultSatellite"
                  :options="defaultMapProvideWithOutOSMOption"
                  type="radio"
                  inline
                  dense
              />
            </div>

            <div class="mb-3 rounded-borders" v-if="!notShowSatelliteAndHybrid">
              <div class="text-ellipsis mb-2">{{ $t('dbProjectToken.defaultHybrid') }}</div>
              <q-option-group
                  v-model="availableMapProviderDefault.DefaultHybrid"
                  :options="defaultMapProvideWithOutOSMOption"
                  type="radio"
                  inline
                  dense
              />
            </div>

            <div class="flex items-center justify-start pb-1 gap-3">
              <q-checkbox
                  v-model="foreverExpireTime"
                  :label="$t('dbProjectToken.alwaysValid')"
                  dense
                  class="flex-none"
              />
              <q-input
                  v-if="!foreverExpireTime"
                  outlined
                  dense
                  hide-bottom-space
                  v-model="apiExpireTime"
                  :rules="[rules.required]"
                  :label="$t('dbProjectToken.expiryDate')"
                  class="flex-auto"
              >
                <template v-slot:prepend>
                  <q-icon
                      name="event"
                      class="cursor-pointer"
                  >
                    <q-popup-proxy
                        cover
                        transition-show="scale"
                        transition-hide="scale"
                    >
                      <q-date
                          v-model="apiExpireTime"
                          mask="YYYY-MM-DD HH:mm"
                      />
                    </q-popup-proxy>
                  </q-icon>
                </template>

                <template v-slot:append>
                  <q-icon
                      name="access_time"
                      class="cursor-pointer"
                  >
                    <q-popup-proxy
                        cover
                        transition-show="scale"
                        transition-hide="scale"
                    >
                      <q-time
                          v-model="apiExpireTime"
                          mask="YYYY-MM-DD HH:mm"
                          format24h
                      >
                      </q-time>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>
            </div>

            <q-checkbox
                class="w-fit"
                v-model="dbProjectToken.Status"
                :label="$t('dbProjectToken.wantToEnableToken')"
                :true-value="1"
                :false-value="4"
                dense
            />
          </q-form>
        </q-card-section>

        <!--footer-->
        <q-card-actions class="flex justify-center gap-4">
          <q-btn
              color="primary"
              class="!w-[150px]"
              :label="dialogAction === OpenDialogAction.Add ? $t('form.add') : $t('form.confirm')"
              @click="addTokenSubmit(false)"
          />
          <q-btn
              v-if="dialogAction === OpenDialogAction.Add"
              color="secondary"
              class="!w-[150px]"
              :label="$t('form.keepAdd')"
              @click="addTokenSubmit(true)"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
  import {
    computed,
    customRef,
    defineAsyncComponent,
    onBeforeUnmount,
    onMounted,
    ref,
  } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useDbProjectQuotasStore, useDbProjectStore, useDbProjectTokenStore, useDbUserStore } from '@/stores/dataBase'
  import { QForm, QPage, QTable, type QTableColumn, useQuasar } from 'quasar'
  import { addDateTime, bigIntUnixTimeToString, dateTimeStringToDayjs, formatDayjs, getTimestamp } from '@/utils/dayjs'
  import {
    type DbProject,
    type DbProjectQuotas,
    DbProjectQuotasSchema,
    DbProjectSchema,
    type DbProjectToken,
    DbProjectTokenSchema,
    MapProviderEnum, SysNamesSchema,
    type TAvailableMapProvider,
    TAvailableMapProviderSchema
  } from '@/proto/db_pb'
  import { setProject, setProjectToken, } from '@/services/connectRpc'
  import { create, fromJsonString, toJsonString } from '@bufbuild/protobuf'
  import { useI18n } from 'vue-i18n'
  import { errorMessage, successMessage, warnMessage } from '@/utils/notify'
  import { OpenDialogAction } from '@/utils/types.ts'
  import { v7 as uuidV7 } from 'uuid'
  import { useLoginStatusStore } from '@/stores/session.ts'
  import * as validation from '@/utils/validation.ts'
  import { confirmAgainBatchDelete, getNextName, useTableHandleSelection } from '@/utils/common.ts'
  import cloneDeep from 'lodash/cloneDeep'
  import DbProjectTokenTable from '@/components/DbProjectToken.vue'
  import { StrPubSub } from 'ypubsub'
  import { NewApiKeyAction, NewProjectAction } from '@/utils/pubSubSubject.ts'
  import OverflowTd from '@/components/OverflowTd.vue'
  import { DbProjectRid } from '@/stores/common.ts'
  import { dayjs } from '@/utils/dayjs.ts'
  import { SetProjectReqSchema, SetProjectTokenReqSchema } from '@/proto/bfmap.rpc_pb.ts'

  const PopupProxyConfirm = defineAsyncComponent(() => import('@/components/PopupProxyConfirm.vue'))
  const { t } = useI18n()
  const $q = useQuasar()
  const dbProjectStore = useDbProjectStore()
  const { rows, rowsMap: dbProjectRowsMap, } = storeToRefs(dbProjectStore)
  const {
    updateRow: updateDbProjectStoreRow,
    deleteRow: deleteDbProjectStoreRow,
    addRow: addDbProjectStoreRow
  } = dbProjectStore
  const dbProjectQuotasStore = useDbProjectQuotasStore()
  const { rowsMap: quotasRowsMap } = storeToRefs(dbProjectQuotasStore)
  const { updateRow: updateDbProjectQuotaStoreRow, addRow: addDbProjectQuotaStoreRow } = dbProjectQuotasStore
  const dbUserStore = useDbUserStore()
  const { rowsMap: dbUserStoreRowsMap } = storeToRefs(dbUserStore)
  const dbProjectTokenStore = useDbProjectTokenStore()
  const { rows: dbProjectTokenStoreRows } = storeToRefs(dbProjectTokenStore)
  const { addRow, updateRow: updateDbProjectTokenStoreRow } = dbProjectTokenStore

  const getDefaultProjectData = () => {
    return create(DbProjectSchema, {
      Rid: uuidV7(),
      UserRid: loginStatusStore.userRid,
      Name: '',
      CreateTime: getTimestamp(),
      Note: '',
      Setting: '{}',
      Status: 1,
    })
  }

  const getDefaultProjectQuota = () => {
    return create(DbProjectQuotasSchema, {
      Rid: projectData.value.Rid,
      QuotasType: 3,
      QuotasLimit: 0,
      CountStartTime: getTimestamp(),
      CurrentUsedQuotas: 0,
    })
  }

  const getDefaultDbProjectToken = (): DbProjectToken => {
    return create(DbProjectTokenSchema, {
      Rid: uuidV7(),
      ProjectRid: dbProjectRowsMap.value.get(DbProjectRid)?.Rid || '',
      Token: uuidV7(),
      CreateTime: getTimestamp(),
      Note: '',
      Name: '',
      Setting: '{}',
      ExpireTime: BigInt(addDateTime(dayjs.utc(), 12, 'month').unix()),
      Status: 1,
      AvailableMapProvider: '{}',
      SysName: create(SysNamesSchema, {
        Names: []
      })
    })
  }

  const filter = ref('')
  const visible = ref(false)
  const tokenDlgVisible = ref(false)
  const dialogAction = ref<OpenDialogAction>(OpenDialogAction.Add)
  const loginStatusStore = useLoginStatusStore()
  const projectData = ref<DbProject>(getDefaultProjectData())
  const dbProjectToken = ref<DbProjectToken>(getDefaultDbProjectToken())
  const formRef = ref<QForm>()
  const tokenFormRef = ref<QForm>()
  const quotaData = ref<DbProjectQuotas>(getDefaultProjectQuota())
  const { tableRef, selected, handleSelection } = useTableHandleSelection<DbProject>()
  const pagination = ref({
    rowsPerPage: 0
  })

  const isMobile = computed(() => {
    return $q.screen.lt.sm
  })

  const finalRows = computed(() => {
    return rows.value.filter(row => row.Status !== 8)
  })

  const apiExpireTime = customRef<string>((track: () => void, trigger: () => void) => ({
    get: () => {
      track()
      try {
        return bigIntUnixTimeToString(dbProjectToken.value.ExpireTime, 'YYYY-MM-DD HH:mm')
      } catch {
        return ''
      }

    },
    set: (value: string) => {
      try {
        dbProjectToken.value.ExpireTime = value ? BigInt(dateTimeStringToDayjs(value).unix()) : 0n
      } catch {
        dbProjectToken.value.ExpireTime = 0n
      }

      trigger()
    }
  }))
  const foreverExpireTime = customRef<boolean>((track: () => void, trigger: () => void) => ({
    get: () => {
      track()
      return dbProjectToken.value.ExpireTime === 0n
    },
    set: (value: boolean) => {
      if (value) {
        dbProjectToken.value.ExpireTime = 0n
      } else {
        dbProjectToken.value.ExpireTime = BigInt(addDateTime(dayjs.utc(), 12, 'month').unix())
      }
      trigger()
    }
  }))

  const columns = computed<QTableColumn[]>(() => {
    return [
      {
        name: 'expand',
        label: '',
        field: '',
      },
      {
        name: 'name',
        label: t('dbProject.name'),
        align: 'center',
        field: 'Name',
      },
      {
        name: 'Status',
        label: t('dbProjectToken.status'),
        align: 'center',
        field: 'Status',
      },
      {
        name: 'apiCount',
        label: t('dbProject.apiCount'),
        align: 'center',
        field: '',
        format: (_, row: DbProject) => {
          return dbProjectTokenStoreRows.value.filter(tk => tk.Status !== 8 && row.Rid == tk.ProjectRid).length + ''
        }
      },
      {
        name: 'quotasType',
        label: t('dbProject.quotasType'),
        align: 'center',
        field: '',
        format: (_, row: DbProject) => {
          const quota = quotasRowsMap.value.get(row.Rid)
          return getQuotasTypeLabel(quota?.QuotasType ?? 0)
        }
      },
      {
        name: 'quotasLimitCount',
        label: t('dbProject.usedQuotas') + ' / ' + t('dbProject.quotasLimit'),
        align: 'center',
        field: '',
        format: (_, row: DbProject) => {
          const quota = quotasRowsMap.value.get(row.Rid)
          return quota?.CurrentUsedQuotas + ' / ' + getQuotasLimitLabel(quota?.QuotasLimit ?? 0)
        }
      },
      {
        name: 'userRid',
        label: t('dbProject.creator'),
        align: 'center',
        field: 'UserRid',
        format: (val: string) => {
          return dbUserStoreRowsMap.value.get(val)?.Name ?? val
        }
      },
      {
        name: 'CreateTime',
        label: t('dbUser.createTime'),
        align: 'center',
        field: 'CreateTime',
        sortable: true,
        format: (value: bigint) => {
          return formatDayjs(Number(value) * 1000)
        },
      },
      {
        name: 'Note',
        label: t('dbUser.note'),
        align: 'center',
        field: 'Note',
      },
      {
        name: 'operate',
        label: t('dbUser.operate'),
        align: 'center',
        field: ''
      },
    ]
  })

  const projectRidOptions = computed(() => {
    return rows.value
        .filter(row => row.Status !== 8)
        .map(row => {
          return {
            label: row.Name,
            value: row.Rid,
            userRid: row.UserRid,
          }
        })
  })

  const availableMapProviderDefault = ref<Record<string, MapProviderEnum>>({
    DefaultRoadmap: MapProviderEnum.ProviderGoogle,
    DefaultSatellite: MapProviderEnum.ProviderGoogle,
    DefaultHybrid: MapProviderEnum.ProviderGoogle,
  })

  // 需要在添加或更新时使用TAvailableMapProvider进行json序列化
  const availableMapProvider = ref<string[]>(['Google'])

  const availableMapProviderOption = computed(() => {
    return [
      {
        label: t('dbProjectToken.googleMap'),
        value: 'Google',
        provider: MapProviderEnum.ProviderGoogle,
      },
      {
        label: t('dbProjectToken.tianditu'),
        value: 'Tianditu',
        provider: MapProviderEnum.ProviderTianditu,
      },
      {
        label: t('dbProjectToken.openStreetMap'),
        value: 'OSM',
        provider: MapProviderEnum.ProviderOSM,
      },
    ]
  })
  const defaultMapProvideOption = computed(() => {
    return [
      {
        label: t('dbProjectToken.googleMap'),
        value: MapProviderEnum.ProviderGoogle,
        disable: !availableMapProvider.value.includes('Google'),
      },
      {
        label: t('dbProjectToken.tianditu'),
        value: MapProviderEnum.ProviderTianditu,
        disable: !availableMapProvider.value.includes('Tianditu'),
      },
      {
        label: t('dbProjectToken.openStreetMap'),
        value: MapProviderEnum.ProviderOSM,
        disable: !availableMapProvider.value.includes('OSM'),
      },
    ]
  })
  const defaultMapProvideWithOutOSMOption = computed(() => {
    return [
      {
        label: t('dbProjectToken.googleMap'),
        value: MapProviderEnum.ProviderGoogle,
        disable: !availableMapProvider.value.includes('Google'),
      },
      {
        label: t('dbProjectToken.tianditu'),
        value: MapProviderEnum.ProviderTianditu,
        disable: !availableMapProvider.value.includes('Tianditu'),
      },
    ]
  })

  const getProjectUser = (rid: string) => {
    const user = dbUserStoreRowsMap.value.get(rid)
    return user?.Name ?? rid
  }

  const availableMapProviderChange = (value: string[]) => {
    // 必须选一项
    if (value.length === 0) {
      availableMapProvider.value = ['Google']
      for (const key in availableMapProviderDefault.value) {
        availableMapProviderDefault.value[key] = MapProviderEnum.ProviderGoogle
      }
      return
    }

    const options = availableMapProviderOption.value.filter(item => value.includes(item.value))
    // 如果可用只有OSM,两个默认影像图都设置Google
    if (options.length === 1 && options[0].provider === MapProviderEnum.ProviderOSM) {
      availableMapProviderDefault.value = {
        DefaultRoadmap: MapProviderEnum.ProviderOSM,
        DefaultSatellite: MapProviderEnum.ProviderGoogle,
        DefaultHybrid: MapProviderEnum.ProviderGoogle,
      }
      return
    }
    // 如果默认的地图不在选中，则从选中的列表中取第一项
    for (const key in availableMapProviderDefault.value) {
      if (!options.some(item => item.provider === availableMapProviderDefault.value[key])) {
        availableMapProviderDefault.value[key] = options[0].provider
      }
    }
  }
  const oneSysNameCache = ref<string>('')
  const sysNameInput = (val: string) => {
    oneSysNameCache.value = val
  }
  const sysNameBlur = () => {
    const cache = oneSysNameCache.value
    const sysName = dbProjectToken.value.SysName

    if ((dbProjectToken.value.SysName?.Names?.length ?? 0) >= 32) {
      warnMessage(t('dbProjectToken.maxSysName'))
      return
    }

    if (!cache) return

    if (!sysName) {
      dbProjectToken.value.SysName = create(SysNamesSchema, {
        Names: [cache]
      })
      return
    }

    if (!Array.isArray(sysName.Names)) {
      sysName.Names = []
    }

    if (!sysName.Names.includes(cache)) {
      sysName.Names.push(cache)
    }
  }

  // eslint-disable-next-line no-unused-vars
  const handleNewSysName = (val: string, done: (item?: any, mode?: 'add' | 'toggle' | 'add-unique' | undefined) => void) => {
    if ((dbProjectToken.value.SysName?.Names?.length ?? 0) >= 32) {
      warnMessage(t('dbProjectToken.maxSysName'))
      return
    }
    done(val, 'add-unique')
  }

  const notShowSatelliteAndHybrid = computed(() => {
    return availableMapProvider.value.length === 1 && availableMapProvider.value[0] === 'OSM'
  })

  const maxQuotasLimit = 999999999
  const rules = computed(() => ({
    required: (val: string | null | undefined) => validation.required(val),
    nameUnique: (val: string) => {
      const row = finalRows.value.find(row => row.Name === val && row.UserRid === loginStatusStore.userRid)
      if (row?.Rid === projectData.value.Rid) {
        return true
      }
      return row ? t('validate.nameUnique') : true
    },
    apiNameUnique: (val: string) => {
      const row = dbProjectTokenStoreRows.value.find(row => row.Name === val && row.ProjectRid === dbProjectToken.value.ProjectRid)
      if (row?.Rid === dbProjectToken.value.Rid) {
        return true
      }
      return (row && row.Status !== 8) ? t('validate.nameUnique') : true
    },
    apiSysNameURequired: (val?: Array<string>) => {
      return (val && val.length > 0) || t('validate.required')
    },
    quotasLimit: (val: number) => (val >= 0 && val <= maxQuotasLimit) || t('validate.numberRange', {
      min: 0,
      max: maxQuotasLimit.toLocaleString()
    }),
  }))

  // 1:per minute 2:per hour 3:per day 4:per month
  const quotasTypeOptions = computed(() => {
    return [
      {
        label: t('dbProject.quotasCalculateBy', { type: t('dbProject.minute') }),
        value: 1
      },
      {
        label: t('dbProject.quotasCalculateBy', { type: t('dbProject.hour') }),
        value: 2
      },
      {
        label: t('dbProject.quotasCalculateBy', { type: t('dbProject.day') }),
        value: 3
      },
      {
        label: t('dbProject.quotasCalculateBy', { type: t('dbProject.month') }),
        value: 4
      },
    ]
  })

  const toggleProjectStatus = async (row: DbProject) => {
    const newRow = create(DbProjectSchema, {
      ...row,
      Status: row.Status === 1 ? 4 : 1,
    })

    const req = create(SetProjectReqSchema, {
      Code: 2,
      DbProject: newRow,
      UpdateProjectFields: ['Status'],
    })

    const res = await setProject(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      updateDbProjectStoreRow(newRow)
    } else {
      errorMessage(t('common.operationFailed'))
    }
  }

  const getQuotasTypeLabel = (quotasType: number): string => {
    switch (quotasType) {
      case 1:
        return t('dbProject.quotasCalculateBy', { type: t('dbProject.minute') })
      case 2:
        return t('dbProject.quotasCalculateBy', { type: t('dbProject.hour') })
      case 3:
        return t('dbProject.quotasCalculateBy', { type: t('dbProject.day') })
      case 4:
        return t('dbProject.quotasCalculateBy', { type: t('dbProject.month') })
      default:
        return '' + quotasType
    }
  }
  const getQuotasLimitLabel = (quotasLimit: number): string => {
    if (quotasLimit === 0) {
      return t('dbProject.unlimited')
    }
    return quotasLimit + ''
  }

  async function batchDeleteRows(rows: DbProject[]) {
    const isConfirm = await confirmAgainBatchDelete()
    if (!isConfirm) return

    const dialog = $q.dialog({
      message: t('common.operationProgress', { progress: 0 }),
      progress: true,
      persistent: true,
      ok: false,
      class: 'flex flex-col items-center justify-center'
    })

    try {
      const total = rows.length
      let successCount = 0
      let failCount = 0
      const startTime = Date.now()

      // 创建删除任务数组，每个任务都包含删除操作和更新进度
      const tasks = rows.map((row, index) => {
        const newRow = create(DbProjectSchema, {
          ...row,
          Status: 8,
        })

        const req = create(SetProjectReqSchema, {
          Code: 4,
          DbProject: newRow,
          DbProjectQuotas: quotasRowsMap.value.get(newRow.Rid)
        })

        return setProject(req).then(res => {
          if (res.Code === 0) {
            successCount++
            deleteDbProjectStoreRow(row)
          } else {
            failCount++
          }
          // 更新进度对话框
          const progress = Math.round(((index + 1) / total) * 100)
          dialog.update({
            message: t('common.operationProgress', { progress })
          })
          return res
        })
      })

      // 等待所有删除任务完成
      await Promise.all(tasks)

      // 关闭进度对话框，如果删除时间小600毫秒，则等待600毫秒，避免进度提示闪烁
      if (Date.now() - startTime < 800) {
        await new Promise(resolve => setTimeout(resolve, 600 - (Date.now() - startTime)))
      }
      dialog.hide()

      // 清空选中项
      selected.value = []

      // 显示操作结果
      if (failCount === 0) {
        successMessage(t('common.operationSuccess'))
      } else {
        errorMessage(t('common.operationPartialSuccess', {
          success: successCount,
          fail: failCount
        }))
      }
    } catch (error) {
      dialog.hide()
      errorMessage(t('common.operationFailed') + `: ${error}`)
    }
  }

  const deleteDbProjectRow = async (row: DbProject) => {

    const newRow = create(DbProjectSchema, {
      ...row,
      Status: 8,
    })

    const req = create(SetProjectReqSchema, {
      Code: 4,
      DbProject: newRow,
      DbProjectQuotas: quotasRowsMap.value.get(newRow.Rid)
    })

    const res = await setProject(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      deleteDbProjectStoreRow(row)
    } else {
      errorMessage(t('common.operationFailed'))
    }
  }

  const addDbProjectRow = async (row: DbProject) => {
    const req = create(SetProjectReqSchema, {
      Code: 1,
      DbProject: row,
      DbProjectQuotas: quotaData.value,
    })
    const res = await setProject(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      addDbProjectStoreRow({ ...row })
      addDbProjectQuotaStoreRow({ ...quotaData.value })
      return true
    }
    errorMessage(t('common.operationFailed') + ` ${res.Reason}`)
    return false
  }

  const updateProjectRow = async (row: DbProject, quotaRow: DbProjectQuotas) => {
    const req = create(SetProjectReqSchema, {
      Code: 2,
      DbProject: row,
      DbProjectQuotas: quotaRow,
      UpdateProjectFields: ['Name', 'Note'],
      UpdateQuotasFields: ['QuotasLimit', 'QuotasType'],
    })

    const res = await setProject(req)

    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      updateDbProjectStoreRow({ ...row })
      updateDbProjectQuotaStoreRow({ ...quotaRow })
      return true
    }

    return false
  }

  const submit = async (keepAdd: boolean) => {
    const valid = await formRef.value!.validate()
    if (!valid) return

    let isOk: boolean = false
    if (dialogAction.value === OpenDialogAction.Add) {
      isOk = await addDbProjectRow(projectData.value)
    } else {
      isOk = await updateProjectRow(projectData.value, quotaData.value)
    }

    if (isOk && !keepAdd) {
      visible.value = false
    }

    if (keepAdd) {
      const row = getDefaultProjectData()
      row.Name = getNextName(projectData.value.Name)
      row.Note = projectData.value.Note
      projectData.value = row
      quotaData.value.Rid = row.Rid
    }
  }

  // 插入一行数据
  const insertDbProjectToken = async (row: DbProjectToken): Promise<boolean> => {
    const mapProvider = create(TAvailableMapProviderSchema, availableMapProvider.value.map(item => {
      return {
        [item]: true
      }
    }).reduce((p, c) => Object.assign(p, c), { ...availableMapProviderDefault.value }))
    row.AvailableMapProvider = toJsonString(TAvailableMapProviderSchema, mapProvider)
    row.CreateTime = getTimestamp()
    const req = create(SetProjectTokenReqSchema, {
      Code: 1,
      ProjectToken: row,
    })
    const res = await setProjectToken(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      addRow(row)
      return true
    }

    errorMessage(t('common.operationFailed') + ` ${res.Reason}`)
    return false
  }

  // 更新一行数据
  const updateDbProjectToken = async (row: DbProjectToken): Promise<boolean> => {
    const mapProvider = create(TAvailableMapProviderSchema, availableMapProvider.value.map(item => {
      return {
        [item]: true
      }
    }).reduce((p, c) => Object.assign(p, c), { ...availableMapProviderDefault.value }))
    row.AvailableMapProvider = toJsonString(TAvailableMapProviderSchema, mapProvider)
    const req = create(SetProjectTokenReqSchema, {
      Code: 2,
      ProjectToken: row,
    })
    const res = await setProjectToken(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      updateDbProjectTokenStoreRow(row)
      return true
    }

    errorMessage(t('common.operationFailed') + ` ${res.Reason}`)
    return false
  }

  async function addTokenSubmit(keepAdd?: boolean) {
    const valid = await tokenFormRef.value!.validate()
    if (!valid) return

    let isOk: boolean
    if (dialogAction.value === OpenDialogAction.Add) {
      isOk = await insertDbProjectToken(dbProjectToken.value)
    } else {
      isOk = await updateDbProjectToken(dbProjectToken.value)
    }

    if (isOk && !keepAdd) {
      tokenDlgVisible.value = false
    }

    // 创建新的token数据
    if (keepAdd) {
      const row = getDefaultDbProjectToken()
      row.ProjectRid = dbProjectToken.value.ProjectRid
      row.Status = dbProjectToken.value.Status
      row.ExpireTime = dbProjectToken.value.ExpireTime
      row.Note = dbProjectToken.value.Note
      row.Name = getNextName(dbProjectToken.value.Name)
      dbProjectToken.value = row
    }
  }

  const isRowSelected = (row: DbProject) => {
    return selected.value.some(r => r.Rid === row.Rid)
  }
  const toggleRowSelection = (val: boolean, row: DbProject, evt: Event) => {
    const added = val
    if (val) {
      if (!isRowSelected(row)) {
        selected.value.push(row)
      }
    } else {
      selected.value = selected.value.filter(r => r.Rid !== row.Rid)
    }

    handleSelection({ rows: [row], added, evt })
  }

  function openDialogBeforeAction(action: OpenDialogAction, row?: DbProject) {
    dialogAction.value = action
    visible.value = true
    if (action === OpenDialogAction.Add) {
      projectData.value = getDefaultProjectData()
      quotaData.value = getDefaultProjectQuota()
    } else {
      projectData.value = cloneDeep(row!)
      const q = quotasRowsMap.value.get(row!.Rid)
      quotaData.value = q ? cloneDeep(q) : getDefaultProjectQuota()
    }
  }

  const openDialogNewApiKey = (action: OpenDialogAction, row?: DbProjectToken) => {
    dialogAction.value = action
    tokenDlgVisible.value = true
    if (action === OpenDialogAction.Add) {
      dbProjectToken.value = getDefaultDbProjectToken()
    } else {
      dbProjectToken.value = cloneDeep(row as DbProjectToken)
      if (!dbProjectToken.value.SysName?.Names) {
        dbProjectToken.value.SysName = create(SysNamesSchema, {
          Names: []
        })
      }
      // 需要将地图平台解析出来
      const mapProvider: TAvailableMapProvider = fromJsonString(TAvailableMapProviderSchema, dbProjectToken.value.AvailableMapProvider, {
        ignoreUnknownFields: true
      })
      availableMapProviderDefault.value = {
        DefaultRoadmap: mapProvider.DefaultRoadmap,
        DefaultSatellite: mapProvider.DefaultSatellite,
        DefaultHybrid: mapProvider.DefaultHybrid,
      }
      availableMapProvider.value = Object.keys(mapProvider).filter(key => mapProvider[key as keyof TAvailableMapProvider] === true)
    }
  }

  onMounted(() => {
    StrPubSub.subscribe(NewProjectAction, () => openDialogBeforeAction(OpenDialogAction.Add)
    )
    StrPubSub.subscribe(NewApiKeyAction, () => openDialogNewApiKey(OpenDialogAction.Add))
  })

  onBeforeUnmount(() => {
    StrPubSub.unsubscribe(NewProjectAction)
    StrPubSub.unsubscribe(NewApiKeyAction)
  })
</script>

<style scoped lang="scss"></style>
