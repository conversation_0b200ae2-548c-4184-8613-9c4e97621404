<template>
  <q-page class="p-4">
    <div class="flex flex-wrap !w-full" :class="{ 'gap-4': isMobile }" v-if="finalProjectRows.length > 0">
      <div class="w-full md:flex-auto md:max-w-1/2 relative">
        <q-btn class="flex absolute z-10" dense icon="west" @click="goBack" v-if="usageOptionStack.length !== 0">
          <q-tooltip>
            {{ $t('dashboard.back') }}
          </q-tooltip>
        </q-btn>
        <q-btn class="flex absolute z-10" dense icon="refresh" @click="refreshProjectUsage(true)" v-else>
          <q-tooltip>
            {{ $t('dashboard.refresh') }}
          </q-tooltip>
        </q-btn>
        <v-chart
            ref="barChart"
            class="!h-96 w-full"
            :option="projectOption"
            autoresize
            @click="chartClick"/>
      </div>
      <div class="w-full md:max-w-1/2 md:flex-auto  relative">
        <q-btn-group class="flex absolute z-10 flex-col">
          <q-btn dense icon="west" @click="goBack" v-if="allUsageOptionStack.length !== 0">
            <q-tooltip>
              {{ $t('dashboard.back') }}
            </q-tooltip>
          </q-btn>
        </q-btn-group>
        <v-chart
            ref="pieChart"
            class="!h-96 w-full"
            :option="projectAllUsageOption"
            autoresize
            @click="chartClick"/>
      </div>
    </div>
    <div class="flex flex-wrap !w-full" :class="{ 'pt-5': !isMobile, 'gap-4': isMobile  }" v-if="finalMapProviderTokenRows.length > 0">
      <div class="w-full md:flex-auto md:max-w-1/2 relative">
        <q-btn class="flex absolute z-10" dense icon="refresh" @click="refreshMapProvider(true)">
          <q-tooltip>
            {{ $t('dashboard.refresh') }}
          </q-tooltip>
        </q-btn>
        <v-chart
            ref="mapBarChart"
            class="!h-96 w-full"
            :option="mapProviderTokenUsageOption"
            autoresize/>
      </div>
      <div class="w-full md:flex-auto md:max-w-1/2 relative">
        <v-chart
            ref="mapPieChart"
            class="!h-96 w-full"
            :option="mapProviderTokenAllUsageOption"
            autoresize/>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
  import * as echarts from 'echarts/core'
  import { use } from 'echarts/core'
  import { CanvasRenderer } from 'echarts/renderers'
  import { BarChart, LineChart, PieChart } from 'echarts/charts'
  import {
    GraphicComponent,
    GridComponent,
    LegendComponent,
    TitleComponent,
    TooltipComponent,
    DataZoomComponent
  } from 'echarts/components'
  import VChart from 'vue-echarts'
  import { computed, onMounted, reactive, ref, watch } from 'vue'
  import type { EChartsOption, BarSeriesOption } from 'echarts'
  import { storeToRefs } from 'pinia'
  import { useI18n } from 'vue-i18n'
  import {
    useDbMapProviderTokenStore,
    useDbMapProviderTokenUsageStore,
    useDbMapProviderUsedQuotasStore,
    useDbProjectQuotasStore,
    useDbProjectStore,
    useDbProjectTokenCurrentUsageStore,
    useDbProjectTokenStore,
    useDbProjectTokenUsageStore, useDbUserStore,
  } from '@/stores/dataBase'
  import {
    type DbProjectQuotas,
    MapProviderEnum,
    type TAvailableMapProvider,
    TAvailableMapProviderSchema,
  } from '@/proto/db_pb'
  import type { DbMapProviderToken, DbMapProviderUsedQuotas, DbProject, DbProjectToken } from '@/proto/db_pb'
  import type { CallbackDataParams } from 'echarts/types/dist/shared'
  import { getCurrentUsageReq } from '@/services/connectRpc.ts'
  import { create, fromJsonString } from '@bufbuild/protobuf'
  import { GetCurrentUsageReqSchema } from '@/proto/bfmap.rpc_pb.ts'
  import type {
    OptionDataValue,
    OptionDataItemObject,
    OptionDataItem,
    LabelOption
  } from 'echarts/types/src/util/types.js'
  import { StrPubSub } from 'ypubsub'
  import { ToggleI18nLang } from '@/utils/pubSubSubject.ts'
  import { getQuotaTimeRange, requestDbProjectTokenUsage } from '@/services/request.ts'
  import { WhereOperator } from '@/proto/protodb_pb.ts'
  import { useQuasar } from 'quasar'
  import type { CategoryAxisBaseOption } from 'echarts/types/src/coord/axisCommonTypes.js'
  import { errorMessage, successMessage } from '@/utils/notify.ts'
  import { useLoginStatusStore } from '@/stores/session.ts'

  echarts.use([GraphicComponent])

  const $q = useQuasar()

  // 注册必须的组件
  use([CanvasRenderer, LineChart, PieChart, BarChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, DataZoomComponent])

  const { t } = useI18n()
  const dbProjectStore = useDbProjectStore()
  const { rows: dbProjectRows, rowsMap: dbProjectRowsMap } = storeToRefs(dbProjectStore)
  const dbProjectTokenStore = useDbProjectTokenStore()
  const { rows: dbProjectTokenRows } = storeToRefs(dbProjectTokenStore)
  const dbProjectQuotasStore = useDbProjectQuotasStore()
  const { updateRow: updateProjectQuotaStoreRow } = dbProjectQuotasStore
  const { rowsMap: dbProjectQuotasRowsMap } = storeToRefs(dbProjectQuotasStore)
  const dbProjectTokenCurrentUsageStore = useDbProjectTokenCurrentUsageStore()
  const {
    rows: dbProjectTokenCurrentUsageRows,
    rowsMap: dbProjectTokenCurrentUsageRowsMap
  } = storeToRefs(dbProjectTokenCurrentUsageStore)
  const dbMapProviderTokenStore = useDbMapProviderTokenStore()
  const { rows: dbMapProviderTokenRows } = storeToRefs(dbMapProviderTokenStore)
  const dbMapProviderUsedQuotasStore = useDbMapProviderUsedQuotasStore()
  const { updateRow: updateMapProviderUsedQuotasStoreRow } = dbMapProviderUsedQuotasStore
  const { rows: dbMapProviderUsedQuotasRows, rowsMap: dbMapProviderUsedQuotasRowsMap } =
      storeToRefs(dbMapProviderUsedQuotasStore)
  const dbProjectTokenUsageStore = useDbProjectTokenUsageStore()
  const { rows: dbProjectTokenUsageRows, rowsMap: dbProjectTokenUsageRowsMap } = storeToRefs(dbProjectTokenUsageStore)
  const dbMapProviderTokenUsageStore = useDbMapProviderTokenUsageStore()
  const { rows: dbMapProviderTokenUsageRows } = storeToRefs(dbMapProviderTokenUsageStore)
  const dbUserStore = useDbUserStore()
  const { rowsMap: dbUserRowsMap } = storeToRefs(dbUserStore)
  const loginStatusStore = useLoginStatusStore()
  const barChart = ref()
  const pieChart = ref()
  const mapBarChart = ref()
  const mapPieChart = ref()

  const isMobile = computed(() => {
    return $q.platform.is.mobile
  })

  const titleStyle: LabelOption = {
    width: $q.screen.width > 400 ? 'auto' : 270 ,
    overflow: 'breakAll',
  }

  // 刷新项目用量 get project current usage
  const refreshProjectUsage = async (notify?: boolean) => {
    const task = []
    for (let i = 0; i < finalProjectRows.value.length; i++) {
      const row = finalProjectRows.value[i]
      // 已删除或禁用的项目不需要去刷新
      if (row.Status !== 1) continue
      const currentUsageReq = reactive(
          create(GetCurrentUsageReqSchema, {
            Code: 2,
            ProjectRid: row.Rid,
          })
      )
      const isOk = getCurrentUsageReq(currentUsageReq).then(res => {
        if (res.Code == 0) {
          const quota = dbProjectQuotasRowsMap.value.get(row.Rid) as DbProjectQuotas
          quota.CurrentUsedQuotas = res.CurrentUsage
          updateProjectQuotaStoreRow(quota)
          return true
        }
        return false
      })
      task.push(isOk)
    }

    const okTask = await Promise.all(task)
    const ok = okTask.every(item => item)
    if (notify) {
      if (ok) {
        successMessage(t('dashboard.refreshSuccess'))
      } else {
        errorMessage(t('dashboard.refreshFailed'))
      }
    }
  }

  // 刷新provider api usage ，get map provider token current usage
  const refreshMapProvider = async (notify?: boolean) => {
    const task = []
    for (let i = 0; i < finalMapProviderTokenRows.value.length; i++) {
      const row = finalMapProviderTokenRows.value[i]
      if (row.Status !== 1 || row.UserRid !== loginStatusStore.userRid) continue
      const currentUsageReq = reactive(
          create(GetCurrentUsageReqSchema, {
            Code: 1,
            UserRid: row.UserRid,
            Provider: row.Provider,
            ProviderToken: row.Token,
          })
      )
      const ok = getCurrentUsageReq(currentUsageReq).then(res => {
        if (res.Code == 0 && res.CurrentUsage > 0) {
          const quota = dbMapProviderUsedQuotasRowsMap.value.get(row.Rid) as DbMapProviderUsedQuotas
          quota.UsedQuotas = res.CurrentUsage
          updateMapProviderUsedQuotasStoreRow(quota)
          return true
        }
        return res.Code == 0
      })
      task.push(ok)
    }

    const okTask = await Promise.all(task)
    const ok = okTask.every(item => item)
    if (notify) {
      if (ok) {
        successMessage(t('dashboard.refreshSuccess'))
      } else {
        errorMessage(t('dashboard.refreshFailed'))
      }
    }
  }

  const finalProjectRows = computed(() => {
    return dbProjectRows.value.filter(row => row.Status !== 8)
  })

  const finalMapProviderTokenRows = computed(() => {
    return dbMapProviderTokenRows.value.filter(row => row.Status !== 8)
  })
  watch(dbMapProviderUsedQuotasRowsMap, () => {
    refreshMapProvider()
  })

  watch(dbProjectQuotasRowsMap, () => {
    refreshProjectUsage()
  })

  const graphic = computed(() => {
    return [
      {
        type: 'text',
        left: 'center',
        top: 'center',
        cursor: 'default',
        style: {
          text: t('dashboard.usageNoDetail'),
          fontSize: 18,
        },
      },
    ]
  })

  // 获取指定项目下token的使用量
  const getProjectTokenUsageFormServer = async (project: DbProject, option: EChartsOption): Promise<EChartsOption> => {
    const seriesData = option.series as Array<BarSeriesOption>
    const data = seriesData[0].data as Array<
        OptionDataValue & {
      name: string
      project: DbProject
      token: DbProjectToken
      value: number
    }
    >
    const projectTokens = dbProjectTokenRows.value.filter(row => row.ProjectRid === project.Rid)

    const quota = dbProjectQuotasRowsMap.value.get(project.Rid) as DbProjectQuotas
    const currentUsageTasks = projectTokens.map(token => {
      const { startTime, endTime } = getQuotaTimeRange(quota.QuotasType)
      return requestDbProjectTokenUsage(
          {
            ProjectRid: quota.Rid,
            StartTime: startTime.toString(),
            EndTime: endTime.toString(),
            Token: token.Token,
          },
          {
            ProjectRid: WhereOperator.WOP_EQ,
            StartTime: WhereOperator.WOP_GTE,
            EndTime: WhereOperator.WOP_LT,
            Token: WhereOperator.WOP_EQ,
          }
      ).then(result => {
        const { addRow, updateRow } = useDbProjectTokenCurrentUsageStore()
        result.forEach(row => {
          const exist = !!dbProjectTokenCurrentUsageRowsMap.value.get(row.Rid)
          if (exist) {
            updateRow(row)
          } else {
            addRow(row)
          }
        })
      })
    })

    const tasks = projectTokens.map(token => {
      const currentUsageReq = reactive(
          create(GetCurrentUsageReqSchema, {
            Code: 3,
            ProjectToken: token.Token,
          })
      )
      return getCurrentUsageReq(currentUsageReq).then(res => {
        if (res.Code == 0 && res.CurrentUsage > 0) {
          const index = data.findIndex(d => d.name === token.Name)
          if (index !== -1) {
            data[index].value += res.CurrentUsage
          } else {
            data.push({
              name: token.Name,
              value: res.CurrentUsage,
              token,
              project,
            } as OptionDataValue & {
              name: string
              project: DbProject
              token: DbProjectToken
              value: number
            })
          }
        }
      })
    })
    await Promise.all(currentUsageTasks)
    await Promise.all(tasks)
    seriesData[0].data = data
    ;(option.xAxis as CategoryAxisBaseOption).data = data.map(d => d.name)
    if (seriesData && seriesData.length === 0) {
      option.graphic = graphic.value
    }
    return option
  }

  const getProjectTokenAllUsageFromServe = async (project: DbProject) => {
    const tokens = dbProjectTokenRows.value.filter(row => row.ProjectRid === project.Rid)
    const tasks = tokens.map(token => {
      return requestDbProjectTokenUsage(
          {
            Token: token.Token,
          },
          {
            Token: WhereOperator.WOP_EQ,
          }
      ).then(rows => {
        const { addRow, updateRow } = useDbProjectTokenUsageStore()
        rows.forEach(row => {
          const exist = !!dbProjectTokenUsageRowsMap.value.get(row.Rid)
          if (exist) {
            updateRow(row)
          } else {
            addRow(row)
          }
        })
      })
    })

    await Promise.all(tasks)
  }

  const getUserName = (rid: string) => {
    const user = dbUserRowsMap.value.get(rid)
    return user?.Name ?? rid
  }

  function useDbProjectQuotasChart() {
    // 项目当前用量Option
    const projectOption = computed(() => {
      const data: any[] = []
      for (let i = 0; i < finalProjectRows.value.length; i++) {
        const project = finalProjectRows.value[i]
        const projectQuota = dbProjectQuotasRowsMap.value.get(project.Rid)
        data.push({
          id: project.Rid,
          name: project.Name === 'Default' ? getUserName(project.UserRid) : project.Name,
          value: projectQuota?.CurrentUsedQuotas ?? 0,
          project,
        })
      }
      const option: EChartsOption = {
        id: 'projectCurrentUsageOption',
        title: {
          text: t('dashboard.projectCurrentUsage'),
          textStyle: titleStyle,
          left: 'center',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '35',
          containLabel: true
        },
        xAxis: {
          triggerEvent: true,
          type: 'category',
          data: finalProjectRows.value.map(project => 
            project.Name === 'Default' ? getUserName(project.UserRid) : project.Name
          ),
          axisLabel: {
            interval: 0,
            rotate: 35
          }
        },
        yAxis: [
          {
            type: 'value',
            name: t('dashboard.currentUsage'),
          },
        ],
        animationDurationUpdate: 500,
        series: [
          {
            colorBy: 'data',
            type: 'bar',
            barMaxWidth: '20%',
            data,
            large: true
          },
        ],
        tooltip: {
          trigger: 'axis',
          confine: true,
          extraCssText: 'white-space: normal !important;word-break:break-all;',
          formatter: (params: CallbackDataParams | CallbackDataParams[]) => {
            const option = Array.isArray(params) ? params[0] : params
            const data = option.data as OptionDataValue & {
              project: DbProject
              value: number
            }
            const alreadyUsedToken = new Set(
                dbProjectTokenCurrentUsageRows.value
                    .filter(row => row.ProjectRid === data.project.Rid)
                    .map(row => row.Token)
            )
            const projectQuota = dbProjectQuotasRowsMap.value.get(data.project.Rid)
            return `
                  <div class="flex flex-col gap-1">
                    <div>${option.marker}<span class="mr-2">${t('dashboard.currentUsage')}: </span>${data.value}</div>
                    <div><span class="mr-2">${t('dashboard.alreadyUsedAPICount')}: </span>${alreadyUsedToken.size}</div>
                    <div><span class="mr-2">${t('dbMapProviderToken.quotaCalcMethod')}: </span>${getQuotasTypeLabel(
                projectQuota?.QuotasType ?? 0
            )}</div>
                    <div><span class="mr-2">${t('dbProject.creator')}:</span>${getUserName(data.project.UserRid)}</div>
                    <div><span class="mr-2">${t('dashboard.clickToDetail')}</span></div>
                  </div>`
          },
        },
      }
      if (finalProjectRows.value.length > 15) {
        option.dataZoom =  [
          {
            type: 'inside',
            startValue: 0,
            endValue: 14,
          },
          {
            type: 'slider'
          }
        ]
      }
      return option
    })

    // 项目总用量option
    const projectAllUsageOption = computed(() => {
      const data: any[] = []
      for (let i = 0; i < finalProjectRows.value.length; i++) {
        const project = finalProjectRows.value[i]
        const projectAllUsage = dbProjectTokenUsageRows.value
            .filter(row => row.ProjectRid === project.Rid)
            .map(r => r.UsageCount)
            .reduce((a, b) => a + b, 0)
        data.push({
          id: project.Rid,
          value: projectAllUsage,
          name: project.Name === 'Default' ? getUserName(project.UserRid) : project.Name,
          project,
        })
      }
      const option: EChartsOption = {
        id: 'projectAllUsageOption',
        title: {
          text: t('dashboard.projectAllUsage'),
          textStyle: titleStyle,
          left: 'center',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '35',
          containLabel: true
        },
        xAxis: {
          triggerEvent: true,
          type: 'category',
          data: finalProjectRows.value.map(project => 
            project.Name === 'Default' ? getUserName(project.UserRid) : project.Name
          ),
          axisLabel: {
            interval: 0,
            rotate: 35
          }
        },
        yAxis: [
          {
            type: 'value',
            name: t('dashboard.totalUsage'),
          },
        ],
        animationDurationUpdate: 500,
        series: [
          {
            colorBy: 'data',
            type: 'bar',
            barMaxWidth: '20%',
            data,
            large: true
          },
        ],
        tooltip: {
          trigger: 'axis',
          confine: true,
          extraCssText: 'white-space: normal !important;word-break:break-all;',
          formatter: (params: CallbackDataParams | CallbackDataParams[]) => {
            const option = Array.isArray(params) ? params[0] : params
            const data = option.data as OptionDataValue & {
              project: DbProject
              value: number
            }
            const projectQuota = dbProjectQuotasRowsMap.value.get(data.project.Rid)
            return `
              <div class="flex flex-col gap-1 max-w-[86vw]">
              <div>${option.marker}<span class="mr-2">${t('dashboard.totalUsage')}: </span>${data.value}</div>
              <div><span class="mr-2">${t('dbProject.name')}: </span>${data.project.Name}</div>
              <div><span class="mr-2">${t('dbMapProviderToken.quotaCalcMethod')}: </span>${getQuotasTypeLabel(
                          projectQuota?.QuotasType ?? 0
                      )}</div>
                        <div><span class="mr-2">${t('dbProject.creator')}:</span>${getUserName(data.project.UserRid)}</div>
                      </div>
              `

          },
        },
      }
      if (finalProjectRows.value.length > 15) {
        option.dataZoom =  [
          {
            type: 'inside',
            startValue: 0,
            endValue: 14,
          },
          {
            type: 'slider'
          }
        ]
      }
      return option
    })

    // 项目下token详细用量
    type EchartsData = string | number | DbProject | DbProjectToken
    const projectTokenCurrentUsageData = computed<Record<string, Array<EchartsData[]>>>(() => {
      const data: any[] = []
      const alreadyUsedToken = dbProjectTokenRows.value.filter(row =>
          dbProjectTokenCurrentUsageRows.value.find(usageRow => row.Token === usageRow.Token)
      )
      for (let i = 0; i < alreadyUsedToken.length; i++) {
        const token = alreadyUsedToken[i]
        const project = dbProjectRowsMap.value.get(token.ProjectRid)
        const tokenUsageRows = dbProjectTokenCurrentUsageRows.value.filter(usageRow => usageRow.Token === token.Token)
        const usageCount = tokenUsageRows.map(row => row.UsageCount).reduce((a, b) => a + b, 0)
        if (usageCount <= 0) continue
        data.push({
          name: token.Name,
          value: usageCount,
          token,
          project,
        })
      }

      const tokenDataMaps: Record<string, Array<EchartsData[]>> = {}
      for (let i = 0; i < finalProjectRows.value.length; i++) {
        const project = finalProjectRows.value[i]
        tokenDataMaps[project.Rid] = data.filter(d => d.project.Rid === project.Rid)
      }

      return tokenDataMaps
    })

    // 项目下token详细总用量
    const projectTokenUsageData = computed<Record<string, Array<EchartsData[]>>>(() => {
      const data: any[] = []
      const alreadyUsedToken = dbProjectTokenRows.value.filter(row =>
          dbProjectTokenUsageRows.value.find(usageRow => row.Token === usageRow.Token)
      )
      for (let i = 0; i < alreadyUsedToken.length; i++) {
        const token = alreadyUsedToken[i]
        const project = dbProjectRowsMap.value.get(token.ProjectRid)
        const tokenUsageRows = dbProjectTokenUsageRows.value.filter(usageRow => usageRow.Token === token.Token)
        const usageCount = tokenUsageRows.map(row => row.UsageCount).reduce((a, b) => a + b, 0)
        if (usageCount <= 0) continue
        data.push({
          name: token.Name,
          value: usageCount,
          token,
          project,
        })
      }

      const tokenDataMaps: Record<string, Array<EchartsData[]>> = {}
      for (let i = 0; i < finalProjectRows.value.length; i++) {
        const project = finalProjectRows.value[i]
        tokenDataMaps[project.Rid] = data.filter(d => d.project.Rid === project.Rid)
      }

      return tokenDataMaps
    })

    // project.Rid: EChartsOption
    const projectAllTokenCurrentUsageOptions = computed<Record<string, EChartsOption>>(() => {
      const options: Record<string, EChartsOption> = {}
      for (let i = 0; i < finalProjectRows.value.length; i++) {
        const project = finalProjectRows.value[i]
        const tokenUsageData: any[] = projectTokenCurrentUsageData.value[project.Rid] ?? []
        const option: EChartsOption = {
          id: 'tokenCurrentUsage',
          title: {
            text: t('dashboard.tokenCurrentUsage', { name: project.Name === 'Default' ? getUserName(project.UserRid) : project.Name }),
            textStyle: titleStyle,
            left: 'center',
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: tokenUsageData.map(t => t.name),
            axisLabel: {
              interval: 0,
              rotate: 35
            },
          },
          yAxis: [
            {
              type: 'value',
              name: t('dashboard.currentUsage'),
              min: 0,
              max: tokenUsageData.length === 0 ? 8 : undefined,
            },
          ],
          animationDurationUpdate: 500,
          series: [
            {
              colorBy: 'data',
              type: 'bar',
              barWidth: '20%',
              data: tokenUsageData,
            },
          ],
          tooltip: {
            trigger: 'axis',
            confine: true,
            extraCssText: 'white-space: normal !important;word-break:break-all;',
            formatter: (params: CallbackDataParams | CallbackDataParams[]) => {
              const option = Array.isArray(params) ? params[0] : params
              const data = option.data as OptionDataValue & {
                project: DbProject
                token: DbProjectToken
                value: number
              }
              if (!data || !data.token) {
                return ''
              }
              const tAvailableMapProvider = fromJsonString(TAvailableMapProviderSchema, data.token.AvailableMapProvider, {
                ignoreUnknownFields: true
              })
              const availableMapPlatforms = Object.keys(tAvailableMapProvider).filter(
                  key => tAvailableMapProvider[key as keyof TAvailableMapProvider] === true
              )
              return `
                  <div class="flex flex-col gap-1 max-w-[86vw]">
                    <div>${option.marker}<span class="mr-2">${t('dashboard.currentUsage')}: </span>${data.value}</div>
                    <div><span class="mr-2">${t('dbMapProviderToken.apiName')}: </span>${data.token.Name}</div>
                    <div><span class="mr-2">${t('dashboard.availableMapPlatforms')}: </span>${getAvailableMap(availableMapPlatforms)}</div>
                    <div><span class="mr-2">${t('dbProjectToken.defaultRoadmap')}: </span>${getMapName(tAvailableMapProvider.DefaultRoadmap)}</div>
                    <div><span class="mr-2">${t('dbProjectToken.defaultSatellite')}: </span>${getMapName(tAvailableMapProvider.DefaultSatellite)}</div>
                    <div><span class="mr-2">${t('dbProjectToken.defaultHybrid')}: </span>${getMapName(tAvailableMapProvider.DefaultHybrid)}</div>
                  </div>`
            },
          },
        }
        if (tokenUsageData.length > 15) {
          option.dataZoom = [
            {
              type: 'inside',
              startValue: 0,
              endValue: 14,
            },
            {
              type: 'slider'
            }
          ]
        }
        options[project.Rid] = option
      }

      return options
    })

    // project.Rid: EChartsOption
    const projectAllTokenUsageOptions = computed<Record<string, EChartsOption>>(() => {
      const options: Record<string, EChartsOption> = {}
      for (let i = 0; i < finalProjectRows.value.length; i++) {
        const project = finalProjectRows.value[i]
        const tokenUsageData: any[] = projectTokenUsageData.value[project.Rid] ?? []
        const option: EChartsOption = {
          id: 'tokenCurrentUsage',
          title: {
            text: t('dashboard.projectTokenAllUsage', { name: project.Name === 'Default' ? getUserName(project.UserRid) : project.Name }),
            left: 'center',
            textStyle: titleStyle,
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: tokenUsageData.map(t => t.name),
            axisLabel: {
              interval: 0,
              rotate: 35
            },
          },
          yAxis: [
            {
              type: 'value',
              name: t('dashboard.totalUsage'),
              min: 0,
              max: tokenUsageData.length === 0 ? 8 : undefined,
            },
          ],
          animationDurationUpdate: 500,
          series: [
            {
              colorBy: 'data',
              type: 'bar',
              barWidth: '20%',
              data: tokenUsageData,
            },
          ],
          tooltip: {
            trigger: 'axis',
            confine: true,
            extraCssText: 'white-space: normal !important;word-break:break-all;',
            formatter: (params: CallbackDataParams | CallbackDataParams[]) => {
              const option = Array.isArray(params) ? params[0] : params
              const data = option.data as OptionDataValue & {
                project: DbProject
                token: DbProjectToken
                value: number
              }
              if (!data || !data.token) {
                return ''
              }
              const tAvailableMapProvider = fromJsonString(TAvailableMapProviderSchema, data.token.AvailableMapProvider, {
                ignoreUnknownFields: true
              })
              const availableMapPlatforms = Object.keys(tAvailableMapProvider).filter(
                  key => tAvailableMapProvider[key as keyof TAvailableMapProvider] === true
              )
              return `
                <div class="flex flex-col gap-1 max-w-[86vw]">
                  <div>${option.marker}<span class="mr-2">${t('dashboard.totalUsage')}: </span>${data.value}</div>
                  <div><span class="mr-2">${t('dbMapProviderToken.apiName')}: </span>${data.token.Name}</div>
                   <div><span class="mr-2">${t('dashboard.availableMapPlatforms')}: </span>${getAvailableMap(availableMapPlatforms)}</div>
                   <div><span class="mr-2">${t('dbProjectToken.defaultRoadmap')}: </span>${getMapName(tAvailableMapProvider.DefaultRoadmap)}</div>
                   <div><span class="mr-2">${t('dbProjectToken.defaultSatellite')}: </span>${getMapName(tAvailableMapProvider.DefaultSatellite)}</div>
                   <div><span class="mr-2">${t('dbProjectToken.defaultHybrid')}: </span>${getMapName(tAvailableMapProvider.DefaultHybrid)}</div>
                </div>
              `
            },
          },
        }
        if (tokenUsageData.length > 15) {
          option.dataZoom = [
            {
              type: 'inside',
              startValue: 0,
              endValue: 14,
            },
            {
              type: 'slider'
            }
          ]
        }
        options[project.Rid] = option
      }

      return options
    })

    const usageOptionStack = ref<Array<string>>([])
    const allUsageOptionStack = ref<Array<string>>([])

    const barChartSetOption = async (project: DbProject) => {
      if (usageOptionStack.value.length > 0) {
        return
      }
      const option = projectAllTokenCurrentUsageOptions.value[project.Rid]
      const option2 = await getProjectTokenUsageFormServer(project, option)
      if (!(option2.series as Array<BarSeriesOption>)[0].data) return
      usageOptionStack.value.push(barChart.value?.getOption().id as string)
      barChart.value?.setOption(option2, { notMerge: true })
    }

    const pieChartSetOption = async (project: DbProject) => {
      if (allUsageOptionStack.value.length > 0) {
        return
      }
      // 重新从服务器获取一次该项目下token的总用量
      await getProjectTokenAllUsageFromServe(project)
      const option = projectAllTokenUsageOptions.value[project.Rid]
      allUsageOptionStack.value.push(pieChart.value?.getOption().id as string)
      pieChart.value?.setOption(option, { notMerge: true })
    }

    const goBack = () => {
      if (usageOptionStack.value.length !== 0 || allUsageOptionStack.value.length !== 0) {
        usageOptionStack.value.pop()
        allUsageOptionStack.value.pop()
        barChart.value?.setOption(projectOption.value, { notMerge: true })
        pieChart.value?.setOption(projectAllUsageOption.value, { notMerge: true })
      }
    }

    const chartClick = (params: CallbackDataParams) => {
      const data = params.data as OptionDataItemObject<number> & {
        project: DbProject
      }
      const project = data?.project
      if (project) {
        barChartSetOption(project)
        pieChartSetOption(project)
      }
    }

    return { projectOption, projectAllUsageOption, allUsageOptionStack, usageOptionStack, chartClick, goBack }
  }

  const {
    projectOption,
    projectAllUsageOption,
    allUsageOptionStack,
    usageOptionStack,
    chartClick,
    goBack
  } = useDbProjectQuotasChart()

  function useDbMapProviderQuotasChart() {
    const mapProviderTokenUsageOption = computed(() => {
      const xAxisData: string[] = []
      const seriesData: any[] = []
      for (let i = 0; i < finalMapProviderTokenRows.value.length; i++) {
        const provider = finalMapProviderTokenRows.value[i]
        const quotas = dbMapProviderUsedQuotasRows.value.find(row => row.Rid === provider.Rid)
        if (provider.Status === 8 || !quotas) continue
        xAxisData.push(provider.Name)
        seriesData.push({
          value: quotas.UsedQuotas,
          provider,
          quotas,
        })
      }

      const option: EChartsOption = {
        title: {
          text: t('dashboard.mapApiQuotasChartTitle'),
          textStyle: titleStyle,
          left: 'center',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '35',
          containLabel: true
        },
        xAxis: {
          triggerEvent: true,
          type: 'category',
          data: xAxisData,
          axisLabel: {
            interval: 0,
            rotate: 35
          }
        },
        yAxis: [
          {
            type: 'value',
            name: t('dashboard.currentUsage'),
          },
        ],
        series: [
          {
            type: 'bar',
            data: seriesData,
            barWidth: '20%',
            colorBy: 'data',
            large: true,
          },
        ],
        tooltip: {
          trigger: 'axis',
          confine: true,
          extraCssText: 'white-space: normal !important;word-break:break-all;',
          formatter: (params: CallbackDataParams | CallbackDataParams[]) => {
            const option = Array.isArray(params) ? params[0] : params
            const data = (option.data ?? {}) as OptionDataItem & {
              provider: DbMapProviderToken
              quotas: DbMapProviderUsedQuotas
            }
            const provider = data.provider
            const quotas = data.quotas
            return `
                  <div class="flex flex-col gap-1 max-w-[86vw]">
                    <div>${option.marker}<span class="mr-2">${t('dashboard.usage')}: </span>${quotas.UsedQuotas}</div>
                    <div><span class="mr-2">${t('dbMapProviderToken.apiName')}: </span>${provider.Name}</div>
                    <div><span class="mr-2">${t('dbMapProviderToken.mapPlatform')}: </span>${getMapPlatform(
                provider.Provider
            )}</div>
          <div><span class="mr-2">${t('dbMapProviderToken.quotaCalcMethod')}: </span>${getQuotasTypeLabel(
                provider.QuotasType
            )}</div>
                    <div><span class="mr-2">${t('dbProject.creator')}:</span>${getUserName(provider.UserRid)}</div>
                  </div>`
          },
        },
      }
      if (seriesData.length > 15) {
        option.dataZoom = [
          {
            type: 'inside',
            startValue: 0,
            endValue: 14,
          },
          {
            type: 'slider'
          }
        ]
      }
      return option
    })

    const mapProviderTokenAllUsageOption = computed(() => {
      const data: any[] = []
      const xAxisData: string[] = []
      for (let i = 0; i < finalMapProviderTokenRows.value.length; i++) {
        const mapToken = finalMapProviderTokenRows.value[i]
        const quotas = dbMapProviderUsedQuotasRows.value.find(row => row.Rid === mapToken.Rid)
        if (mapToken.Status === 8 || !quotas) continue
        const usageRows = dbMapProviderTokenUsageRows.value.filter(usageRow => usageRow.ProviderRid === mapToken.Rid)
        const usageCount = usageRows.map(row => row.UsageCount).reduce((a, b) => a + b, 0)
        xAxisData.push(mapToken.Name)
        data.push({
          name: mapToken.Name,
          value: usageCount,
          mapToken,
        })
      }
      const option: EChartsOption = {
        id: 'tokenAllUsage',
        title: {
          text: t('dashboard.mapApiQuotasChartAllUsageTitle'),
          textStyle: titleStyle,
          left: 'center',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '35',
          containLabel: true
        },
        xAxis: {
          triggerEvent: true,
          type: 'category',
          data: xAxisData,
          axisLabel: {
            interval: 0,
            rotate: 35
          }
        },
        yAxis: [
          {
            type: 'value',
            name: t('dashboard.currentUsage'),
          },
        ],
        series: [
          {
            type: 'bar',
            data: data,
            barWidth: '20%',
            colorBy: 'data',
            large: true,
          },
        ],
        tooltip: {
          trigger: 'axis',
          confine: true,
          extraCssText: 'white-space: normal !important;word-break:break-all;',
          formatter: (params: CallbackDataParams | CallbackDataParams[]) => {
            const option = Array.isArray(params) ? params[0] : params
            const data = (option.data ?? {}) as OptionDataItem & {
              mapToken: DbMapProviderToken
              quotas: DbMapProviderUsedQuotas
              value: number
            }
            const provider = data.mapToken
            return `
                <div class="flex flex-col gap-1 max-w-[86vw]">
                  <div>${option.marker}<span class="mr-2">${t('dashboard.totalUsage')}: </span>${data.value}</div>
                  <div><span class="mr-2">${t('dbMapProviderToken.apiName')}: </span>${provider.Name}</div>
                  <div><span class="mr-2">${t('dbMapProviderToken.mapPlatform')}: </span>${getMapPlatform(provider.Provider)}</div>
                  <div><span class="mr-2">${t('dbMapProviderToken.quotaCalcMethod')}: </span>${getQuotasTypeLabel(provider.QuotasType)}</div>
                  <div><span class="mr-2">${t('dbProject.creator')}:</span>${getUserName(provider.UserRid)}</div>
                </div>`
          },
        },
      }
      if (data.length > 15) {
        option.dataZoom = [
          {
            type: 'inside',
            startValue: 0,
            endValue: 14,
          },
          {
            type: 'slider'
          }
        ]

      }
      return option
    })

    return { mapProviderTokenUsageOption, mapProviderTokenAllUsageOption }
  }

  const { mapProviderTokenUsageOption, mapProviderTokenAllUsageOption } = useDbMapProviderQuotasChart()

  const availableMapProviderOption = computed(() => {
    return [
      {
        label: t('dbProjectToken.googleMap'),
        value: MapProviderEnum.ProviderGoogle,
      },
      {
        label: t('dbProjectToken.tianditu'),
        value: MapProviderEnum.ProviderTianditu,
      },
      {
        label: t('dbProjectToken.openStreetMap'),
        value: MapProviderEnum.ProviderOSM,
      },
    ]
  })

  const availableMapStringOption = computed(() => {
    return [
      {
        label: t('dbProjectToken.googleMap'),
        value: 'Google',
      },
      {
        label: t('dbProjectToken.tianditu'),
        value: 'Tianditu',
      },
      {
        label: t('dbProjectToken.openStreetMap'),
        value: 'OSM',
      },
    ]
  })

  const getMapPlatform = (provider: MapProviderEnum) => {
    return availableMapProviderOption.value.find(item => item.value === provider)?.label ?? MapProviderEnum[provider]
  }

  const getAvailableMap = (mapPlatformsValue: Array<string>): string => {
    return mapPlatformsValue.map(value => availableMapStringOption.value.find(item => item.value === value)?.label ?? value).join(',')
  }

  const getMapName = (mapEnum: MapProviderEnum): string => {
    return availableMapProviderOption.value.find(item => item.value === mapEnum)?.label ?? MapProviderEnum[mapEnum]
  }

  const getQuotasTypeLabel = (quotasType: number): string => {
    switch (quotasType) {
      case 1:
        return t('dbProject.quotasCalculateBy', { type: t('dbProject.minute') })
      case 2:
        return t('dbProject.quotasCalculateBy', { type: t('dbProject.hour') })
      case 3:
        return t('dbProject.quotasCalculateBy', { type: t('dbProject.day') })
      case 4:
        return t('dbProject.quotasCalculateBy', { type: t('dbProject.month') })
      default:
        return '' + quotasType
    }
  }

  onMounted(() => {
    StrPubSub.subscribe(ToggleI18nLang, () => {
      usageOptionStack.value.pop()
      allUsageOptionStack.value.pop()
    })
  })
</script>

<style scoped lang="scss"></style>
