<template>
  <q-page>
    <q-table
        style="height: calc(100vh - 50px)"
        ref="tableRef"
        binary-state-sort
        bordered
        flat
        virtual-scroll
        row-key="Rid"
        selection="multiple"
        :rows="dbOrgRows"
        :columns="columns"
        :filter="filter"
        v-model:selected="selected"
        v-model:pagination="pagination"
        :rows-per-page-options="[0]"
        @selection="handleSelection"
        class="!border-x-0 !border-t-0 !rounded-none sticky-header-table"
    >
      <template v-slot:top>
        <q-btn
            color="primary"
            :label="$t('form.add')"
            @click="openDialogBeforeAction(OpenDialogAction.Add)"
        />
        <q-btn
            class="q-ml-sm"
            color="negative"
            :label="$t('form.batchDelete')"
            :disable="selected.length <= 0"
            @click="batchDeleteRows(selected)"
        />
        <q-space />
        <q-input
            dense
            debounce="300"
            v-model="filter"
            :placeholder="$t('form.search')"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:header-selection="scope">
        <q-checkbox v-model="scope.selected" @update:model-value="selectNowPageAll">
          <q-tooltip
              anchor="top middle"
              :offset="[10, 20]"
          >
            {{ $t('dbUser.selectTooltip') }}
          </q-tooltip>
        </q-checkbox>
      </template>

      <template v-slot:body-selection="scope">
        <q-checkbox
            :disable="scope.row.Rid === loginStatusStore.userOrgRid"
            :model-value="scope.selected"
            @update:model-value="(val, evt) => {
            // @ts-ignore
            Object.getOwnPropertyDescriptor(scope, 'selected').set(val, evt)
          }"
        />
      </template>

      <template v-slot:body-cell-Note="props">
        <q-td auto-width align="center">
          <OverflowTd :value="props.row.Note"></OverflowTd>
        </q-td>
      </template>

      <template v-slot:body-cell-operate="props">
        <q-td auto-width>
          <q-btn
              round
              outline
              icon="edit"
              size="xs"
              color="primary"
              @click="openDialogBeforeAction(OpenDialogAction.Edit, props.row)"
          >
            <q-tooltip>
              {{ $t('form.edit') }}
            </q-tooltip>
          </q-btn>
          <q-btn
              round
              outline
              icon="delete"
              size="xs"
              color="negative"
              class="!ml-2"
              :disable="props.row.Rid === loginStatusStore.userOrgRid"
          >
            <q-tooltip>
              {{ $t('form.delete') }}
            </q-tooltip>
            <popup-proxy-confirm
                :message="$t('dbOrg.wantToDeleteOrg')"
                @confirm="deleteDbOrg(props.row)"
            />
          </q-btn>
        </q-td>
      </template>

      <template v-slot:no-data>
        <div class="full-width flex justify-center">
          <q-icon
              name="warning"
              size="xs"
          ></q-icon>
          <span>{{ $t('dbUser.noData') }}</span>
        </div>
      </template>
    </q-table>

    <q-dialog v-model="visible" persistent>
      <q-card class="w-fit !max-w-none p-4 pt-2">
        <!--header-->
        <q-bar class="bg-white !px-2 mt-2">
          <q-icon name="people" size="24px" color="primary"/>
          <span class="pl-2">{{ `${$t('dbOrg.org')}-${dialogAction === OpenDialogAction.Add ? $t('form.add') : $t('form.edit')}` }}</span>
          <q-space></q-space>
          <q-btn flat round dense icon="close" size="sm" v-close-popup/>
        </q-bar>

        <!--body-->
        <q-card-section class="w-fit !p-2">
          <q-form class="fit gap-x-4 user-form" :class="{ 'min-w-xl': !isMobile }" ref="formRef">
            <q-input
                v-model="orgData.Name"
                :label="$t('dbOrg.name')"
                outlined
                dense
                clearable
                autofocus
                lazy-rules
                :maxlength="16"
                :rules="[rules.required, rules.nameUnique]"
            />
            <q-input
                type="textarea"
                v-model="orgData.Note"
                :label="$t('dbUser.note')"
                outlined
                dense
                clearable
                autogrow
                counter
                lazy-rules
                :maxlength="256"
                :rules="[]"
                v-clear-fix="[orgData, 'Note']"
            />

            <div class="flex justify-center gap-2 mt-4 col-span-2">
              <q-btn
                  color="primary"
                  class="!w-[150px]"
                  :label="dialogAction === OpenDialogAction.Add ? $t('form.add') : $t('form.confirm')"
                  @click="submit(false)"
              />
              <q-btn
                  v-if="dialogAction === OpenDialogAction.Add"
                  color="secondary"
                  class="!w-[150px]"
                  :label="$t('form.keepAdd')"
                  @click="submit(true)"
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
  import { useDbOrgStore, useDbUserStore } from '@/stores/dataBase.ts'
  import { useI18n } from 'vue-i18n'
  import { QForm, QPage, QTable, type QTableColumn, useQuasar } from 'quasar'
  import { computed, defineAsyncComponent, ref } from 'vue'
  import { type DbOrg } from '@/proto/db_pb.ts'
  import { DbOrgSchema } from '@/proto/db_pb.ts'
  import { v7 as uuidV7 } from 'uuid'
  import { create } from '@bufbuild/protobuf'
  import { formatDayjs, getTimestamp } from '@/utils/dayjs.ts'
  import { storeToRefs } from 'pinia'
  import { OpenDialogAction } from '@/utils/types'
  import { useLoginStatusStore } from '@/stores/session.ts'
  import * as validation from '@/utils/validation.ts'
  import cloneDeep from 'lodash/cloneDeep'
  import { deleteRow, insertRow, updateRow } from '@/services/connectRpc.ts'
  import { errorMessage, successMessage } from '@/utils/notify.ts'
  import { confirmAgainBatchDelete, getNextName, useTableHandleSelection } from '@/utils/common.ts'
  import OverflowTd from '@/components/OverflowTd.vue'

  const PopupProxyConfirm = defineAsyncComponent(() => import('@/components/PopupProxyConfirm.vue'))

  const { t } = useI18n()
  const $q = useQuasar()
  const dbOrgStore = useDbOrgStore()
  const loginStatusStore = useLoginStatusStore()
  const dbUserStore = useDbUserStore()
  const { rows: dbUserRows } = storeToRefs(dbUserStore)
  const { rows: dbOrgRows } = storeToRefs(dbOrgStore)
  const { addRow, updateRow: updateOrgStoreRow, deleteRow: deleteOrgStoreRow } = dbOrgStore
  const getDefaultDbOrg = (): DbOrg => {
    return create(DbOrgSchema, {
      Rid: uuidV7(),
      Name: '',
      CreateTime: getTimestamp(),
      Note: '',
      Setting: '{}',
      OwnerRid: loginStatusStore.userRid,
    })
  }
  const filter = ref('')
  const { tableRef, selected, handleSelection } = useTableHandleSelection<DbOrg>()
  const pagination = ref({
    rowsPerPage: 0
  })
  const dialogAction = ref<OpenDialogAction>(OpenDialogAction.Add)
  const visible = ref(false)
  const formRef = ref<QForm>()

  const isMobile = computed(() => {
    return $q.screen.lt.sm
  })

  const orgData = ref<DbOrg>(getDefaultDbOrg())

  const columns = computed<QTableColumn[]>(() => {
    return [
      {
        name: 'Name',
        label: t('dbOrg.name'),
        align: 'center',
        field: 'Name',
      },
      {
        name: 'OwnerRid',
        label: t('dbOrg.owner'),
        align: 'center',
        field: 'OwnerRid',
        format: (val: string) => {
          return dbUserRows.value.find(row => row.Rid === val)?.Name ?? val
        }
      },
      {
        name: 'CreateTime',
        label: t('dbUser.createTime'),
        align: 'center',
        field: 'CreateTime',
        format: (value: bigint) => {
          return formatDayjs(Number(value) * 1000)
        },
      },
      {
        name: 'Note',
        label: t('dbUser.note'),
        align: 'center',
        field: 'Note',
      },
      {
        name: 'operate',
        label: t('dbUser.operate'),
        align: 'center',
        field: ''
      },
    ]
  })

  const rules = computed(() => ({
    required: (val: string | null | undefined) => validation.required(val),
    password: (val: string) => validation.passwordAtLeast(val),
    nameUnique: (val: string) => {
      const row = dbOrgRows.value.find(row => row.Name === val)
      if (row) {
        return row.Rid === orgData.value.Rid ? true : t('validate.nameUnique')
      }
      return true
    }
  }))

  const openDialogBeforeAction = (action: OpenDialogAction, row?: DbOrg) => {
    dialogAction.value = action
    visible.value = true
    dialogAction.value = action
    if (action === OpenDialogAction.Add) {
      orgData.value = getDefaultDbOrg()
    } else {
      orgData.value = cloneDeep(row as DbOrg)
    }
    visible.value = true
  }

  function selectNowPageAll() {
    if (selected.value.length === 0) return
    selected.value = selected.value.filter(org => org.Rid !== loginStatusStore.userOrgRid)
  }

  const insertDbOrg = async (row: DbOrg): Promise<boolean> => {
    const res = await insertRow(DbOrgSchema, row)
    if (res.RowsAffected === 1n) {
      successMessage(t('common.operationSuccess'))
      addRow(row)
      return true
    }

    errorMessage(t('common.operationFailed') + ` ${res.ErrInfo}`)
    return false
  }

  const updateDbOrg = async (row: DbOrg): Promise<boolean> => {
    const res = await updateRow(DbOrgSchema, row)
    if (res.RowsAffected === 1n) {
      successMessage(t('common.operationSuccess'))
      updateOrgStoreRow(row)
      return true
    }
    const errInfo = JSON.parse(res.ErrInfo)
    if (errInfo.rawMessage.includes('no permission')) {
      errorMessage(t('common.permissionDenied'))
    } else {
      errorMessage(t('common.operationFailed') + ` ${res.ErrInfo}`)
    }
    return false
  }

  const deleteDbOrg = async (row: DbOrg): Promise<boolean> => {
    const res = await deleteRow(DbOrgSchema, row)
    if (res.RowsAffected === 1n) {
      successMessage(t('common.operationSuccess'))
      deleteOrgStoreRow(row)
      return true
    }

    errorMessage(t('common.operationFailed') + ` ${res.ErrInfo}`)
    return false
  }

  async function batchDeleteRows(rows: DbOrg[]) {
    const isConfirm = await confirmAgainBatchDelete()
    if (!isConfirm) return

    const dialog = $q.dialog({
      message: t('common.operationProgress', { progress: 0 }),
      progress: true,
      persistent: true,
      ok: false,
      class: 'flex flex-col items-center justify-center'
    })

    try {
      const total = rows.length
      let successCount = 0
      let failCount = 0
      const startTime = Date.now()

      // 创建删除任务数组，每个任务都包含删除操作和更新进度
      const tasks = rows.map((row, index) =>
          deleteRow(DbOrgSchema, row)
              .then(res => {
                if (res.RowsAffected === 1n) {
                  successCount++
                  deleteOrgStoreRow(row)
                } else {
                  failCount++
                }
                // 更新进度对话框
                const progress = Math.round(((index + 1) / total) * 100)
                dialog.update({
                  message: t('common.operationProgress', { progress })
                })
                return res
              })
      )

      // 等待所有删除任务完成
      await Promise.all(tasks)

      // 关闭进度对话框，如果删除时间小600毫秒，则等待600毫秒，避免进度提示闪烁
      if (Date.now() - startTime < 800) {
        await new Promise(resolve => setTimeout(resolve, 600 - (Date.now() - startTime)))
      }
      dialog.hide()

      // 清空选中项
      selected.value = []

      // 显示操作结果
      if (failCount === 0) {
        successMessage(t('common.operationSuccess'))
      } else {
        errorMessage(t('common.operationPartialSuccess', {
          success: successCount,
          fail: failCount
        }))
      }
    } catch (error) {
      dialog.hide()
      errorMessage(t('common.operationFailed') + `: ${error}`)
    }
  }

  const submit = async (keepAdd: boolean) => {
    const valid = await formRef.value!.validate()
    if (!valid) return

    let isOk: boolean = false
    if (dialogAction.value === OpenDialogAction.Add) {
      isOk = await insertDbOrg(orgData.value)
    } else {
      isOk = await updateDbOrg(orgData.value)
    }

    if (isOk && !keepAdd) {
      visible.value = false
    }

    if (keepAdd) {
      const row = getDefaultDbOrg()
      row.Name = getNextName(orgData.value.Name)
      orgData.value = row
    }
  }

</script>

<style scoped lang="scss">
  @import "@/css/data_table.scss";
</style>
