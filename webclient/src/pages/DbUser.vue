<template>
  <q-page>
    <q-table
      style="height: calc(100vh - 50px)"
      ref="tableRef"
      :rows="finalUserRows"
      :columns="columns"
      row-key="Rid"
      selection="multiple"
      virtual-scroll
      v-model:selected="selected"
      bordered
      flat
      :filter="filter"
      v-model:pagination="pagination"
      :rows-per-page-options="[0]"
      @selection="handleSelection"
      binary-state-sort
      class="user-table sticky-header-table !border-x-0 !border-t-0 !rounded-none"
    >
      <template v-slot:top>
        <q-btn
          color="primary"
          :label="$t('form.add')"
          :disable="!canEditUser"
          @click="openDialogBeforeAction(OpenDialogAction.Add)"
        />
        <q-btn
          class="q-ml-sm"
          color="negative"
          :label="$t('form.batchDelete')"
          :disable="!canEditUser || selected.length <= 0"
          @click="batchDeleteRows(selected)"
        />
        <q-space />
        <q-input
          dense
          debounce="300"
          v-model="filter"
          :placeholder="$t('form.search')"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>
      <template v-slot:header-selection="scope">
        <q-checkbox
          v-model="scope.selected"
          :disable="!canEditUser || userRows.length === 1 && userRows[0].Rid === loginStatusStore.userRid"
          @update:model-value="selectNowPageAll"
        >
          <q-tooltip
            anchor="top middle"
            :offset="[10, 20]"
          >
            {{ $t('dbUser.selectTooltip') }}
          </q-tooltip>
        </q-checkbox>
      </template>

      <template v-slot:body-selection="scope">
        <q-checkbox
          :model-value="scope.selected"
          @update:model-value="(val, e) => {
                  // @ts-ignore
                  Object.getOwnPropertyDescriptor(scope, 'selected')?.set?.(val, e)
                }"
          :disable="canNotEditMySelf(scope.row) || !canEditUser"
        />
      </template>

      <template v-slot:body-cell-note="props">
        <q-td
          auto-width
          align="center"
        >
          <OverflowTd :value="props.row.Note"></OverflowTd>
        </q-td>
      </template>

      <template v-slot:body-cell-disabled="props">
        <q-td auto-width align="center">
          <template v-if="!props.row.Disabled">
            <q-chip
                dense
                clickable
                color="teal"
                text-color="white"
                icon="star"
                :disable="canNotEditMySelf(props.row) || !canEditUser"
            >
              {{ $t('dbProjectToken.enable') }}
              <popup-proxy-confirm
                  v-if="!canNotEditMySelf(props.row) && canEditUser"
                  :message="$t('dbUser.wantToDisableUser')"
                  @confirm="toggleUserDisabled(props.row)"
              />
            </q-chip>
          </template>
          <template v-else>
            <q-chip
                dense
                clickable
                color="red"
                text-color="white"
                icon="star_border"
                :disable="canNotEditMySelf(props.row) || !canEditUser"
            >
              {{ $t('dbProjectToken.disable') }}
              <popup-proxy-confirm
                  v-if="!canNotEditMySelf(props.row) && canEditUser"
                  :message="$t('dbUser.wantToEnableUser')"
                  @confirm="toggleUserDisabled(props.row)"
              />
            </q-chip>
          </template>
        </q-td>
      </template>

      <template v-slot:body-cell-operate="props">
        <q-td auto-width>
          <q-btn
            round
            outline
            icon="edit"
            :disable="getDisableEdit(props.row)"
            size="xs"
            color="primary"
            @click="openDialogBeforeAction(OpenDialogAction.Edit, props.row)"
          >
            <q-tooltip>
              {{ $t('form.edit') }}
            </q-tooltip>
          </q-btn>
          <q-btn
            round
            outline
            icon="delete"
            size="xs"
            color="negative"
            :disable="canNotEditMySelf(props.row) || !canEditUser"
            class="!ml-2"
          >
            <q-tooltip>
              {{ $t('form.delete') }}
            </q-tooltip>
            <popup-proxy-confirm
              :message="$t('dbUser.wantToDeleteUser')"
              @confirm="deleteDbUser(props.row)"
            />
          </q-btn>
        </q-td>
      </template>
      <template v-slot:no-data>
        <div class="full-width flex justify-center">
          <q-icon
            name="warning"
            size="xs"
          ></q-icon>
          <span>{{ $t('dbUser.noData') }}</span>
        </div>
      </template>
    </q-table>

    <q-dialog
      v-model="visible"
      persistent
    >
      <q-card class="w-fit !max-w-none p-4 pt-2">
        <!--header-->
        <q-bar class="bg-white !px-2 mt-2">
          <q-icon
            name="people"
            size="24px"
            color="primary"
          />
          <span class="pl-2">{{ dialogTitle }}</span>
          <q-space></q-space>
          <q-btn
            flat
            round
            dense
            icon="close"
            size="sm"
            v-close-popup
          />
        </q-bar>

        <!--body-->
        <q-card-section class="w-fit !p-2">
          <q-form
            class="fit user-form"
            :class="[isMobile ? '' : 'grid grid-cols-2 gap-x-4 min-w-xl']"
            ref="dialogRef"
          >
            <q-input
              v-model="userData.Name"
              :label="$t('userName')"
              outlined
              dense
              clearable
              autofocus
              lazy-rules
              :maxlength="16"
              :rules="[rules.required, rules.nameUnique]"
              @blur="nameChange"
            />
            <q-input
              :class="{'mb-4': isMobile}"
              v-model="userData.Nickname"
              :label="$t('dbUser.nickname')"
              outlined
              dense
              clearable
              lazy-rules
              :maxlength="16"
              v-clear-fix="[userData, 'Nickname']"
            />
            <q-select
              outlined
              v-model="userData.Org"
              :options="orgRowOptions"
              :label="$t('dbOrg.org')"
              lazy-rules
              dense
              clearable
              options-dense
              map-options
              emit-value
              :rules="[rules.required]"
            />
            <q-input
              v-model="userData.Email"
              :label="$t('dbUser.email')"
              outlined
              dense
              lazy-rules
              clearable
              :rules="[rules.email]"
              v-clear-fix="[userData, 'Email']"
            />
            <q-input
              v-model="userData.Phone"
              :label="$t('dbUser.phone')"
              outlined
              dense
              lazy-rules
              clearable
              :rules="[rules.phone]"
              v-clear-fix="[userData, 'Phone']"
            />
            <q-input
              :type="isPwd ? 'password' : 'text'"
              v-model="userData.Password"
              :label="$t('password')"
              outlined
              dense
              counter
              lazy-rules
              :rules="passwordRules"
            >
              <template v-slot:append>
                <q-icon
                  :name="isPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isPwd = !isPwd"
                />
              </template>
            </q-input>
            <q-input
              type="textarea"
              v-model="userData.Note"
              :label="$t('dbUser.note')"
              outlined
              dense
              autogrow
              counter
              clearable
              lazy-rules
              :maxlength="256"
              :rules="[]"
              class="col-span-2"
              v-clear-fix="[userData, 'Note']"
            />

            <div
              class="privilege-setting flex cursor-pointer w-fit pr-4"
              @click="expanded = !expanded"
              v-if="canEditUser"
            >
              <q-btn
                color="grey"
                round
                flat
                dense
                :icon="expanded ? 'keyboard_arrow_down' : 'keyboard_arrow_right'"
              />
              <div class="privilege-setting-title leading-[34px]">{{ $t('dbUser.userPrivilegeSetting') }}</div>
            </div>

            <q-slide-transition class="col-span-2" v-if="canEditUser">
              <div v-show="expanded">
                <q-separator />
                <q-card-section class="text-subtitle2 !p-0 !pt-4">
                  <q-checkbox
                    class="pl-4"
                    v-model="userPrivilegeData.CanModifyOtherUser"
                    @update:model-value="updateUserPrivilegeData"
                    :disable="canNotEditMySelf(userData)"
                    :label="$t('dbUser.canEditUser')"
                  />
                  <q-checkbox
                      v-model="userData.Disabled"
                      :label="$t('dbUser.isDisableUser')"
                      :disable="canNotEditMySelf(userData)"
                      @update:model-value="updateUserDisable"
                      class="pl-4"
                  >
                  </q-checkbox>
                </q-card-section>
              </div>
            </q-slide-transition>

            <div class="flex justify-center gap-2 mt-4 col-span-2">
              <q-btn
                color="primary"
                class="!w-[150px]"
                :label="dialogAction === OpenDialogAction.Add ? $t('form.add') : $t('form.confirm')"
                @click="_ => submit()"
              />
              <q-btn
                v-if="dialogAction === OpenDialogAction.Add"
                color="secondary"
                class="!w-[150px]"
                :label="$t('form.keepAdd')"
                @click="_ => submit(true)"
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
  import { useDbUserStore, useDbUserPrivilegeStore, useDbOrgStore } from '@/stores/dataBase.ts'
  import { useI18n } from 'vue-i18n'
  import { type QTableColumn, QForm, QTable, useQuasar } from 'quasar'
  import { ref, defineAsyncComponent, computed, onMounted, onBeforeUnmount } from 'vue'
  import { DbUserSchema, DbUserPrivilegeSchema } from '@/proto/db_pb.ts'
  import type { DbUser, DbUserPrivilege } from '@/proto/db_pb.ts'
  import { v7 as uuidV7 } from 'uuid'
  import { useLoginStatusStore } from '@/stores/session'
  import { create } from '@bufbuild/protobuf'
  import { encodeUserPassword } from '@/utils/crypto.ts'
  import { formatDayjs, getTimestamp } from '@/utils/dayjs.ts'
  import * as validation from '@/utils/validation.ts'
  import { StrPubSub } from 'ypubsub'
  import { NewUserDialog } from '@/utils/pubSubSubject.ts'
  import logger from '@/utils/logger.ts'
  import { storeToRefs } from 'pinia'
  import { createUser, deleteRow, updateRow, updateDbUser } from '@/services/connectRpc.ts'
  import { errorMessage, successMessage } from '@/utils/notify.ts'
  import { UpdateDbUserReqSchema, type Common, CommonRespCode } from '@/proto/bfmap.rpc_pb.ts'
  import { OpenDialogAction } from '@/utils/types'
  import cloneDeep from 'lodash/cloneDeep'
  import { confirmAgainBatchDelete, getNextName, useTableHandleSelection } from '@/utils/common.ts'
  import OverflowTd from '@/components/OverflowTd.vue'
  import { CreateUserReqSchema } from '@/proto/bfmap.rpc_pb.ts'
  import { AdminUserRid } from '@/stores/common.ts';
  import type { CrudResp } from '@/proto/protodb_pb.ts';

  const PopupProxyConfirm = defineAsyncComponent(() => import('@/components/PopupProxyConfirm.vue'))

  const dialogAction = ref<OpenDialogAction>(OpenDialogAction.Add)
  let editUserDataPhoneCache: string = ''

  const { t } = useI18n()
  const $q = useQuasar()
  const dbUserStore = useDbUserStore()
  const dbUserPrivilegeStore = useDbUserPrivilegeStore()
  const loginStatusStore = useLoginStatusStore()
  const { userRid: loginUserRid, userOrgRid: loginUserOrgRid } = storeToRefs(loginStatusStore)
  const { rows: userRows, rowsMap: userRowsMap } = storeToRefs(dbUserStore)
  const { rows: userPrivilegeRows } = storeToRefs(dbUserPrivilegeStore)
  const dbOrgStore = useDbOrgStore()
  const { rowsMap: orgRowsMap } = storeToRefs(dbOrgStore)

  const dialogRef = ref<QForm>()
  const visible = ref<boolean>(false)
  const { tableRef, selected, handleSelection } = useTableHandleSelection<DbUser>()
  const dialogTitle = ref<string>('')
  const userData = ref<DbUser>(getDefaultUserData())
  const filter = ref('')
  const userPrivilegeData = ref<DbUserPrivilege>(getDefaultUserPrivilege())
  const pagination = ref({
    rowsPerPage: 0
  })
  const expanded = ref(false)
  const isPwd = ref(true)

  const finalUserRows = computed(() => {
    const me = userRowsMap.value.get(loginUserRid.value)
    if (me?.Rid === AdminUserRid) {
      return userRows.value
    }
    const myCreator = userRowsMap.value.get(me?.Creater ?? '')

    const myOrg = orgRowsMap.value.get(loginUserOrgRid.value)
    const myOrgCreator = userRowsMap.value.get(myOrg?.OwnerRid ?? '')
    return userRows.value.filter(row => row.Rid !== myCreator?.Rid && row.Rid !== myOrgCreator?.Rid)
  })

  const isMobile = computed(() =>{
    return $q.platform.is.mobile
  })

  const columns = computed<QTableColumn[]>(() => {
    return [
      {
        name: 'name',
        required: true,
        label: t('userName'),
        align: 'center',
        field: 'Name',
        style: 'width: 220px'
      },
      {
        name: 'nickName',
        label: t('dbUser.nickname'),
        align: 'center',
        field: 'Nickname',
      },
      {
        name: 'org',
        label: t('dbOrg.org'),
        align: 'center',
        field: 'Org',
        format: (val: string) => {
          return dbOrgStore.rows.find(row => row.Rid === val)?.Name ?? val
        }
      },
      {
        name: 'disabled',
        label: t('dbUser.userStatus'),
        align: 'center',
        field: 'Disabled',
      },
      {
        name: 'email',
        label: t('dbUser.email'),
        align: 'center',
        field: 'Email',
        style: 'width: 200px'
      },
      {
        name: 'phone',
        label: t('dbUser.phone'),
        align: 'center',
        field: 'Phone',
        style: 'width: 140px'
      },
      {
        name: 'createTime',
        label: t('dbUser.createTime'),
        align: 'center',
        field: 'CreateTime',
        format: (val: bigint) => {
          return formatDayjs(Number(val) * 1000)
        },
        style: 'width: 190px'
      },
      {
        name: 'note',
        label: t('dbUser.note'),
        align: 'center',
        field: 'Note',
      },
      {
        name: 'operate',
        label: t('dbUser.operate'),
        align: 'center',
        field: ''
      }
    ]
  })

  const orgRowOptions = computed(() => {
    return dbOrgStore.rows.map(row => {
      return {
        value: row.Rid,
        label: row.Name
      }
    })
  })

  const canEditUser = computed(() => {
    return userPrivilegeRows.value.find(up => up.UserRid === loginStatusStore.userRid)?.CanModifyOtherUser
  })

  const canNotEditMySelf = (row: DbUser) => {
    return row.Rid === loginStatusStore.userRid
  }

  const getDisableEdit = (row: DbUser): boolean => {
    if (canNotEditMySelf(row)) {
      return false
    }
    return !canEditUser.value
  }

  const rules = computed(() => ({
    required: (val: string | null | undefined) => validation.required(val),
    allowedChars: (val: string) => validation.validatePasswordWithAllowedChars(val),
    password: (val: string) => validation.passwordAtLeast(val),
    phone: (val: string) => validation.validatePhone(val),
    email: (val: string) => validation.validateEmail(val),
    nameUnique: (val: string) => {
      const row = userRows.value.find(row => row.Name === val)
      if (row) {
        return row.Rid === userData.value.Rid ? true : t('validate.nameUnique')
      }
      return true
    }
  }))

  const passwordRules = computed(() => {
    if (dialogAction.value === OpenDialogAction.Add) {
      return [rules.value.required, rules.value.allowedChars, rules.value.password]
    } else {
      const rule = [rules.value.allowedChars]
      if (userData.value.Password) {
        rule.push(rules.value.password)
      }
      return rule
    }
  })

  function selectNowPageAll() {
    if (selected.value.length === 0) return
    selected.value = selected.value.filter(user => user.Rid !== loginStatusStore.userRid)
  }

  function nameChange() {
    if (!userData.value.Nickname) {
      userData.value.Nickname = userData.value.Name
    }
  }

  function getDefaultUserData(): DbUser {
    return create(DbUserSchema, {
      Rid: uuidV7(),
      Org: loginStatusStore.userOrgRid,
      Name: '',
      Nickname: '',
      Email: '',
      Phone: '',
      CreateTime: getTimestamp(),
      Creater: loginStatusStore.userRid,
      Note: '',
      Disabled: false,
    })
  }

  function getDefaultUserPrivilege(): DbUserPrivilege {
    return create(DbUserPrivilegeSchema, {
      Rid: uuidV7(),
      UserRid: userData.value.Rid,
      UpdateTime: getTimestamp(),
      CanModifyOtherUser: false,
    })
  }

  // 打开弹窗前操作
  function openDialogBeforeAction(action: OpenDialogAction, row?: DbUser) {
    dialogAction.value = action
    if (action === OpenDialogAction.Add) {
      dialogTitle.value = t('dbUser.userManager') + '-' + t('form.add')
      userData.value = getDefaultUserData()
      userPrivilegeData.value = getDefaultUserPrivilege()
    } else {
      dialogTitle.value = t('dbUser.userManager') + '-' + t('form.edit')
      const user = cloneDeep(row) as DbUser
      editUserDataPhoneCache = user.Password
      user.Password = ''
      userData.value = user
      const rowPrivilege = userPrivilegeRows.value.find(up => up.UserRid === user.Rid) ?? getDefaultUserPrivilege()
      userPrivilegeData.value = cloneDeep(rowPrivilege)
    }
    visible.value = true
  }

  // 添加
  async function addUserData(row: DbUser) {
    userPrivilegeData.value.UserRid = row.Rid
    userPrivilegeData.value.UpdateTime = getTimestamp()
    const req = create(CreateUserReqSchema, {
      User: row,
      UserPrivilege: userPrivilegeData.value
    })

    return await createUser(req)
        .then(res => {
          if (res.Code === 0) {
            successMessage(t('form.addSuccess'))
            dbUserStore.addRow(row)
            dbUserPrivilegeStore.addRow({ ...userPrivilegeData.value })
            return true
          } else {
            errorMessage(t('common.operationFailed'))
            return false
          }
        })
        .catch((err) => {
          logger.error('addUser catch error:', err)
          return false
        })
  }

  async function editUserData(row: DbUser) {
    if (row.Password) {
      editUserDataPhoneCache = encodeUserPassword(userData.value.Name, userData.value.Password)
    }
    const data = { ...row, Password: editUserDataPhoneCache }

    return await updateDbUser(create(UpdateDbUserReqSchema, {
      User: data,
    }))
        .then((res: Common) => {
          if (res.Code === 0) {
            dbUserStore.updateRow(data)
            return true
          } else {
            logger.error('toggleUserDisabled failed', res)
            handleUpdateUserFailed(res)
            return false
          }
        })
        .catch((err: Error) => {
          logger.error('toggleUserDisabled catch error:', err)
          errorMessage(t('common.operationFailed'))
          return false
        })
  }

  async function batchDeleteRows(rows: DbUser[]) {
    const isConfirm = await confirmAgainBatchDelete()
    if (!isConfirm) return

    const dialog = $q.dialog({
      message: t('common.operationProgress', { progress: 0 }),
      progress: true,
      persistent: true,
      ok: false,
      class: 'flex flex-col items-center justify-center'
    })

    try {
      const total = rows.length
      let successCount = 0
      let failCount = 0
      const startTime = Date.now()

      // 创建删除任务数组，每个任务都包含删除操作和更新进度
      const tasks = rows.map((row, index) =>
          deleteRow(DbUserSchema, row)
              .then(res => {
                if (res.RowsAffected === 1n) {
                  successCount++
                  dbUserStore.deleteRow(row)
                  const userPrivilegeRow = userPrivilegeRows.value.find(up => up.UserRid === row.Rid)
                  if (userPrivilegeRow) {
                    dbUserPrivilegeStore.deleteRow(userPrivilegeRow)
                  }
                } else {
                  failCount++
                }
                // 更新进度对话框
                const progress = Math.round(((index + 1) / total) * 100)
                dialog.update({
                  message: t('common.operationProgress', { progress })
                })
                return res
              })
      )

      // 等待所有删除任务完成
      await Promise.all(tasks)

      // 关闭进度对话框，如果删除时间小600毫秒，则等待600毫秒，避免进度提示闪烁
      if (Date.now() - startTime < 800) {
        await new Promise(resolve => setTimeout(resolve, 600 - (Date.now() - startTime)))
      }
      dialog.hide()

      // 清空选中项
      selected.value = []

      // 显示操作结果
      if (failCount === 0) {
        successMessage(t('common.operationSuccess'))
      } else {
        errorMessage(t('common.operationPartialSuccess', {
          success: successCount,
          fail: failCount
        }))
      }
    } catch (error) {
      console.log('error', error)
      dialog.hide()
      errorMessage(t('common.operationFailed') + `: ${error}`)
    }
  }

  // 删除一行数据
  const deleteDbUser = async (row: DbUser): Promise<boolean> => {
    const res = await deleteRow(DbUserSchema, row)
    if (res.RowsAffected === 1n) {
      successMessage(t('common.operationSuccess'))
      dbUserStore.deleteRow(row)
      return true
    }

    errorMessage(t('common.operationFailed') + ` ${res.ErrInfo}`)
    return false
  }

  const handleUpdateUserFailed = (res: Common) => {
    switch (res.Code) {
      case CommonRespCode.PermissionDenied:
        errorMessage(t('common.permissionDenied'))
        break
      default:
        errorMessage(t('common.operationFailed') + (res.Reason ? `: ${res.Reason}` : ''))
        break
    }
  }

  // 切换用户禁用状态
  const toggleUserDisabled = async (row: DbUser): Promise<boolean> => {
    const updatedRow = cloneDeep(row)
    updatedRow.Disabled = !updatedRow.Disabled
    
    // Use the new UpdateDbUser RPC method
    return await updateDbUser(create(UpdateDbUserReqSchema, {
      User: updatedRow,
      UpdateFields: ['Disabled'] // Only update the Disabled field
    }))
      .then((res: Common) => {
        if (res.Code === 0) {
          dbUserStore.updateRow(updatedRow)
          successMessage(updatedRow.Disabled ? 
            t('dbUser.disableSuccess') : 
            t('dbUser.enableSuccess'))
          return true
        } else {
          logger.error('toggleUserDisabled failed', res)
          handleUpdateUserFailed(res)
          return false
        }
      })
      .catch((err: Error) => {
        logger.error('toggleUserDisabled catch error:', err)
        errorMessage(t('common.operationFailed'))
        return false
      })
  }

  function updateUserDisable() {
    if (dialogAction.value === OpenDialogAction.Add) {
      return
    }

    updateDbUser(create(UpdateDbUserReqSchema, {
      User: userData.value,
      UpdateFields: ['Disabled'] // Only update the Disabled field
    }))
        .then((res: Common) => {
          if (res.Code === 0) {
            dbUserStore.updateRow(userData.value)
            successMessage(userData.value.Disabled ?
                t('dbUser.disableSuccess') :
                t('dbUser.enableSuccess'))
          } else {
            logger.error('toggleUserDisabled failed', res)
            userData.value.Disabled = !userData.value.Disabled
            handleUpdateUserFailed(res)
          }
        })
        .catch((err: Error) => {
          logger.error('toggleUserDisabled catch error:', err)
          userData.value.Disabled = !userData.value.Disabled
          errorMessage(t('common.operationFailed'))
        })
  }

  function updateUserPrivilegeData() {
    if (dialogAction.value === OpenDialogAction.Add) {
      return
    }
    const updateFail = (res?: CrudResp) => {
      const row = dbUserPrivilegeStore.rows.find(r => r.Rid === userPrivilegeData.value.Rid)
      userPrivilegeData.value = cloneDeep(row!)
      const err = JSON.parse(res?.ErrInfo ?? '{}')
      if (err.rawMessage.includes('no permission')) {
        errorMessage(t('common.permissionDenied'))
      } else {
        errorMessage(t('common.operationFailed'))
      }
    }

    updateRow<DbUserPrivilege>(DbUserPrivilegeSchema, userPrivilegeData.value)
        .then((res) => {
          if (res.RowsAffected === 1n) {
            dbUserPrivilegeStore.updateRow(userPrivilegeData.value)
            successMessage(t('common.operationSuccess'))
          } else {
            logger.error('updateUserPrivilege failed', res)
            updateFail(res)
          }
        })
        .catch((err) => {
          logger.error('updateUserPrivilege catch error:', err)
          updateFail()
        })
  }

  function submit(keepAdd?: boolean) {
    dialogRef.value!.validate().then(async (res) => {
      if (!res) return
      let isOk: boolean
      if (dialogAction.value === OpenDialogAction.Add) {
        const pw = encodeUserPassword(userData.value.Name, userData.value.Password)
        const user = { ...userData.value, Password: pw }
        isOk = await addUserData(user)
      } else {
        isOk = await editUserData(userData.value)
        if (isOk) {
          successMessage(t('form.updateSuccess'))
        } else {
          return
        }
      }
      if (isOk && !keepAdd) {
        visible.value = false
        return
      }
      userData.value.Rid = uuidV7()
      userData.value.Name = getNextName(userData.value.Name)
      userData.value.Nickname = getNextName(userData.value.Nickname)
      userData.value.CreateTime = getTimestamp()
      userPrivilegeData.value.Rid = uuidV7()
      userPrivilegeData.value.UserRid = userData.value.Rid
      userPrivilegeData.value.UpdateTime = getTimestamp()
    })
  }

  onMounted(() => {
    StrPubSub.subscribe(NewUserDialog, () => {
      openDialogBeforeAction(OpenDialogAction.Add)
    })
  })

  onBeforeUnmount(() => {
    StrPubSub.unsubscribe(NewUserDialog)
  })

</script>

<style scoped lang="scss">
  @import "@/css/data_table.scss";

  .user-form {
    .privilege-setting:hover {
      background-color: #f5f5f5;
    }

    .privilege-setting-title {
      color: #777777;
    }
  }
</style>
