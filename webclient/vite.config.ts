import { fileURLToPath } from 'url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { quasar, transformAssetUrls } from '@quasar/vite-plugin'
import tailwindcss from '@tailwindcss/vite'
// @ts-expect-error vite-plugin-eslint 类型问题
import eslintPlugin from 'vite-plugin-eslint'

function createProxyApi(baseUrl: string, api: string) {
  return {
    [`/${api}`]: {
      target: baseUrl,
      changeOrigin: true,
    },
  }
}

// https://vitejs.dev/config/
export default ({ mode }: { mode: string }) => {
  const viteEnv = loadEnv(mode, process.cwd())
  console.log('viteEnv:', viteEnv)

  const baseApiUrl = `${viteEnv.VITE_SERVER_PROTOCOL}://${viteEnv.VITE_SERVER_HOSTNAME}:${viteEnv.VITE_SERVER_PORT}`

  return defineConfig({
    plugins: [
      vue({
        template: { transformAssetUrls }
      }),

      // @quasar/plugin-vite options list:
      // https://github.com/quasarframework/quasar/blob/dev/vite-plugin/index.d.ts
      quasar({
        sassVariables: fileURLToPath(
          new URL('./src/quasar-variables.sass', import.meta.url)
        )
      }),
      tailwindcss(),
      eslintPlugin({
        // 配置项 (可选)
        fix: true, // 自动修复部分 ESLint 问题 (可选)
        cache: false, // 禁用缓存，确保每次都进行 lint 检查
        // include: ['./src/**/*.{js,jsx,ts,tsx,vue}'], // 指定要 lint 的文件 (可选)
        // exclude: ['./node_modules/**'], // 排除不需要 lint 的目录 (可选)
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
    server: {
      open: true,
      host: true,
      proxy: {
        ...createProxyApi(baseApiUrl, 'api'),
      }
    }
  })
}
