variables:
  BuildDir: ./build

.go-cache:
  variables:
    GOMODCACHE: $CI_PROJECT_DIR/.gomodcache
  before_script:
    - export GOPROXY='https://goproxy.cn|https://goproxy.io,direct'
    - export GOSUMDB=off
    - export GOPRIVATE=git.kicad99.com
    - mkdir -p $GOMODCACHE
  cache:
    key:
      files:
        - go.sum
      prefix: ${CI_COMMIT_REF_SLUG}
    paths:
      - $GOMODCACHE

.node-cache:
  before_script:
    - npm i -g pnpm
    - pnpm config set store-dir $CI_PROJECT_DIR/.pnpm-store
  cache:
    key:
      files:
        - webclient/pnpm-lock.yaml
      prefix: ${CI_COMMIT_REF_SLUG}
    paths:
      - $CI_PROJECT_DIR/.pnpm-store

stages:
  - lint
  - build-web
  - build-linux64
  - build-win64

lint:
  stage: lint
  image: ghcr.io/yangjuncode/go:1.23.9
  tags:
    - docker
  extends:
    - .go-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
  script:
    - go vet ./...

build-web:
  stage: build-web
  image: node:22.16.0
  tags:
    - docker
  extends:
    - .node-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - version.txt
  script:
    - ./build-web.sh
  artifacts:
    expire_in: 7 day
    name: bfmap_web_client
    paths:
      - $BuildDir/web

build-linux64:
  stage: build-linux64
  image: ghcr.io/yangjuncode/go:1.23.9
  tags:
    - docker
  extends:
    - .go-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - version.txt
  script:
    # clean the build dir except web
    - find build/ -mindepth 1 -maxdepth 1 -not \( -name "web" -type d \) -exec rm -rf {} \; 2>/dev/null
    - ./build-linux64.sh
  artifacts:
    expire_in: 7 day
    name: bfmap_linux_x64
    paths:
      - $BuildDir

build-win64:
  stage: build-win64
  image: ghcr.io/yangjuncode/go:1.23.9
  tags:
    - docker
  extends:
    - .go-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - version.txt
  script:
    # clean the build dir except web
    - find build/ -mindepth 1 -maxdepth 1 -not \( -name "web" -type d \) -exec rm -rf {} \; 2>/dev/null
    - ./build-win64.sh
  artifacts:
    expire_in: 7 day
    name: bfmap_win_x64
    paths:
      - $BuildDir