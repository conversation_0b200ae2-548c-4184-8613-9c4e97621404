syntax = "proto3";

package rpc;

option go_package = "bfmap/rpc";

import "db.proto";

// empty msg
message Empty {}


// some response code in common means
enum CommonRespCode {
  Success = 0;
  InvalidParam = 1;
  InvalidSessionId = 2;
  UnmarshalError = 3;
  DataBaseError = 4;
  ServerError = 5;
  PermissionDenied = 6;
  NotFound = 7;
  AlreadyExist = 8;
  Unknown = 9;
}

// common msg
message Common {
  int32 Code = 1;
  string Reason = 2;
  string Msg = 3;
  bytes Body = 4;
}

message SetupReq {
  string Name = 1;
  // password, base64(sha256(Name+Password))
  string Password = 2;
  string OrgName = 3;
  string ProjectName = 4;
}

enum LoginMethod {
  Password = 0;
  SessionId = 1;
}

message LoginReq {
  string Name = 1;
  // 0: password login，1: session id login
  LoginMethod LoginMethod = 2;
  // value is base64(sha256(time_str+base64(sha256(Name+Password))))
  string PasswordHash = 3;
  // format in utc time: yyyy-mm-dd hh:mm:ss
  string TimeStr = 4;
  string SessionId = 5;
}

enum LoginRespCode {
  LoginSuccess = 0;
  InvalidLoginParam = 1;
  // the time in LoginReq is too old, it should be less than 5 minutes from now
  ReqTimeTooOld = 2;
  // the time in LoginReq is too new, it should not be more than 1 minute from now
  ReqTimeTooNew = 3;
  UserNotExist = 4;
  PasswordNotMatch = 5;
  SessionIdNotExist = 6;
  SessionIdExpired = 7;
  SessionAlreadyLogin = 8;
  FailWithInternalError = 9;
  UserDisabled = 10;
}

message LoginResp {
  LoginRespCode Code = 1;
  // if login fail, the reason will be set
  string Reason = 2;
  string SessionId = 3;
  string UserRid = 4;
  string UserOrgRid = 5;
  string ServerVersion = 6;
}

message CreateUserReq {
  dbproto.DbUser User = 1;
  dbproto.DbUserPrivilege UserPrivilege = 2;
}

message SetProviderTokenReq {
  // 1:create
  // 2:update,if UpdateFields is empty, all the fields will be updated
  // 4:delete, mark old one as "delete"
  // 8:force delete,delete in db
  // Common.Body is DbMapProviderToken
  int32 Code = 1;
  dbproto.DbMapProviderToken ProviderToken = 2;
  repeated string UpdateFields = 3;
}

message SetProjectReq {
   // 1:create
  // 2:update,if UpdateFields is empty, all the fields will be updated,
  // 4:delete, mark old one as "delete"
  // 8:force delete,delete in db
  // Common.Body is DbMapProviderToken
  int32 Code = 1;
  // set empty if do not want to update this
  dbproto.DbProject DbProject = 2;
  // set empty if do not want to update this
  dbproto.DbProjectQuotas DbProjectQuotas = 3;
  repeated string UpdateProjectFields = 4;
  repeated string UpdateQuotasFields = 5;
}

message SetProjectTokenReq {
  // 1:create
  // 2:update, if UpdateFields is empty, all the fields will be updated
  // 3:rotate token,create new one and mark old one status as "delete",
  // new DbProjectToken return in Common.Body
  // 4:delete, mark old one as "delete"
  // 8:force delete,delete in db
  // Common.Body is DbMapProviderToken
  int32 Code = 1;
  dbproto.DbProjectToken ProjectToken = 2;
  repeated string UpdateFields = 3;
}

message DeleteMapCacheIndexesReq {
  // map type: 1:roadmap 2:satellite 3: hybrid
  int32 MapType = 1;
  repeated dbproto.MapProviderEnum Providers = 2;
  // max zoom is 22
  int32 Zoom = 3;
  double MinLon = 4;
  double MaxLon = 5;
  double MinLat = 6;
  double MaxLat = 7;
  // <=0: no limit
  int64 CacheTime = 8;
}

message GetCurrentUsageReq {
  // 1:provider token 2:project 3:project token
  int32 Code = 1;
  // for provider token
  string UserRid = 2;
  // for provider token
  dbproto.MapProviderEnum Provider = 3;
  // for provider token
  string ProviderToken = 4;
  // for project
  string ProjectRid = 5;
  // for project token
  string ProjectToken = 6;
}

message GetCurrentUsageResp {
  // 0:success, !0:fail
  int32 Code = 1;
  string Reason = 2;
  int32 CurrentUsage = 3;
}

message UpdateDbUserReq {
  dbproto.DbUser User = 1;
  // Optional: If provided, only update these fields
  repeated string UpdateFields = 2;
}

// bfmap rpc service
// need add "Session-Id" in header
service bfmap {
  // is server has setup，no session id required
  // Common.Code: 0: setup, !0: not setup
  rpc IsSetup(Empty) returns (Common);
  // Setup to setup the admin account,no session id required
  // Common.Code: 0: success, !0: fail, may refer to Common.Reason, see CommonRespCode
  rpc Setup(SetupReq) returns (Common);
  // Login to login the server,no session id required
  rpc Login(LoginReq) returns (LoginResp);
  // Logout to logout the server, use "Session-Id" in header to identify the user
  // Common.Code: 0: success, !0: fail, may refer to Common.Reason, see CommonRespCode
  rpc Logout(Empty) returns (Common);
  // Ping to keep the session alive,every hour
  // Common.Code: 0: success, !0: fail, see CommonRespCode
  rpc Ping(Empty) returns (Common);
  rpc CreateUser(CreateUserReq) returns (Common);
  // 1:create
  // 2:update
  // 3:rotate token,create new one and mark old one status as "delete"
  // only for token, new one return in Common.Body
  // 4:delete, mark old one as "delete"
  // Common.Body is DbMapProviderToken
  // in resp,Common.Code: 0: success, !0: fail, see CommonRespCode
  rpc SetProviderToken(SetProviderTokenReq) returns (Common);
  rpc SetProject(SetProjectReq)returns (Common);
  // Common.Msg is DbProjectToken, code is same as SetProviderToken
  // **ProjectToken Rid and ProjectRid should be in SetProjectReq**
  rpc SetProjectToken(SetProjectTokenReq) returns (Common);
  // in resp,Common.Code: 0: success, !0: fail, see CommonRespCode
  // result in Common.Msg is token value
  rpc CreateTempMapProjectToken(Empty) returns (Common);
  rpc DeleteMapCacheIndexes(DeleteMapCacheIndexesReq) returns (Common);
  rpc GetCurrentUsage(GetCurrentUsageReq) returns (GetCurrentUsageResp);
  // Update a DbUser
  // Common.Code: 0: success, !0: fail, see CommonRespCode
  rpc UpdateDbUser(UpdateDbUserReq) returns (Common);
}
