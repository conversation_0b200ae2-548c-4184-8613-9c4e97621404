syntax = "proto3";

package protodb;

import "google/protobuf/descriptor.proto";

option go_package = "github.com/ygrpc/protodb";

message PDBFile {
  // name style(msg & field)
  // empty='go': default, go name style, better performance in crud operation in
  // go (like: UserName) 'snake': snake name style (like: user_name)
  string NameStyle = 1;

  // comment for file
  repeated string Comment = 2;
}

message PDBMsg {
  // comment for table
  repeated string Comment = 1;

  // sql prepends before create table
  repeated string SQLPrepend = 2;

  // sql appends before )
  repeated string SQLAppend = 3;

  // sql appends after ) before ;
  repeated string SQLAppendsAfter = 4;

  // sql appends after ;
  repeated string SQLAppendsEnd = 5;

  // generate proto msg { {msg}}List in  xxx.list.proto
  // 0: auto if msg name start with db then generate { {msg}}List
  // 1: always generate { {msg}}List
  // 4: never generate { {msg}}List
  int32 MsgList = 6;

  // do not generate db table for this message
  bool NotDB = 7;

  //sql for migrate table
  repeated string SQLMigrate = 8;
}

enum FieldDbType {
  // auto match db type if DbTypeStr not set
  // pb type -> db type
  // bool -> bool
  // string -> text
  // int32 -> int
  // int64,uint32 -> bigint
  // float -> float
  // double -> double precision
  // bytes -> bytea
  AutoMatch = 0;
  BOOL = 1;
  INT32 = 2;
  INT64 = 3;
  FLOAT = 4;
  DOUBLE = 5;
  TEXT = 6;
  JSONB = 7;
  UUID = 8;
  TIMESTAMP = 9;
  DATE = 10;
  BYTEA = 11;
  //ipv4 or ipv6 address
  INET = 12;
  UINT32 = 13;
}

message PDBField {
  // do not generate db field in create table
  // when in update, do not update this field
  bool NotDB = 1;

  // is primary key
  bool Primary = 2;

  // is unique key, if the unique include multiple columns, specify the UniqueName
  bool Unique = 3;

  // is not null
  bool NotNull = 4;

  // reference to other table, sql like:  REFERENCES other_table(other_field)
  string Reference = 5;

  // default value
  string DefaultValue = 6;

  // append sql before ,
  repeated string SQLAppend = 7;

  // append sql after ,
  repeated string SQLAppendsEnd = 8;

  // db no update
  // when in update, do not update this field, for example, create time
  bool NoUpdate = 9;

  // serial type 0:not serial type 2:smallint(serial2) 4:integer(serial4) 8:bigint(serial8)
  // strong advice not use serial type,it's hard in distributed system
  int32 SerialType = 10;

  // db type
  FieldDbType DbType = 11;
  // use custom db type when DbType is not good fit
  string DbTypeStr = 12;

  // zero value treat as null for insert,update, especially for reference field
  bool ZeroAsNull = 13;

  // db no insert
  // when in insert, do not insert this field, for example, database has default value
  bool NoInsert = 14;

  // comment for field
  repeated string Comment = 15;

  // unique group name
  // when a unique constrain include multiple column, specify the a group name for it
  string UniqueName = 16;
}

extend google.protobuf.FileOptions {optional PDBFile pdbf = 1888;}

extend google.protobuf.MessageOptions {optional PDBMsg pdbm = 1888;}

extend google.protobuf.FieldOptions {optional PDBField pdb = 1888;}

//crud api code
enum CrudReqCode {
  INSERT = 0;
  UPDATE = 1;
  PARTIALUPDATE = 2;
  DELETE = 3;
  SELECTONE = 4;
  QUERY = 5;
}

// crud result type for return
enum CrudResultType {
  //only info dml result(affected rows)
  DMLResult = 0;
  //return new row as msg, selectone use this too
  NewMsg = 1;
  //return old row as msg and new row as msg
  OldMsgAndNewMsg = 2;
}

// where operator
enum WhereOperator {
  // unknown
  WOP_UNKNOWN = 0;
  // > greater than
  WOP_GT = 1;
  // < less than
  WOP_LT = 2;
  // >= greater than or equal
  WOP_GTE = 3;
  // <= less than or equal
  WOP_LTE = 4;
  // like
  WOP_LIKE = 5;
  // equal
  WOP_EQ = 6;


}

//crud request
message CrudReq {
  CrudReqCode Code = 1;
  CrudResultType ResultType = 2;
  string SchemeName = 3;
  string TableName = 4;
  bytes MsgBytes = 5;
  // msg format 0:protobuf 1:protobuf json
  int32 MsgFormat = 6;
  int32 MsgLastFieldNo = 7;
  repeated string PartialUpdateFields = 8;
  repeated string SelectResultFields = 9;
  repeated string SelectOneKeyFields = 10;
}

// Crud response
message CrudResp {
  // RowsAffected in dml operation
  int64 RowsAffected = 1;
  // err info when error happened
  string ErrInfo = 2;
  //old row as msg
  bytes OldMsgBytes = 3;
  //new row as msg
  bytes NewMsgBytes = 4;
  // msg format 0:protobuf 1:protobuf json
  int32 MsgFormat = 8;
}

message TableQueryReq {
  string SchemeName = 1;
  string TableName = 2;
  // result column names, need same as proto msg field name
  repeated string ResultColumnNames = 3;
  // Fieldname == Value
  map<string, string> Where = 4;
  // limit 0:no limit
  int32 Limit = 5;
  int64 Offset = 6;
  // prefer batch size
  int32 PreferBatchSize = 7;
  // msg format 0:protobuf 1:protobuf json
  int32 MsgFormat = 8;
  // where2 field operator, fieldname -> op
  map<string, WhereOperator> Where2Operator = 9;
  // where2 field value, fieldname -> value
  map<string, string> Where2 = 10;
}

message QueryResp {
  // response batch no, start from 0
  int64 ResponseNo = 1;
  // if it is last response
  bool ResponseEnd = 2;
  // err info when error happened
  string ErrInfo = 3;
  repeated bytes MsgBytes = 4;
  // msg format 0:protobuf 1:protobuf json
  int32 MsgFormat = 8;
}

message  QueryReq {
  // history sql
  string QueryName = 1;
  // result column names, need same as proto msg field name
  repeated string ResultColumnNames = 3;
  // sql where condition Fieldname == Value
  map<string, string> Where = 4;
  // custom where protobuf msg bytes
  bytes WhereMsgBytes = 5;
  // limit 0:no limit
  int32 Limit = 6;
  int64 Offset = 7;
  // prefer batch size
  int32 PreferBatchSize = 8;
  // msg format 0:protobuf 1:protobuf json
  int32 MsgFormat = 9;
  // where2 field operator, fieldname op
  map<string, WhereOperator> Where2Operator = 10;
  // where2 field value, fieldname op value
  map<string, string> Where2 = 11;
}

// protodb service
service ProtoDbSrv {
  // crud
  rpc Crud(CrudReq) returns (CrudResp) {};
  // table query
  rpc TableQuery(TableQueryReq) returns (stream QueryResp) {};
  // general query
  rpc Query(QueryReq) returns (stream QueryResp) {};
}