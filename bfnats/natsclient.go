package bfnats

import (
	"fmt"
	"sync"
	"time"

	"bfmap/bfutil"
	"bfmap/config"

	"github.com/nats-io/nats.go"
	"google.golang.org/protobuf/proto"
)

const (
	NatsClientIDHeader = "Nats-Client-Id"
)

// connect to nats server with nats://localhost:<config.NatsServerPort>
var defaultNatsClient *nats.Conn
var defaultNatsClientMutex = sync.Mutex{}

// identifier for this client
var clientId = bfutil.UuidV7()

func GetClientID() string {
	return clientId
}

// scheme: "nats" or "ws"
// port: nats server port
func ConnectLocalNatsServer(scheme string, port int, authToken string) (*nats.Conn, error) {
	natsURL := fmt.Sprintf("%s://localhost:%d", scheme, port)
	if authToken != "" {
		natsURL = fmt.Sprintf("%s://%s@localhost:%d", scheme, authToken, port)
	}
	c, err := nats.Connect(
		natsURL,
		nats.Token(authToken),
		nats.InProcessServer(natServer),
		nats.MaxReconnects(-1),
	)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// GetDefaultNatsClient returns the default nats client using port in setting.ini.
func GetDefaultNatsClient() (*nats.Conn, error) {
	defaultNatsClientMutex.Lock()
	defer defaultNatsClientMutex.Unlock()

	if defaultNatsClient != nil && !defaultNatsClient.IsClosed() {
		return defaultNatsClient, nil
	}

	err := SetupDefaultNatsClient()
	if err != nil {
		return nil, err
	}

	return defaultNatsClient, nil
}

// SetupDefaultNatsClient sets up the default nats client connection.
func SetupDefaultNatsClient() (err error) {
	if defaultNatsClient, err = ConnectLocalNatsServer(
		"nats",
		config.NatsServerPort,
		config.NatsAuthToken,
	); err != nil {
		return err
	}
	return nil
}

func CloseDefaultNatsClient() {
	defaultNatsClientMutex.Lock()
	defer defaultNatsClientMutex.Unlock()
	if defaultNatsClient != nil && !defaultNatsClient.IsClosed() {
		defaultNatsClient.Close()
		defaultNatsClient = nil
	}
}

// NatsPublish publishes data to the subject with the default nats client.
func NatsPublish(subject string, data []byte) error {
	nc, err := GetDefaultNatsClient()
	if err != nil {
		return err
	}
	msg := nats.NewMsg(subject)
	msg.Header.Set(NatsClientIDHeader, clientId)
	msg.Data = data
	return nc.PublishMsg(msg)
}

// NatPublishProto publishes proto message to the subject with the default nats client.
func NatPublishProto(subject string, protoMsg proto.Message) error {
	data, err := proto.Marshal(protoMsg)
	if err != nil {
		return err
	}
	return NatsPublish(subject, data)
}

// NatsSubscribe subscribes to the subject with the default nats client.
func NatsSubscribe(subject string, handler nats.MsgHandler) (*nats.Subscription, error) {
	nc, err := GetDefaultNatsClient()
	if err != nil {
		return nil, err
	}

	return nc.Subscribe(subject, handler)
}

// NatsSubscribeProto subscribes to the subject with the default nats client.
func NatsSubscribeProto(subject string, protoMsg proto.Message, protoHandler func(proto.Message)) error {
	handler := func(msg *nats.Msg) {
		_ = proto.Unmarshal(msg.Data, protoMsg)
		protoHandler(protoMsg)
	}
	_, err := NatsSubscribe(subject, handler)
	return err
}

func NatsRequest(subject string, data []byte, timeout time.Duration) (*nats.Msg, error) {
	nc, err := GetDefaultNatsClient()
	if err != nil {
		return nil, err
	}

	msg := nats.NewMsg(subject)
	msg.Header.Set(NatsClientIDHeader, clientId)
	msg.Data = data
	return nc.RequestMsg(msg, timeout)
}

func NatsRequestProto(subject string, protoMsg proto.Message, timeout time.Duration) (proto.Message, error) {
	data, err := proto.Marshal(protoMsg)
	if err != nil {
		return nil, err
	}

	msg, err := NatsRequest(subject, data, timeout)
	if err != nil {
		return nil, err
	}

	err = proto.Unmarshal(msg.Data, protoMsg)
	return protoMsg, err
}
