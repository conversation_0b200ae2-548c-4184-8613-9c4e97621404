package bfnats

import (
	"fmt"
	"log/slog"

	"github.com/nats-io/nats-server/v2/server"
)

var natServer *server.Server

// StartNatsServer starts the embedded NATS Server. it will not block.
func StartNatsServer(
	natsPort, maxPayload int,
	authToken string,
	LeafNodeOpts server.LeafNodeOpts) (err error) {
	opts := &server.Options{
		Host:          "0.0.0.0",
		Port:          natsPort,
		Authorization: authToken,
		LeafNode:      LeafNodeOpts,
		MaxPayload:    int32(maxPayload),
		// do not deal with os.Signal in nats server, we will handle it in main process
		NoSigs: true,
	}
	natServer, err = server.NewServer(opts)
	if err != nil {
		return fmt.Errorf("create embedded NATS Server: %w", err)
	}

	natServer.Start()
	if LeafNodeOpts.Remotes != nil {
		slog.Info("embedded NATS Server started as leaf node",
			"natsPort", natsPort,
			"leafNodeUser", LeafNodeOpts.Remotes[0].URLs[0].User,
			"leafNodeHost", LeafNodeOpts.Remotes[0].URLs[0].Host)
	} else {
		slog.Info("embedded NATS Server started",
			"natsPort", natsPort,
			"leafNodePort", LeafNodeOpts.Port,
		)
	}

	return nil
}

func StopNatsServer() {
	if natServer != nil {
		natServer.Shutdown()
	}
	slog.Info("embedded NATS Server stopped")
}
