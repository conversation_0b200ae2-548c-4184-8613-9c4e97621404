# 如何获取 Google Maps API Token

Google Maps API Token（API 密钥）用于访问 Google 地图相关的服务。以下是获取 Google Maps API Token 的详细步骤：

> **注意：** 使用美国节点代理。

## 注册 Google 账号

如果你还没有 Google 账号，请先前往 [Google 账号注册页面](https://accounts.google.com/signup) 注册。
![Google账号页面](images/获取GoogleMapAPIToken指南/google账号注册.png)

## 启用结算功能（添加信用卡）

Google Maps API 需要启用结算功能，即绑定有效的信用卡或借记卡，否则无法启用相关 API。如果已经绑定结算账户，请跳过此步骤。

1. 登录 Google Cloud 控制台中的 [管理结算账号](https://console.cloud.google.com/billing)页面。
2. 点击“创建账号”按钮，添加结算账户。
![创建结算账户按钮](images/获取GoogleMapAPIToken指南/创建结算账户按钮.png)
3. 填写名称，选择国家/地区，一般选择美国即可。
![创建结算账户](images/获取GoogleMapAPIToken指南/创建结算账户.png)
4. 设置付款资料，使用[美国地址生成器](https://www.meiguodizhi.com/)生成的地址。
![设置付款资料](images/获取GoogleMapAPIToken指南/设置付款资料.png)
5. 设置付款方式，选择“信用卡或借记卡”，并输入相关信息。注意，**不支持银联，推荐使用Visa或MasterCard**。
![设置付款方式](images/获取GoogleMapAPIToken指南/设置付款方式.png)
6. 点击“提交并启用结算功能”按钮，完成结算账户的创建。

> **注意：** Google 会为新用户提供一定额度的免费试用金。绑定信用卡不会立即扣款，只有超出免费额度后才会计费。

## 创建一个新项目

1. 点击 [创建新项目链接](https://console.cloud.google.com/projectcreate)，在 Google Cloud 控制台中创建新的 Google Cloud 项目。
2. 输入项目名称并选择组织（可选），点击“创建”按钮。
![创建新项目](images/获取GoogleMapAPIToken指南/创建新项目.png)
3.为项目选择结算账号。

## 启用 Google Maps Static API 服务

打开 [Google Maps Static API 启用页面](https://console.cloud.google.com/apis/library/static-maps-backend.googleapis.com)，选择刚才创建的项目，点击“启用”按钮。
![启用Google Maps Static API](<images/获取GoogleMapAPIToken指南/启用Google Maps Static API.png>)

## 创建 API 密钥

1. 打开 [密钥和凭据页面](https://console.cloud.google.com/projectselector2/google/maps-apis/credentials)，选择刚才创建的项目。
![选择gcp项目](images/获取GoogleMapAPIToken指南/选择gcp项目.png)
2. 点击“创建凭据”按钮，选择“API 密钥”。
![创建API密钥](images/获取GoogleMapAPIToken指南/创建API密钥.png)
3. 系统会生成一个新的 API 密钥（即 Token）,并显示在页面上。
![API密钥](images/获取GoogleMapAPIToken指南/API密钥.png)

## 设置 API 密钥的安全性（可选）

- 点击刚刚生成的 API 密钥的“操作”按钮，选择“修改API密钥”。
![修改API密钥](images/获取GoogleMapAPIToken指南/修改API密钥.png)
- 在此页面可以设置 API 密钥的安全性，限制密钥的使用范围，以及密钥可调用的API。
![限制API密钥](images/获取GoogleMapAPIToken指南/限制API密钥.png)
