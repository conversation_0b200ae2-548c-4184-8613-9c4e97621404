# 本地目录提供商语言支持功能需求文档

## 功能概述

基于现有的本地目录fallback机制，为本地目录提供商（Local Directory Provider）添加语言支持功能。该功能将在QueryTileFromLocalDirectory方法中实现语言参数匹配逻辑，根据DbMapProviderToken.language字段值与请求语言参数进行匹配，确保返回正确语言版本的地图瓦片。

## 背景分析

### 现有基础设施

根据`proto/db.proto`中的定义，系统已具备以下基础设施：

1. **DbMapProviderToken.language字段**：
   - 字段类型：`string`
   - 默认值：空字符串
   - 用途：存储本地目录中瓦片的语言信息

2. **本地目录Provider类型**：
   - `ProviderGoogleLocalDirectory` (3) - Google地图本地文件夹
   - `ProviderTiandituLocalDirectory` (4) - 天地图本地文件夹
   - `ProviderOSMLocalDirectory` (5) - OSM本地文件夹

3. **现有TODO注释位置**：
   - `handleWithCacheOrOSMMapTile`方法：第205行 "//todo search from local provider"
   - `natsHandlerForMapReq`方法：需要扩展本地目录查询功能

### 语言处理现状

当前系统在`parseMapReq`方法中已实现基础语言处理：

- 使用`golang.org/x/text/language`库进行语言解析
- 支持BCP 47格式的语言标签
- 针对不同provider有特定的语言规则

## 核心技术要求

### 1. 语言匹配机制核心要求

#### 1.1 语言代码标准化处理

**实现位置**：QueryTileFromLocalDirectory方法内部

**技术规范**：

```go
// 使用golang.org/x/text/language库进行语言代码标准化
func normalizeLanguageCode(lang string) (string, error) {
    if lang == "" {
        return "", nil
    }

    // 解析语言标签
    tag, err := language.Parse(lang)
    if err != nil {
        return "", fmt.Errorf("invalid language code: %w", err)
    }

    // 标准化为BCP 47格式
    return tag.String(), nil
}
```

**标准化规则**：

- 输入格式：`zh-CN`、`zh`、`zh_CN`
- 输出格式：统一为BCP 47标准格式（如`zh-CN`）
- 错误处理：无效语言代码时返回错误，调用方决定降级策略

#### 1.2 语言匹配逻辑

**匹配策略**：

1. **精确匹配**：请求语言与token.language完全相同
2. **语言族匹配**：当精确匹配失败时，尝试语言族匹配（如`zh-CN`匹配`zh`）
3. **空语言处理**：token.language为空时的特殊处理逻辑

**实现伪代码**：

```go
func matchLanguage(requestLang, tokenLang string) bool {
    // 标准化语言代码
    reqLang, _ := normalizeLanguageCode(requestLang)
    tokLang, _ := normalizeLanguageCode(tokenLang)

    // 精确匹配
    if reqLang == tokLang {
        return true
    }

    // 语言族匹配
    reqBase, _ := language.Parse(reqLang)
    tokBase, _ := language.Parse(tokLang)

    return reqBase.Parent() == tokBase.Parent()
}
```

### 2. 方法级别的精确实现范围

#### 2.1 QueryTileFromLocalDirectory方法核心实现

**方法签名**：

```go
func QueryTileFromLocalDirectory(
    userRid string,
    mapReq *MapReq,
    localProviderType int,
    needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error)
```

**语言匹配集成点**：

```go
// 在获取provider tokens后，添加语言匹配过滤
providerTokens, err := GlobalMapsManager.GetMapProviderTokens(userRid, localProviderType)
if err != nil {
    return nil, nil, err
}

// 过滤出语言兼容的tokens
var compatibleTokens []*MapProviderToken
for _, token := range providerTokens {
    if isLanguageCompatible(mapReq.Lang, token.Language, token.Provider, mapReq.MapType) {
        compatibleTokens = append(compatibleTokens, token)
    }
}

if len(compatibleTokens) == 0 {
    return nil, nil, errors.New("no language-compatible local directory provider token found")
}
```

**Provider类型映射辅助函数**：

```go
// 将在线provider类型映射为对应的本地目录provider类型
func getLocalDirectoryProviderType(onlineProviderType int) int {
    switch onlineProviderType {
    case int(dbproto.MapProviderEnum_ProviderGoogle):
        return int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory)
    case int(dbproto.MapProviderEnum_ProviderTianditu):
        return int(dbproto.MapProviderEnum_ProviderTiandituLocalDirectory)
    case int(dbproto.MapProviderEnum_ProviderOSM):
        return int(dbproto.MapProviderEnum_ProviderOSMLocalDirectory)
    default:
        return -1 // 不支持的provider类型
    }
}
```

#### 2.2 resolveTempMapProjectToken方法

**实现要求**：**明确排除**本地目录provider查询功能

**技术规范**：

- 该方法专门处理临时项目token，不应包含本地目录fallback逻辑
- 保持现有实现不变，确保功能边界清晰

#### 2.2 handleWithCacheOrOSMMapTile方法

**实现要求**：仅在`sysexpired=1`条件下启用本地目录provider查询

**触发条件**：

```go
// 仅当sysexpired=1时才启用本地目录fallback
if r.URL.Query().Get("sysexpired") == "1" {
    // 在QueryTileFromKeyValueDb失败后，尝试本地目录fallback
    if err != nil {
        tileInfo, imageBytes, err = QueryTileFromLocalDirectory(
            "", // userRid为空，因为是临时token
            mapReq,
            getLocalDirectoryProviderType(mapReq.Provider),
            false,
        )
        if err == nil {
            return // 成功获取本地瓦片
        }
    }
}
```

**实现位置**：第205行TODO注释处

#### 2.3 natsHandlerForMapReq方法

**实现要求**：完整实现本地目录provider查询功能，作为NATS请求前的fallback机制

**实现逻辑**：

```go
func natsHandlerForMapReq(msg *nats.Msg) {
    // ... 现有代码 ...

    _, imageBytes, err := QueryTileFromKeyValueDb(mapReq)
    if err != nil {
        // 尝试本地目录fallback
        _, imageBytes, err = QueryTileFromLocalDirectory(
            "", // NATS处理中userRid未知
            mapReq,
            getLocalDirectoryProviderType(mapReq.Provider),
            false,
        )
        if err != nil {
            // 本地目录也失败，记录日志并返回
            if config.IsVerboseDebugMap {
                slog.Debug("natsHandlerForMapReq local directory fallback fail",
                    "err", err, "mapReq", mapReq.Key())
            }
            return
        }
    }

    // ... 响应逻辑 ...
}
```

### 3. 提供商特定的语言支持规则

#### 3.1 Google地图（ProviderGoogleLocalDirectory）

**语言支持**：支持多语言匹配

**匹配规则**：

- 需要严格的language字段匹配
- 支持所有BCP 47标准语言代码
- 卫星图层跳过语言检查
- 路线图/混合图层执行语言匹配

**实现逻辑**：

```go
case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
    if mapReq.MapType == MapTypeSatellite {
        // 卫星图跳过语言检查
        return true
    }
    return matchLanguage(mapReq.Lang, token.Language)
```

#### 3.2 天地图（ProviderTiandituLocalDirectory）

**语言支持**：仅支持zh-CN

**特殊处理规则**：

- 中文变体标准化：`zh-CN`、`zh`、`zh_CN`统一为`zh-CN`
- 非中文语言请求时跳过该provider
- 卫星图层跳过语言检查

**实现逻辑**：

```go
case dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
    if mapReq.MapType == MapTypeSatellite {
        return true
    }

    // 标准化中文语言代码
    normalizedReqLang := normalizeChinese(mapReq.Lang)
    normalizedTokenLang := normalizeChinese(token.Language)

    return normalizedReqLang == "zh-CN" && normalizedTokenLang == "zh-CN"

func normalizeChinese(lang string) string {
    switch lang {
    case "zh", "zh_CN", "zh-CN":
        return "zh-CN"
    default:
        return lang
    }
}
```

#### 3.3 OSM（ProviderOSMLocalDirectory）

**语言支持**：跳过语言匹配检查

**实现原理**：

- OSM的语言由地理位置自动决定
- 不需要进行语言参数匹配
- 所有语言请求都可以使用同一套OSM瓦片

**实现逻辑**：

```go
case dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
    // OSM跳过语言匹配，语言由地理位置决定
    return true
```

### 4. 执行优先级和触发条件

#### 4.1 触发时机

**主要触发条件**：

1. `QueryTileFromKeyValueDb`返回错误（瓦片不存在于数据库）
2. `QueryTileFromKeyValueDb`返回的瓦片需要更新（基于缓存时间和Status判断）

**执行顺序**：

```text
1. 缓存查询 (QueryTileFromKeyValueDb)
   ↓ (失败或需要更新)
2. 本地目录Fallback (QueryTileFromLocalDirectory) ← 新增语言匹配
   ↓ (失败)
3. NATS请求 (如果启用)
   ↓ (失败)
4. 在线Provider请求 (QueryTileFromProvider)
```

#### 4.2 向后兼容性

**空Language字段处理**：

- 现有DbMapProviderToken记录的Language字段可能为空
- 空Language字段应被视为"通用语言"，可匹配任何请求
- 确保现有配置不受影响

**实现逻辑**：

```go
func isLanguageCompatible(requestLang, tokenLang string, providerType dbproto.MapProviderEnum, mapType string) bool {
    // 向后兼容：空token语言匹配所有请求
    if tokenLang == "" {
        return true
    }

    // 卫星图跳过语言检查
    if mapType == MapTypeSatellite {
        return true
    }

    // 根据provider类型执行特定匹配逻辑
    switch providerType {
    case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
        return matchLanguage(requestLang, tokenLang)
    case dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
        return matchTiandituLanguage(requestLang, tokenLang)
    case dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
        return true
    default:
        return false
    }
}
```

### 5. 错误处理和日志记录

#### 5.1 语言代码解析失败

**降级策略**：

- 当语言代码解析失败时，记录警告日志
- 继续尝试其他可用的provider token
- 不中断整个请求流程

**实现示例**：

```go
normalizedLang, err := normalizeLanguageCode(mapReq.Lang)
if err != nil {
    slog.Warn("failed to normalize language code, skip language matching",
        "requestLang", mapReq.Lang,
        "tokenRid", token.TokenRid,
        "err", err)
    // 降级为不匹配，继续尝试其他token
    continue
}
```

#### 5.2 本地文件不存在

**处理策略**：

- 记录调试级别日志，不视为错误
- 继续fallback到NATS或在线provider
- 避免日志污染

**实现示例**：

```go
if os.IsNotExist(err) {
    if config.IsVerboseDebugMap {
        slog.Debug("local directory file not found",
            "filePath", filePath,
            "mapReq", mapReq.Key(),
            "tokenRid", token.TokenRid)
    }
    return nil, nil, err
}
```

#### 5.3 详细调试日志

**日志内容**：

- 语言匹配过程的详细信息
- Provider选择逻辑
- 文件路径构建过程
- 匹配成功/失败的原因

**实现示例**：

```go
if config.IsVerboseDebugMap {
    slog.Debug("local directory language matching",
        "requestLang", mapReq.Lang,
        "tokenLang", token.Language,
        "providerType", token.Provider,
        "mapType", mapReq.MapType,
        "matched", matched,
        "tokenRid", token.TokenRid)
}
```

## 实现计划

### 阶段1：核心语言匹配功能（1-2天）

1. **语言标准化函数实现**
   - 实现`normalizeLanguageCode`函数
   - 实现`normalizeChinese`函数
   - 单元测试覆盖

2. **语言匹配逻辑实现**
   - 实现`matchLanguage`函数
   - 实现`isLanguageCompatible`函数
   - 针对不同provider的特定匹配逻辑

### 阶段2：方法集成（2-3天）

1. **QueryTileFromLocalDirectory方法扩展**
   - 添加语言匹配逻辑到token筛选过程
   - 实现向后兼容性处理

2. **handleWithCacheOrOSMMapTile方法修改**
   - 在TODO注释处添加本地目录fallback
   - 确保仅在sysexpired=1时启用

3. **natsHandlerForMapReq方法扩展**
   - 添加完整的本地目录fallback逻辑

### 阶段3：测试和优化（2-3天）

1. **单元测试**
   - 语言匹配函数测试
   - 不同provider的语言规则测试
   - 边界条件和错误处理测试

2. **集成测试**
   - 端到端语言匹配流程测试
   - 多语言环境下的fallback机制测试

3. **性能优化**
   - 语言匹配过程的性能分析
   - 缓存机制优化（如需要）

## 测试计划

### 1. 单元测试用例

#### 1.1 语言标准化测试

```go
func TestNormalizeLanguageCode(t *testing.T) {
    tests := []struct {
        input    string
        expected string
        hasError bool
    }{
        {"zh-CN", "zh-CN", false},
        {"zh", "zh", false},
        {"zh_CN", "zh-CN", false},
        {"en-US", "en-US", false},
        {"invalid", "", true},
    }
    // ... 测试实现
}
```

#### 1.2 语言匹配测试

```go
func TestLanguageMatching(t *testing.T) {
    tests := []struct {
        requestLang string
        tokenLang   string
        provider    dbproto.MapProviderEnum
        mapType     string
        expected    bool
    }{
        {"zh-CN", "zh-CN", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, "roadmap", true},
        {"zh", "zh-CN", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, "roadmap", true},
        {"en", "", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, "roadmap", true}, // 向后兼容
        {"zh-CN", "en", dbproto.MapProviderEnum_ProviderOSMLocalDirectory, "roadmap", true}, // OSM跳过语言检查
    }
    // ... 测试实现
}
```

### 2. 集成测试场景

#### 2.1 多语言Provider配置测试

- 配置多个相同provider但不同语言的token
- 验证请求能够匹配到正确语言的token
- 验证fallback机制的正确性

#### 2.2 边界条件测试

- 空语言字段的向后兼容性
- 无效语言代码的处理
- 本地文件不存在时的fallback

### 3. 性能测试

#### 3.1 语言匹配性能

- 大量并发请求下的语言匹配性能
- 不同语言代码复杂度的处理时间

#### 3.2 内存使用分析

- 语言标准化过程的内存分配
- 长时间运行下的内存泄漏检查

## 风险评估

### 技术风险

1. **语言代码标准化复杂性**
   - 风险：BCP 47标准的复杂性可能导致边界情况处理不当
   - 缓解：使用成熟的golang.org/x/text/language库，充分的单元测试

2. **向后兼容性问题**
   - 风险：现有空Language字段的token可能无法正常工作
   - 缓解：实现明确的向后兼容逻辑，空字段匹配所有请求

3. **性能影响**
   - 风险：语言匹配逻辑可能增加请求处理时间
   - 缓解：优化匹配算法，考虑添加缓存机制

### 业务风险

1. **语言匹配错误**
   - 风险：错误的语言匹配可能导致返回错误语言的地图
   - 缓解：详细的测试覆盖，渐进式部署

2. **配置复杂性增加**
   - 风险：语言支持增加了token配置的复杂性
   - 缓解：提供清晰的配置文档和示例

## 关键实现注意事项

### 1. ReqMapFromProviderWithToken方法扩展

**当前状态**：该方法目前只处理在线provider类型（Google、Tianditu、OSM）

**需要扩展**：添加对本地目录provider类型的支持

```go
func ReqMapFromProviderWithToken(
    providerToken *MapProviderToken,
    mapReq *MapReq,
    needCreateNewIndex bool,
) (*TileInfo, []byte, error) {
    // ... 现有代码 ...

    switch providerToken.Provider {
    case dbproto.MapProviderEnum_ProviderGoogle:
        // ... 现有Google逻辑 ...
    case dbproto.MapProviderEnum_ProviderTianditu:
        // ... 现有Tianditu逻辑 ...
    case dbproto.MapProviderEnum_ProviderOSM:
        // ... 现有OSM逻辑 ...

    // 新增本地目录provider支持
    case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
         dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
         dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
        imageBytes, err = readLocalDirectoryFile(providerToken, mapReq)

    default:
        return nil, nil, errors.New("not support provider")
    }
    // ... 其余逻辑 ...
}
```

### 2. 本地文件读取实现

**文件路径构建**：

```go
func buildLocalFilePath(token *MapProviderToken, mapReq *MapReq) string {
    baseUrl := strings.TrimSuffix(token.BaseUrl, "/")
    ext := "png"
    if mapReq.MapType == MapTypeSatellite || mapReq.MapType == MapTypeHybrid {
        ext = "jpg"
    }

    return fmt.Sprintf("%s/%s/%d/%d/%d.%s",
        baseUrl, mapReq.MapType, mapReq.Z, mapReq.X, mapReq.Y, ext)
}
```

## 相关文档

- [本地文件夹Fallback机制需求文档](./REQUIREMENTS_local_directory_fallback.md) - 本功能的基础架构文档

## 总结

本需求文档详细定义了本地目录提供商语言支持功能的技术规范。该功能将显著提升系统的国际化支持能力，确保用户能够获取到正确语言版本的地图瓦片。通过严格的语言匹配逻辑、完善的错误处理和向后兼容性保证，该功能将无缝集成到现有的本地目录fallback机制中。

实现过程中需要特别注意不同地图提供商的语言支持特性，确保匹配逻辑的准确性和系统的稳定性。通过分阶段的实现计划和全面的测试策略，可以确保功能的质量和可靠性。

**核心价值**：

1. **国际化支持**：为多语言用户提供准确的地图服务
2. **精确匹配**：基于BCP 47标准的语言代码匹配
3. **向后兼容**：确保现有配置无需修改即可正常工作
4. **性能优化**：本地文件访问比网络请求更快
5. **可维护性**：清晰的模块化设计和完善的错误处理
