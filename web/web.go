package web

import (
	"bfmap/maps"
	"bfmap/rpc"
	"crypto/tls"
	"fmt"
	"log/slog"
	"net/http"
	"sync/atomic"
	"time"

	"bfmap/bfutil"
	"bfmap/config"

	"net/http/pprof"

	"connectrpc.com/connect"
	connectcors "connectrpc.com/cors"
	"github.com/rs/cors"
	"github.com/ygrpc/protodb/service"
)

var IsServingTls = atomic.Bool{}
var httpMux http.Handler

func WebServe(port int, webDir string) {
	mux := http.NewServeMux()

	if config.IsVerboseDebug {
		mux.HandleFunc("/debug/pprof/", pprof.Index)
		mux.HandleFunc("/debug/pprof/cmdline", pprof.Cmdline)
		mux.HandleFunc("/debug/pprof/profile", pprof.Profile)
		mux.HandleFunc("/debug/pprof/symbol", pprof.Symbol)
		mux.HandleFunc("/debug/pprof/trace", pprof.Trace)
	}

	rpcPath, rpcHandler := rpc.NewBfmapHandler(
		&rpc.RpcImpl{},
		connect.WithInterceptors(rpc.NewLogInterceptor()),
	)
	mux.Handle(rpcPath, rpcHandler)

	dbRpcPath, dbRpcHandler := rpc.CreateDbRpcHandler(
		connect.WithInterceptors(rpc.NewDbrpcInterceptor(), rpc.NewLogInterceptor()),
	)
	mux.Handle(dbRpcPath, dbRpcHandler)

	mux.HandleFunc("/map", maps.MapHttpHandler)
	mux.HandleFunc("/sslupdate", sslUpdate)
	mux.Handle("/", http.FileServer(http.Dir(webDir)))

	httpMux = mux
	if config.IsVerboseDebug {
		httpMux = WithCors(mux)
	}

	WebServeTls(config.HttpsPort)

	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", port),
		Handler:      httpMux,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	err := srv.ListenAndServe()
	if err != nil {
		fmt.Println("WebServe http listen error:", err)
	}
}

func WebServeTls(port int) {
	if port <= 0 ||
		!bfutil.IsFileExist(config.SSLKeyFilePath) ||
		!bfutil.IsFileExist(config.SSLCertFilePath) {
		IsServingTls.Store(false)
		return
	}

	tlsConfig := &tls.Config{
		GetCertificate: sslCert.GetCertificate,
		MinVersion:     tls.VersionTLS12,
	}

	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", port),
		Handler:      httpMux,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
		TLSConfig:    tlsConfig,
	}

	if sslCert.cert.Load() == nil {
		err := sslCert.loadCert(config.SSLCertFilePath, config.SSLKeyFilePath)
		if err != nil {
			slog.Error("https server failed!", "error", err)
			IsServingTls.Store(false)
			return
		}
	}

	go func() {
		IsServingTls.Store(true)
		slog.Info("WebServe https listen start", "port", port)
		err := srv.ListenAndServeTLS("", "") // Empty strings because we are using GetCertificate
		if err != nil {
			slog.Error("WebServe https listen end", "error", err)
			IsServingTls.Store(false)
		}
		IsServingTls.Store(false)
	}()
}

func WithCors(h http.Handler) http.Handler {
	return cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: append(connectcors.AllowedMethods(), "OPTIONS"),
		AllowedHeaders: append(
			connectcors.AllowedHeaders(),
			"Session-Id",
			"If-None-Match",
			"If-Modified-Since",
			service.YgrpcErrHeader,
			service.YgrpcErrMax,
		),
		ExposedHeaders: append(
			connectcors.ExposedHeaders(),
			maps.MapProviderHeaderName,
			maps.MapCacheTimeHeaderName,
			"Cache-Control",
			"ETag",
			"Last-Modified",
			service.YgrpcErr,
		),
	}).Handler(h)
}
