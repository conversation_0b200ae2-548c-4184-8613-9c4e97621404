package web

import (
	"bfmap/bfutil"
	"bfmap/config"
	"crypto/tls"
	"errors"
	"io"
	"log"
	"log/slog"
	"net/http"
	"os"
	"path/filepath"
	"sync/atomic"

	"github.com/fsnotify/fsnotify"
)

type atomicCert struct {
	fsWatcher *fsnotify.Watcher
	cert      atomic.Pointer[tls.Certificate]
}

var sslCert atomicCert

func (ac *atomicCert) CreateFsWatcher() (err error) {
	ac.fsWatcher, err = fsnotify.NewWatcher()
	if err != nil {
		return
	}
	go ac.watchLoop()
	return
}
func (ac *atomicCert) WatchFolder(name string) (err error) {
	if ac.fsWatcher == nil {
		err = ac.CreateFsWatcher()
		if err != nil {
			return
		}
	}
	return ac.fsWatcher.Add(name)
}

func (ac *atomicCert) watchLoop() (err error) {
	for {
		select {
		case ev := <-ac.fsWatcher.Events:
			if ev.Op.Has(fsnotify.Write) || ev.Op.Has(fsnotify.Create) {
				ac.onSSLFileUpdate(ev)
			}
		case err := <-ac.fsWatcher.Errors:
			slog.Info("watch ssl file fail", "error", err)
		}
	}
}

func (ac *atomicCert) onSSLFileUpdate(event fsnotify.Event) {
	if event.Name != config.SSLKeyFilePath && event.Name != config.SSLCertFilePath {
		return
	}

	err := sslCert.loadCert(config.SSLCertFilePath, config.SSLKeyFilePath)
	if err != nil {
		return
	}

	if !IsServingTls.Load() {
		WebServeTls(config.HttpsPort)
	}
	slog.Info("onSSLFileUpdate load cert success")
}

func (ac *atomicCert) GetCertificate(clientHello *tls.ClientHelloInfo) (*tls.Certificate, error) {
	return ac.cert.Load(), nil
}
func (ac *atomicCert) loadCert(certFile, keyFile string) error {
	cert, err := tls.LoadX509KeyPair(certFile, keyFile)
	if err != nil {
		log.Printf("Error loading certificate: %v", err)
		return err
	}

	ac.cert.Store(&cert)
	return nil
}
func testCert(certFileContent, keyFileContent []byte) error {
	_, err := tls.X509KeyPair(certFileContent, keyFileContent)
	return err
}

func sslUpdate(writer http.ResponseWriter, request *http.Request) {
	if request.Method == http.MethodOptions {
		writer.WriteHeader(http.StatusOK)
		return
	}

	if request.Method != http.MethodPost {
		http.Error(writer, "method not allowed", http.StatusMethodNotAllowed)
		return
	}

	err := request.ParseMultipartForm(10 << 20) // 10 MB max memory
	if err != nil {
		http.Error(writer, err.Error(), http.StatusInternalServerError)
		return
	}

	//check token
	token := request.Form.Get("token")
	if len(token) == 0 || token != config.SSLUpdateToken {
		http.Error(writer, "token error", http.StatusUnauthorized)
		return
	}

	//get file crt
	fileCrt, _, err := request.FormFile("crt")

	if err != nil {
		http.Error(writer, "no crt file in form:"+err.Error(), http.StatusInternalServerError)
		return
	}

	defer fileCrt.Close()

	fileKey, _, err := request.FormFile("key")
	if err != nil {
		http.Error(writer, "no key file in form:"+err.Error(), http.StatusInternalServerError)
		return
	}

	defer fileKey.Close()

	crtFileContent, err := io.ReadAll(fileCrt)
	if err != nil {
		http.Error(writer, "read crt file err:"+err.Error(), http.StatusInternalServerError)
		return
	}

	keyFileContent, err := io.ReadAll(fileKey)
	if err != nil {
		http.Error(writer, "read key file err:"+err.Error(), http.StatusInternalServerError)
		return
	}

	err = testCert(crtFileContent, keyFileContent)
	if err != nil {
		http.Error(writer, "test cert err:"+err.Error(), http.StatusInternalServerError)
		return
	}

	if len(config.SSLCertFilePath) == 0 {
		http.Error(writer, "no cert file path", http.StatusInternalServerError)
		return
	}

	if len(config.SSLKeyFilePath) == 0 {
		http.Error(writer, "no key file path", http.StatusInternalServerError)
		return
	}

	// backup first
	_ = backupFileToDir(config.SSLCertFilePath, filepath.Join(filepath.Dir(config.SSLCertFilePath), "backup"))
	_ = backupFileToDir(config.SSLKeyFilePath, filepath.Join(filepath.Dir(config.SSLCertFilePath), "backup"))

	//save crt file to config.SSLCertFilePath
	errCrt := checkPathAndWriteFile(config.SSLCertFilePath, crtFileContent, 0666)
	if errCrt != nil {
		http.Error(writer, "save cert err:"+errCrt.Error(), http.StatusInternalServerError)
		return
	}

	//save key file to config.SSLKeyFilePath
	errKey := checkPathAndWriteFile(config.SSLKeyFilePath, keyFileContent, 0666)
	if errKey != nil {
		http.Error(writer, "save key err:"+errKey.Error(), http.StatusInternalServerError)
		return
	}

	writer.WriteHeader(http.StatusOK)
	// 不需要调用 loadCert,写入文件会触发 onSSLFileUpdate
}

// backupFileToDir move file into dir,not change anything
//
// usage: backupFileToDir("./folder/test.file", "./folder/backup")
func backupFileToDir(name, dir string) error {
	if !bfutil.IsFileExist(name) {
		return errors.New("file not exist")
	}

	err := os.MkdirAll(dir, 0755)
	if err != nil {
		return err
	}

	err = os.Rename(name, filepath.Join(dir, filepath.Base(name)))
	if err != nil {
		return err
	}

	return nil
}

func checkPathAndWriteFile(name string, data []byte, perm os.FileMode) error {
	err := os.MkdirAll(filepath.Dir(name), 0755)
	if err != nil {
		return err
	}

	err = os.WriteFile(name, data, perm)
	if err != nil {
		return err
	}
	return nil
}
